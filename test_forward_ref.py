#!/usr/bin/env python3
"""
Test forward references fix
"""

import sys
import os

# Thêm đường dẫn
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'chatbot-service'))

print("=== TESTING FORWARD REFERENCES FIX ===")

# Test 1: Import lottery schemas
try:
    print("1. Testing lottery schema import...")
    from app.schemas.lottery import LotteryTypeSchema, LotteryResultResponseSchema
    print("✅ Lottery schema import OK")
except Exception as e:
    print(f"❌ Lottery schema import failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 2: Test schema instantiation
try:
    print("2. Testing schema instantiation...")
    
    lottery_type = LotteryTypeSchema(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Xổ số miền Bắc"
    )
    
    print(f"✅ Schema instantiation OK: {lottery_type.name}")
except Exception as e:
    print(f"❌ Schema instantiation failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 3: Test service import
try:
    print("3. Testing service import...")
    from app.services.lottery import LotteryService
    print("✅ Service import OK")
except Exception as e:
    print(f"❌ Service import failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 All forward reference tests passed!")
print("✅ RecursionError should be fixed!")
