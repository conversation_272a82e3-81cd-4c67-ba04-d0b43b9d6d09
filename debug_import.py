#!/usr/bin/env python3
"""
Debug script để tìm nguyên nhân RecursionError
"""

import sys
import os

# Thêm đường dẫn
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'chatbot-service'))

print("=== DEBUG IMPORT STEP BY STEP ===")

# Step 1: Test basic Python imports
try:
    print("1. Testing basic Python imports...")
    from typing import Dict, Any, List, Optional
    from datetime import date, datetime
    print("✅ Basic Python imports OK")
except Exception as e:
    print(f"❌ Basic Python imports failed: {e}")
    exit(1)

# Step 2: Test Pydantic
try:
    print("2. Testing Pydantic...")
    from pydantic import BaseModel, Field
    print("✅ Pydantic OK")
except Exception as e:
    print(f"❌ Pydantic failed: {e}")
    exit(1)

# Step 3: Test SQLAlchemy
try:
    print("3. Testing SQLAlchemy...")
    from sqlalchemy import Column, Integer, String
    from sqlalchemy.ext.asyncio import AsyncSession
    print("✅ SQLAlchemy OK")
except Exception as e:
    print(f"❌ SQLAlchemy failed: {e}")
    exit(1)

# Step 4: Test app.db.base
try:
    print("4. Testing app.db.base...")
    from app.db.base import Base
    print("✅ app.db.base OK")
except Exception as e:
    print(f"❌ app.db.base failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Step 5: Test individual model files (not __init__)
try:
    print("5. Testing individual model files...")
    from app.models.user_query import UserQueryModel
    print("✅ UserQueryModel OK")
except Exception as e:
    print(f"❌ UserQueryModel failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

try:
    print("6. Testing lottery models...")
    from app.models.lottery import LotteryTypeModel, LotteryDrawModel
    print("✅ Lottery models OK")
except Exception as e:
    print(f"❌ Lottery models failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Step 7: Test individual schema files
try:
    print("7. Testing individual schema files...")
    from app.schemas.lottery import LotteryTypeSchema
    print("✅ LotteryTypeSchema OK")
except Exception as e:
    print(f"❌ LotteryTypeSchema failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Step 8: Test services
try:
    print("8. Testing services...")
    from app.services.lottery import LotteryService
    print("✅ LotteryService OK")
except Exception as e:
    print(f"❌ LotteryService failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 All imports successful!")
print("✅ No RecursionError detected!")
