<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XỔ SỐ TV - Giám sát hệ thống</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .service-card {
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 0.9rem;
            padding: 5px 10px;
        }
        .component-status {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 4px;
        }
        .component-status:hover {
            background-color: #f8f9fa;
        }
        .status-up {
            color: #198754;
        }
        .status-down {
            color: #dc3545;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        .last-updated {
            font-size: 0.8rem;
            color: #6c757d;
            text-align: center;
            margin-bottom: 20px;
        }
        .endpoint-table {
            font-size: 0.9rem;
        }
        .system-info {
            font-size: 0.9rem;
        }
        .progress {
            height: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="mb-4">
            <h1 class="text-center">XỔ SỐ TV - Giám sát hệ thống</h1>
            <p class="text-center text-muted">Trạng thái các dịch vụ trong hệ thống</p>
            <div class="last-updated" id="lastUpdated">Cập nhật lần cuối: --</div>
        </header>

        <div class="row" id="servicesContainer">
            <!-- Các service card sẽ được thêm vào đây bằng JavaScript -->
            <div class="col-md-4">
                <div class="card service-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Đang tải...</h5>
                        <span class="badge bg-secondary status-badge">--</span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Đang tải thông tin dịch vụ...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="btn btn-primary refresh-btn" id="refreshBtn">
        <i class="bi bi-arrow-clockwise"></i> Làm mới
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Cấu hình các dịch vụ cần giám sát
        const services = [
            {
                name: 'Chatbot Service',
                url: 'http://localhost:8000/health',
                simpleUrl: 'http://localhost:8000/health/simple'
            },
            {
                name: 'Integration API',
                url: 'http://localhost:9000/health',
                simpleUrl: 'http://localhost:9000/health/simple'
            },
            {
                name: 'LLM Core',
                url: 'http://localhost:8080/health',
                simpleUrl: 'http://localhost:8080/health/simple'
            }
        ];

        // Hàm để tạo card cho mỗi dịch vụ
        function createServiceCard(service, data) {
            const isHealthy = data.status === 'healthy' || data.status === 'ok';
            const statusClass = isHealthy ? 'bg-success' : 'bg-danger';
            const statusText = isHealthy ? 'Hoạt động' : 'Lỗi';
            
            let componentsHtml = '';
            if (data.components) {
                componentsHtml += '<h6 class="mt-3">Các thành phần:</h6>';
                for (const [name, component] of Object.entries(data.components)) {
                    const componentStatusClass = component.status === 'up' ? 'status-up' : 'status-down';
                    componentsHtml += `
                        <div class="component-status">
                            <span>${name}</span>
                            <span class="${componentStatusClass}">${component.status === 'up' ? 'Hoạt động' : 'Lỗi'}</span>
                        </div>
                    `;
                }
            }
            
            let endpointsHtml = '';
            if (data.endpoints && data.endpoints.length > 0) {
                endpointsHtml += `
                    <h6 class="mt-3">Endpoints:</h6>
                    <table class="table table-sm endpoint-table">
                        <thead>
                            <tr>
                                <th>Tên</th>
                                <th>Trạng thái</th>
                                <th>Thời gian</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                for (const endpoint of data.endpoints) {
                    const endpointStatusClass = endpoint.status ? 'text-success' : 'text-danger';
                    endpointsHtml += `
                        <tr>
                            <td>${endpoint.name}</td>
                            <td class="${endpointStatusClass}">${endpoint.status ? 'OK' : 'Lỗi'}</td>
                            <td>${endpoint.response_time || '--'}</td>
                        </tr>
                    `;
                }
                
                endpointsHtml += `
                        </tbody>
                    </table>
                `;
            }
            
            let systemInfoHtml = '';
            if (data.system_info) {
                const sysInfo = data.system_info;
                systemInfoHtml += `
                    <h6 class="mt-3">Thông tin hệ thống:</h6>
                    <div class="system-info">
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>CPU:</span>
                                <span>${sysInfo.cpu?.usage_percent || 0}</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: ${sysInfo.cpu?.usage_percent || 0}"
                                    aria-valuenow="${sysInfo.cpu?.usage_percent || 0}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Bộ nhớ:</span>
                                <span>${sysInfo.memory?.percent || 0}</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: ${sysInfo.memory?.percent || 0}"
                                    aria-valuenow="${sysInfo.memory?.percent || 0}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Đĩa:</span>
                                <span>${sysInfo.disk?.percent || 0}</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: ${sysInfo.disk?.percent || 0}"
                                    aria-valuenow="${sysInfo.disk?.percent || 0}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            return `
                <div class="col-md-4">
                    <div class="card service-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">${service.name}</h5>
                            <span class="badge ${statusClass} status-badge">${statusText}</span>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                <strong>Phiên bản:</strong> ${data.version || 'N/A'}<br>
                                <strong>Môi trường:</strong> ${data.environment || 'N/A'}<br>
                                <strong>Thời gian:</strong> ${data.timestamp || 'N/A'}
                            </p>
                            ${componentsHtml}
                            ${endpointsHtml}
                            ${systemInfoHtml}
                        </div>
                        <div class="card-footer text-end">
                            <a href="${service.url}" target="_blank" class="btn btn-sm btn-outline-primary">Chi tiết</a>
                        </div>
                    </div>
                </div>
            `;
        }

        // Hàm để tạo card lỗi
        function createErrorCard(service, error) {
            return `
                <div class="col-md-4">
                    <div class="card service-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">${service.name}</h5>
                            <span class="badge bg-danger status-badge">Lỗi</span>
                        </div>
                        <div class="card-body">
                            <p class="card-text text-danger">Không thể kết nối đến dịch vụ.</p>
                            <p class="card-text"><small>${error}</small></p>
                        </div>
                        <div class="card-footer text-end">
                            <a href="${service.url}" target="_blank" class="btn btn-sm btn-outline-primary">Thử lại</a>
                        </div>
                    </div>
                </div>
            `;
        }

        // Hàm để cập nhật trạng thái các dịch vụ
        async function updateServices() {
            const servicesContainer = document.getElementById('servicesContainer');
            servicesContainer.innerHTML = '';
            
            for (const service of services) {
                try {
                    const response = await fetch(service.simpleUrl);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    let data;
                    try {
                        data = await response.json();
                        
                        // Nếu là simple health check, thử lấy thêm thông tin chi tiết
                        if (!data.components) {
                            try {
                                const detailResponse = await fetch(service.url);
                                if (detailResponse.ok) {
                                    const detailData = await detailResponse.json();
                                    data = {...data, ...detailData};
                                }
                            } catch (detailError) {
                                console.warn(`Không thể lấy thông tin chi tiết cho ${service.name}:`, detailError);
                            }
                        }
                    } catch (jsonError) {
                        data = {
                            status: response.ok ? 'healthy' : 'unhealthy',
                            message: 'Không thể phân tích dữ liệu JSON'
                        };
                    }
                    
                    servicesContainer.innerHTML += createServiceCard(service, data);
                } catch (error) {
                    console.error(`Lỗi khi kiểm tra ${service.name}:`, error);
                    servicesContainer.innerHTML += createErrorCard(service, error.message);
                }
            }
            
            // Cập nhật thời gian làm mới
            document.getElementById('lastUpdated').textContent = `Cập nhật lần cuối: ${new Date().toLocaleString()}`;
        }

        // Khởi tạo trang
        document.addEventListener('DOMContentLoaded', () => {
            updateServices();
            
            // Thiết lập làm mới tự động mỗi 30 giây
            setInterval(updateServices, 30000);
            
            // Thiết lập nút làm mới
            document.getElementById('refreshBtn').addEventListener('click', updateServices);
        });
    </script>
</body>
</html>
