services:
  timescale:
    image: timescale/timescaledb:latest-pg16
    container_name: timescale
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PG_USER: ${POSTGRES_USER}
      PG_PASSWORD: ${POSTGRES_PASSWORD}
      PG_DATABASE: ${POSTGRES_DB}
      TZ: ${TZ}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - timescale_data:/var/lib/postgresql/data
      - ./database-service/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./database-service/setup_timescaledb.sql:/docker-entrypoint-initdb.d/02-setup_timescaledb.sql
      - ./database-service/storage_management.sql:/docker-entrypoint-initdb.d/03-storage_management.sql
      - ./database-service/postgresql.conf:/etc/postgresql/postgresql.conf
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c shared_preload_libraries=timescaledb
      -c timescaledb.telemetry_level=off
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    image: redis:7
    container_name: redis
    environment:
      TZ: ${TZ}
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  llm-core:
    build: ./llm-core
    container_name: llm-core
    environment:
      LLM_MODEL_PATH: ${LLM_MODEL_PATH}
      LLM_THREADS: ${LLM_THREADS}
      LLM_CTX_SIZE: ${LLM_CTX_SIZE:-4096}
      LLM_N_BATCH: ${LLM_N_BATCH:-512}
      LLM_USE_MLOCK: ${LLM_USE_MLOCK:-false}
      LLM_VERBOSE: ${LLM_VERBOSE:-false}
      LLM_LAZY_LOAD: ${LLM_LAZY_LOAD:-true}
      TZ: ${TZ}
      VERSION: ${VERSION:-1.0.0}
      ENVIRONMENT: ${ENVIRONMENT:-development}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_FILE: /app/logs/llm-core.log
      LOG_REQUEST_BODY: ${LOG_REQUEST_BODY:-False}
      LOG_RESPONSE_BODY: ${LOG_RESPONSE_BODY:-False}
    ports:
      - "${LLM_SERVER_PORT}:8080"
    volumes:
      - ./llm-core/model:/model
      - ./logs:/app/logs
    depends_on:
      timescale:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: on-failure:3
    deploy:
      resources:
        limits:
          cpus: 4
          memory: 12G
        reservations:
          memory: 8G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health/simple"]
      interval: 120s
      timeout: 30s
      retries: 3
      start_period: 120s
    ulimits:
      memlock: -1
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    shm_size: 2g

  chatbot-service:
    build: ./chatbot-service
    container_name: chatbot-service
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_HOST: timescale
      POSTGRES_PORT: ${POSTGRES_PORT}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      LLM_CORE_HOST: llm-core
      LLM_CORE_PORT: ${LLM_SERVER_PORT}
      TZ: ${TZ}
      VERSION: ${VERSION:-1.0.0}
      ENVIRONMENT: ${ENVIRONMENT:-development}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_FILE: /app/logs/chatbot-service.log
      LOG_REQUEST_BODY: ${LOG_REQUEST_BODY:-False}
      LOG_RESPONSE_BODY: ${LOG_RESPONSE_BODY:-False}
    ports:
      - "${CHATBOT_PORT}:8000"
    volumes:
      - ./logs:/app/logs
    depends_on:
      timescale:
        condition: service_healthy
      redis:
        condition: service_healthy
      llm-core:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/simple"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  integration-api:
    build: ./integration-api
    container_name: integration-api
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_HOST: timescale
      POSTGRES_PORT: ${POSTGRES_PORT}
      REDIS_HOST: redis
      REDIS_PORT: ${REDIS_PORT}
      WEBHOOK_SECRET_KEY: ${WEBHOOK_SECRET_KEY:-your-webhook-secret-key-change-in-production}
      WEBHOOK_VERIFY_SIGNATURE: ${WEBHOOK_VERIFY_SIGNATURE:-True}
      WEBHOOK_ALLOWED_IPS: ${WEBHOOK_ALLOWED_IPS:-}
      TZ: ${TZ}
      VERSION: ${VERSION:-1.0.0}
      ENVIRONMENT: ${ENVIRONMENT:-development}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_FILE: /app/logs/integration-api.log
      LOG_REQUEST_BODY: ${LOG_REQUEST_BODY:-False}
      LOG_RESPONSE_BODY: ${LOG_RESPONSE_BODY:-False}
    ports:
      - "${INTEGRATION_API_PORT:-9000}:9000"
    volumes:
      - ./logs:/app/logs
    depends_on:
      timescale:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health/simple"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  monitoring:
    image: nginx:alpine
    container_name: monitoring
    ports:
      - "${MONITORING_PORT:-8888}:80"
    volumes:
      - ./monitoring:/usr/share/nginx/html
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Database Management Tool - pgAdmin
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-123123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "${PGADMIN_PORT:-8081}:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./pgadmin/servers.json:/pgadmin4/servers.json:ro
    depends_on:
      timescale:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/misc/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  timescale_data:
  redis_data:
  pgadmin_data:
