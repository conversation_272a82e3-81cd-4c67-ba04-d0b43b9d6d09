#!/usr/bin/env python3
"""
Test đơn giản để tái tạo schema
"""

print("=== SIMPLE SCHEMA TEST ===")

# Test 1: Tái tạo schema đơn giản
try:
    print("1. Creating simple schemas...")
    
    from pydantic import BaseModel, Field
    from typing import Dict, Any, List, Optional
    from datetime import date
    
    class LotteryTypeSchema(BaseModel):
        """Schema cho loại xổ số"""
        id: int = Field(..., description="ID loại xổ số")
        code: str = Field(..., description="Mã loại xổ số")
        name: str = Field(..., description="Tên loại xổ số")
        region: Optional[str] = Field(None, description="Khu vực")
        description: Optional[str] = Field(None, description="Mô tả")

        class Config:
            from_attributes = True
    
    # Test instantiation
    lottery = LotteryTypeSchema(
        id=1,
        code="mb",
        name="<PERSON><PERSON><PERSON>",
        region="Bắc",
        description="Test"
    )
    
    print(f"✅ Simple schema creation OK: {lottery.name}")
    
except Exception as e:
    print(f"❌ Simple schema creation failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 2: Test với forward reference
try:
    print("2. Testing forward references...")
    
    class LotteryDrawSchema(BaseModel):
        id: int
        lottery_type_id: int
        draw_date: date
        results: Dict[str, Any]

        class Config:
            from_attributes = True

    class LotteryDrawWithTypeSchema(LotteryDrawSchema):
        lottery_type: "LotteryTypeSchema"  # Forward reference

        class Config:
            from_attributes = True
    
    print("✅ Forward reference schema creation OK")
    
except Exception as e:
    print(f"❌ Forward reference schema creation failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 All simple tests passed!")
print("✅ Schemas work fine when created independently!")
