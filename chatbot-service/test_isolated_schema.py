#!/usr/bin/env python3
"""
Test isolated schema without any other imports
"""

print("=== TESTING ISOLATED SCHEMA ===")

# Test 1: Import schemas in isolation
try:
    print("1. Testing isolated schema imports...")
    from app.schemas.lottery import (
        LotteryTypeSchema, 
        LotteryResultResponseSchema,
        LotteryTypesResponseSchema,
        LotteryResultsResponseSchema
    )
    print("✅ Isolated schema imports OK")
except Exception as e:
    print(f"❌ Isolated schema imports failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 2: Test schema instantiation
try:
    print("2. Testing schema instantiation...")
    
    lottery_type = LotteryTypeSchema(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Xổ số miền Bắc"
    )
    
    print(f"✅ Schema instantiation OK: {lottery_type.name}")
    
except Exception as e:
    print(f"❌ Schema instantiation failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 3: Test response schemas
try:
    print("3. Testing response schemas...")
    
    # Test LotteryTypesResponseSchema
    types_response = LotteryTypesResponseSchema(
        types=[lottery_type]
    )
    print(f"✅ LotteryTypesResponseSchema OK: {len(types_response.types)} types")
    
    # Test LotteryResultResponseSchema
    result = LotteryResultResponseSchema(
        id=1,
        lottery_type="Miền Bắc",
        lottery_code="mb",
        region="Bắc",
        draw_date="2024-01-01",
        results={"special": "12345"},
        formatted_results="Giải đặc biệt: 12345"
    )
    
    # Test LotteryResultsResponseSchema
    results_response = LotteryResultsResponseSchema(
        results=[result],
        count=1
    )
    print(f"✅ LotteryResultsResponseSchema OK: {results_response.count} results")
    
except Exception as e:
    print(f"❌ Response schemas failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 ALL ISOLATED SCHEMA TESTS PASSED!")
print("✅ Schemas work perfectly when isolated!")
print("✅ No RecursionError when no circular imports!")
print("✅ The issue is definitely circular imports from other files!")
