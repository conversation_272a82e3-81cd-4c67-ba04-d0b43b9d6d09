# API Thống kê và Soi cầu Xổ số

## Giới thiệu

API Thống kê và Soi cầu Xổ số cung cấp các endpoint để phân tích dữ liệu xổ số, thống kê tần suất xuất hiện của các số, và dự đoán kết quả xổ số. API này được tích hợp vào chatbot-service và cung cấp các endpoint RESTful để truy vấn dữ liệu.

## Endpoints

### 1. Thống kê tần suất

```
GET /api/v1/statistics/frequency
```

Lấy thống kê tần suất xuất hiện của các số.

**Tham số:**

- `lottery_type` (bắt buộc): M<PERSON> loại xổ số (mb, mt, mn)
- `start_date` (tùy chọn): <PERSON><PERSON><PERSON> b<PERSON><PERSON> đầ<PERSON> (YYYY-MM-DD)
- `end_date` (tù<PERSON> chọn): <PERSON><PERSON><PERSON> kế<PERSON> thúc (YYYY-MM-DD)
- `include_details` (tùy chọn): <PERSON><PERSON> gồm chi tiết (mặc định: false)

**Phản hồi:**

```json
{
  "lottery_type": "Xổ số Miền Bắc",
  "period": "Từ 15/10/2023 đến 15/11/2023",
  "total_draws": 30,
  "frequency": {
    "lottery_type": "Xổ số Miền Bắc",
    "start_date": "2023-10-15",
    "end_date": "2023-11-15",
    "total_draws": 30,
    "by_number": [
      {
        "number": "12345",
        "frequency": 5,
        "last_appeared": "2023-11-10",
        "days_ago": 5
      },
      ...
    ],
    "by_prize": [
      {
        "prize": "special",
        "numbers": [
          {
            "number": "12345",
            "frequency": 2,
            "last_appeared": "2023-11-10",
            "days_ago": 5
          },
          ...
        ]
      },
      ...
    ],
    "by_position": {
      "heads": {
        "1": 25,
        "2": 18,
        ...
      },
      "tails": {
        "5": 30,
        "8": 22,
        ...
      }
    }
  },
  "hot_numbers": [
    {
      "number": "12345",
      "frequency": 5,
      "last_appeared": "2023-11-10",
      "days_ago": 5
    },
    ...
  ],
  "cold_numbers": [
    {
      "number": "67890",
      "frequency": 1,
      "last_appeared": "2023-10-20",
      "days_ago": 26
    },
    ...
  ],
  "doubles": [
    {
      "number": "1122",
      "frequency": 3,
      "last_appeared": "2023-11-05"
    },
    ...
  ],
  "head_tail": {
    "heads": {
      "1": 25,
      "2": 18,
      ...
    },
    "tails": {
      "5": 30,
      "8": 22,
      ...
    },
    "hot_heads": ["1", "2", "3"],
    "hot_tails": ["5", "8", "0"]
  }
}
```

### 2. Lấy số nóng

```
GET /api/v1/statistics/hot-numbers
```

Lấy danh sách các số nóng (xuất hiện nhiều nhất).

**Tham số:**

- `lottery_type` (bắt buộc): Mã loại xổ số (mb, mt, mn)
- `days` (tùy chọn): Số ngày phân tích (mặc định: 30)
- `limit` (tùy chọn): Số lượng kết quả tối đa (mặc định: 10)

**Phản hồi:**

```json
["12345", "67890", "11111", "22222", "33333"]
```

### 3. Lấy số lạnh

```
GET /api/v1/statistics/cold-numbers
```

Lấy danh sách các số lạnh (lâu chưa về).

**Tham số:**

- `lottery_type` (bắt buộc): Mã loại xổ số (mb, mt, mn)
- `days` (tùy chọn): Số ngày phân tích (mặc định: 30)
- `limit` (tùy chọn): Số lượng kết quả tối đa (mặc định: 10)

**Phản hồi:**

```json
["44444", "55555", "66666", "77777", "88888"]
```

### 4. Thống kê đầu đuôi

```
GET /api/v1/statistics/head-tail
```

Lấy thống kê đầu đuôi.

**Tham số:**

- `lottery_type` (bắt buộc): Mã loại xổ số (mb, mt, mn)
- `days` (tùy chọn): Số ngày phân tích (mặc định: 30)

**Phản hồi:**

```json
{
  "heads": {
    "1": 25,
    "2": 18,
    ...
  },
  "tails": {
    "5": 30,
    "8": 22,
    ...
  },
  "hot_heads": ["1", "2", "3"],
  "hot_tails": ["5", "8", "0"]
}
```

### 5. Dự đoán kết quả

```
GET /api/v1/statistics/prediction
```

Lấy dự đoán kết quả xổ số.

**Tham số:**

- `lottery_type` (bắt buộc): Mã loại xổ số (mb, mt, mn)
- `prediction_date` (tùy chọn): Ngày dự đoán (YYYY-MM-DD)
- `analysis_period` (tùy chọn): Khoảng thời gian phân tích (ngày) (mặc định: 30)

**Phản hồi:**

```json
{
  "prediction": {
    "lottery_type": "Xổ số Miền Bắc",
    "prediction_date": "2023-11-16",
    "special_prize": ["12345", "67890", "11111"],
    "first_prize": ["22222", "33333", "44444"],
    "hot_numbers": ["12345", "67890", "11111", "22222", "33333", "44444", "55555", "66666", "77777", "88888"],
    "cold_numbers": ["99999", "00000", "11112", "22223", "33334", "44445", "55556", "66667", "77778", "88889"],
    "pairs": [
      ["12345", "67890"],
      ["11111", "22222"],
      ["33333", "44444"]
    ],
    "analysis": "Dự đoán xổ số Miền Bắc ngày 16/11/2023:\n\nDựa trên phân tích 30 kỳ quay gần nhất, các số có khả năng về cao là: 12345, 67890, 11111\n\nCác số nóng (xuất hiện nhiều): 12345, 67890, 11111, 22222, 33333\nCác số lạnh (lâu chưa về): 99999, 00000, 11112, 22223, 33334\n\nCác số đến chu kỳ: 12345, 33333\n\nCác cặp số thường về cùng nhau:\n- 12345 và 67890\n- 11111 và 22222\n- 33333 và 44444"
  },
  "statistics": {
    // Thống kê như trong endpoint /frequency
  },
  "cycles": {
    "lottery_type": "Xổ số Miền Bắc",
    "analysis_period": 30,
    "cycles": [
      {
        "number": "12345",
        "average_cycle": 7.5,
        "last_appeared": "2023-11-10",
        "next_predicted": "2023-11-17",
        "confidence": 0.85
      },
      ...
    ]
  }
}
```

### 6. Phân tích chu kỳ

```
GET /api/v1/statistics/cycles
```

Lấy phân tích chu kỳ xuất hiện của các số.

**Tham số:**

- `lottery_type` (bắt buộc): Mã loại xổ số (mb, mt, mn)
- `days` (tùy chọn): Số ngày phân tích (mặc định: 30)
- `top_n` (tùy chọn): Số lượng số cần phân tích (mặc định: 10)

**Phản hồi:**

```json
{
  "lottery_type": "Xổ số Miền Bắc",
  "analysis_period": 30,
  "cycles": [
    {
      "number": "12345",
      "average_cycle": 7.5,
      "last_appeared": "2023-11-10",
      "next_predicted": "2023-11-17",
      "confidence": 0.85
    },
    ...
  ]
}
```

### 7. Thống kê lô kép

```
GET /api/v1/statistics/doubles
```

Lấy thống kê lô kép.

**Tham số:**

- `lottery_type` (bắt buộc): Mã loại xổ số (mb, mt, mn)
- `days` (tùy chọn): Số ngày phân tích (mặc định: 30)
- `limit` (tùy chọn): Số lượng kết quả tối đa (mặc định: 10)

**Phản hồi:**

```json
[
  {
    "number": "1122",
    "frequency": 3,
    "last_appeared": "2023-11-05"
  },
  {
    "number": "3344",
    "frequency": 2,
    "last_appeared": "2023-11-10"
  },
  ...
]
```

### 8. Lấy các cặp số

```
GET /api/v1/statistics/pairs
```

Lấy các cặp số thường xuất hiện cùng nhau.

**Tham số:**

- `lottery_type` (bắt buộc): Mã loại xổ số (mb, mt, mn)
- `days` (tùy chọn): Số ngày phân tích (mặc định: 30)
- `limit` (tùy chọn): Số lượng kết quả tối đa (mặc định: 10)

**Phản hồi:**

```json
[
  ["12345", "67890"],
  ["11111", "22222"],
  ["33333", "44444"],
  ...
]
```

### 9. Tóm tắt thống kê

```
GET /api/v1/statistics/summary
```

Lấy tóm tắt thống kê.

**Tham số:**

- `lottery_type` (bắt buộc): Mã loại xổ số (mb, mt, mn)
- `days` (tùy chọn): Số ngày phân tích (mặc định: 30)

**Phản hồi:**

```json
{
  "lottery_type": "Xổ số Miền Bắc",
  "period": "Từ 15/10/2023 đến 15/11/2023",
  "total_draws": 30,
  "hot_numbers": ["12345", "67890", "11111", "22222", "33333"],
  "cold_numbers": ["44444", "55555", "66666", "77777", "88888"],
  "hot_heads": ["1", "2", "3"],
  "hot_tails": ["5", "8", "0"],
  "cycles": [
    {
      "number": "12345",
      "average_cycle": 7.5,
      "next_predicted": "2023-11-17"
    },
    ...
  ],
  "prediction": {
    "special_prize": ["12345", "67890", "11111"],
    "hot_numbers": ["12345", "67890", "11111", "22222", "33333"],
    "pairs": [
      ["12345", "67890"],
      ["11111", "22222"],
      ["33333", "44444"]
    ]
  }
}
```

## Mô hình dữ liệu

### NumberFrequency

```json
{
  "number": "12345",
  "frequency": 5,
  "last_appeared": "2023-11-10",
  "days_ago": 5
}
```

### PrizeFrequency

```json
{
  "prize": "special",
  "numbers": [
    {
      "number": "12345",
      "frequency": 2,
      "last_appeared": "2023-11-10",
      "days_ago": 5
    },
    ...
  ]
}
```

### FrequencyStatistics

```json
{
  "lottery_type": "Xổ số Miền Bắc",
  "start_date": "2023-10-15",
  "end_date": "2023-11-15",
  "total_draws": 30,
  "by_number": [
    {
      "number": "12345",
      "frequency": 5,
      "last_appeared": "2023-11-10",
      "days_ago": 5
    },
    ...
  ],
  "by_prize": [
    {
      "prize": "special",
      "numbers": [
        {
          "number": "12345",
          "frequency": 2,
          "last_appeared": "2023-11-10",
          "days_ago": 5
        },
        ...
      ]
    },
    ...
  ],
  "by_position": {
    "heads": {
      "1": 25,
      "2": 18,
      ...
    },
    "tails": {
      "5": 30,
      "8": 22,
      ...
    }
  }
}
```

### LotteryPrediction

```json
{
  "lottery_type": "Xổ số Miền Bắc",
  "prediction_date": "2023-11-16",
  "special_prize": ["12345", "67890", "11111"],
  "first_prize": ["22222", "33333", "44444"],
  "hot_numbers": ["12345", "67890", "11111", "22222", "33333", "44444", "55555", "66666", "77777", "88888"],
  "cold_numbers": ["99999", "00000", "11112", "22223", "33334", "44445", "55556", "66667", "77778", "88889"],
  "pairs": [
    ["12345", "67890"],
    ["11111", "22222"],
    ["33333", "44444"]
  ],
  "analysis": "Dự đoán xổ số Miền Bắc ngày 16/11/2023:\n\nDựa trên phân tích 30 kỳ quay gần nhất, các số có khả năng về cao là: 12345, 67890, 11111\n\nCác số nóng (xuất hiện nhiều): 12345, 67890, 11111, 22222, 33333\nCác số lạnh (lâu chưa về): 99999, 00000, 11112, 22223, 33334\n\nCác số đến chu kỳ: 12345, 33333\n\nCác cặp số thường về cùng nhau:\n- 12345 và 67890\n- 11111 và 22222\n- 33333 và 44444"
}
```

### LotteryCycle

```json
{
  "number": "12345",
  "average_cycle": 7.5,
  "last_appeared": "2023-11-10",
  "next_predicted": "2023-11-17",
  "confidence": 0.85
}
```

### CycleAnalysis

```json
{
  "lottery_type": "Xổ số Miền Bắc",
  "analysis_period": 30,
  "cycles": [
    {
      "number": "12345",
      "average_cycle": 7.5,
      "last_appeared": "2023-11-10",
      "next_predicted": "2023-11-17",
      "confidence": 0.85
    },
    ...
  ]
}
```

### DoubleNumbers

```json
{
  "number": "1122",
  "frequency": 3,
  "last_appeared": "2023-11-05"
}
```

### HeadTailStatistics

```json
{
  "heads": {
    "1": 25,
    "2": 18,
    ...
  },
  "tails": {
    "5": 30,
    "8": 22,
    ...
  },
  "hot_heads": ["1", "2", "3"],
  "hot_tails": ["5", "8", "0"]
}
```

## Caching

API sử dụng Redis để cache kết quả thống kê và dự đoán, giúp cải thiện hiệu suất và giảm tải cho database.

- Thống kê tần suất: Cache 1 giờ
- Dự đoán kết quả: Cache 1 giờ
- Danh sách kỳ quay: Cache 1 ngày

## Ví dụ sử dụng

### Lấy thống kê tần suất

```bash
curl -X GET "http://localhost:8000/api/v1/statistics/frequency?lottery_type=mb&days=30&include_details=true"
```

### Lấy dự đoán kết quả

```bash
curl -X GET "http://localhost:8000/api/v1/statistics/prediction?lottery_type=mb&analysis_period=30"
```

### Lấy số nóng

```bash
curl -X GET "http://localhost:8000/api/v1/statistics/hot-numbers?lottery_type=mb&days=30&limit=10"
```

## Tích hợp với Chatbot

API này được tích hợp với chatbot để cung cấp thông tin thống kê và dự đoán kết quả xổ số cho người dùng. Khi người dùng hỏi về thống kê hoặc dự đoán, chatbot sẽ gọi các endpoint tương ứng và trả về kết quả.

Ví dụ:

- "Cho tôi xem các số nóng của xổ số miền Bắc"
- "Dự đoán kết quả xổ số miền Nam ngày mai"
- "Thống kê lô gan miền Trung 30 ngày qua"
