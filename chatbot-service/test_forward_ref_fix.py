#!/usr/bin/env python3
"""
Test forward reference fix
"""

print("=== TESTING FORWARD REFERENCE FIX ===")

# Test 1: Import schemas
try:
    print("1. Testing schema imports...")
    from app.schemas.lottery import LotteryTypeSchema, LotteryResultResponseSchema
    print("✅ Schema imports OK")
except Exception as e:
    print(f"❌ Schema imports failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 2: Test schema instantiation
try:
    print("2. Testing schema instantiation...")
    
    lottery_type = LotteryTypeSchema(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Xổ số miền Bắc"
    )
    
    print(f"✅ Schema instantiation OK: {lottery_type.name}")
    
except Exception as e:
    print(f"❌ Schema instantiation failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 3: Test response schemas with forward references
try:
    print("3. Testing response schemas...")
    from app.schemas.lottery import LotteryTypesResponseSchema, LotteryResultsResponseSchema
    
    # Test LotteryTypesResponseSchema
    types_response = LotteryTypesResponseSchema(
        types=[lottery_type]
    )
    print(f"✅ LotteryTypesResponseSchema OK: {len(types_response.types)} types")
    
    # Test LotteryResultsResponseSchema
    result = LotteryResultResponseSchema(
        id=1,
        lottery_type="Miền Bắc",
        lottery_code="mb",
        region="Bắc",
        draw_date="2024-01-01",
        results={"special": "12345"},
        formatted_results="Giải đặc biệt: 12345"
    )
    
    results_response = LotteryResultsResponseSchema(
        results=[result],
        count=1
    )
    print(f"✅ LotteryResultsResponseSchema OK: {results_response.count} results")
    
except Exception as e:
    print(f"❌ Response schemas failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 ALL FORWARD REFERENCE TESTS PASSED!")
print("✅ Forward references are working correctly!")
print("✅ RecursionError should be fixed!")
