"""Sample lottery data

Revision ID: 002
Revises: 001
Create Date: 2023-11-16 10:00:00.000000

"""
from typing import Sequence, Union
from datetime import date, timedelta

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table, column
from sqlalchemy import String, Integer, Date, JSON

# revision identifiers, used by Alembic.
revision: str = '002'
down_revision: Union[str, None] = '001'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Tạo bảng tạm để chèn dữ liệu
    lottery_types = table('lottery_types',
        column('id', Integer),
        column('code', String),
        column('name', String),
        column('region', String),
        column('description', String),
    )
    
    lottery_draws = table('lottery_draws',
        column('id', Integer),
        column('lottery_type_id', Integer),
        column('draw_date', Date),
        column('results', JSON),
    )
    
    # Chèn dữ liệu loại xổ số
    op.bulk_insert(lottery_types,
        [
            {
                'id': 1,
                'code': 'mb',
                'name': '<PERSON>ổ số Miền Bắc',
                'region': 'Miền Bắc',
                'description': 'Xổ số kiến thiết Miền Bắc'
            },
            {
                'id': 2,
                'code': 'mt',
                'name': 'Xổ số Miền Trung',
                'region': 'Miền Trung',
                'description': 'Xổ số kiến thiết Miền Trung'
            },
            {
                'id': 3,
                'code': 'mn',
                'name': 'Xổ số Miền Nam',
                'region': 'Miền Nam',
                'description': 'Xổ số kiến thiết Miền Nam'
            }
        ]
    )
    
    # Tạo dữ liệu mẫu cho kết quả xổ số
    today = date.today()
    
    # Kết quả xổ số Miền Bắc
    mb_results = {
        'special': '12345',
        'first': '67890',
        'second': ['11111', '22222'],
        'third': ['33333', '33334', '33335', '33336', '33337', '33338'],
        'fourth': ['44441', '44442', '44443', '44444'],
        'fifth': ['55551', '55552', '55553', '55554', '55555', '55556'],
        'sixth': ['66661', '66662', '66663'],
        'seventh': ['77771', '77772', '77773', '77774'],
        'eighth': ['8881', '8882', '8883', '8884']
    }
    
    # Kết quả xổ số Miền Trung
    mt_results = {
        'special': '23456',
        'first': '78901',
        'second': ['22222', '33333'],
        'third': ['44444', '55555', '66666'],
        'fourth': ['77777', '88888', '99999', '00000'],
        'fifth': ['11112', '22223', '33334', '44445', '55556', '66667'],
        'sixth': ['77778', '88889', '99990'],
        'seventh': ['12345', '23456', '34567', '45678'],
        'eighth': ['5678', '6789', '7890', '8901']
    }
    
    # Kết quả xổ số Miền Nam
    mn_results = {
        'special': '34567',
        'first': '89012',
        'second': ['33333', '44444'],
        'third': ['55555', '66666', '77777'],
        'fourth': ['88888', '99999', '00000', '11111'],
        'fifth': ['22223', '33334', '44445', '55556', '66667', '77778'],
        'sixth': ['88889', '99990', '00001'],
        'seventh': ['12345', '23456', '34567', '45678'],
        'eighth': ['5678', '6789', '7890', '8901']
    }
    
    # Chèn dữ liệu kết quả xổ số cho 7 ngày gần đây
    draws_data = []
    for i in range(7):
        draw_date = today - timedelta(days=i)
        
        # Miền Bắc
        draws_data.append({
            'lottery_type_id': 1,
            'draw_date': draw_date,
            'results': mb_results
        })
        
        # Miền Trung
        draws_data.append({
            'lottery_type_id': 2,
            'draw_date': draw_date,
            'results': mt_results
        })
        
        # Miền Nam
        draws_data.append({
            'lottery_type_id': 3,
            'draw_date': draw_date,
            'results': mn_results
        })
    
    op.bulk_insert(lottery_draws, draws_data)


def downgrade() -> None:
    # Xóa dữ liệu mẫu
    op.execute("DELETE FROM lottery_draws")
    op.execute("DELETE FROM lottery_types")
