"""Initial tables

Revision ID: 001
Revises: 
Create Date: 2023-11-15 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON

# revision identifiers, used by Alembic.
revision: str = '001'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Tạo bảng lottery_types
    op.create_table('lottery_types',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('code', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('region', sa.String(length=50), nullable=True),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('code')
    )
    op.create_index(op.f('ix_lottery_types_code'), 'lottery_types', ['code'], unique=True)
    op.create_index(op.f('ix_lottery_types_id'), 'lottery_types', ['id'], unique=False)

    # Tạo bảng lottery_draws
    op.create_table('lottery_draws',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('lottery_type_id', sa.Integer(), nullable=False),
        sa.Column('draw_date', sa.Date(), nullable=False),
        sa.Column('results', JSON(), nullable=True),
        sa.ForeignKeyConstraint(['lottery_type_id'], ['lottery_types.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('lottery_type_id', 'draw_date', name='uix_lottery_draw')
    )
    op.create_index(op.f('ix_lottery_draws_draw_date'), 'lottery_draws', ['draw_date'], unique=False)
    op.create_index(op.f('ix_lottery_draws_id'), 'lottery_draws', ['id'], unique=False)

    # Tạo bảng user_queries
    op.create_table('user_queries',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(length=50), nullable=False),
        sa.Column('query_text', sa.Text(), nullable=False),
        sa.Column('intent', sa.String(length=50), nullable=True),
        sa.Column('response_summary', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_queries_id'), 'user_queries', ['id'], unique=False)
    op.create_index(op.f('ix_user_queries_user_id'), 'user_queries', ['user_id'], unique=False)


def downgrade() -> None:
    # Xóa các bảng theo thứ tự ngược lại
    op.drop_index(op.f('ix_user_queries_user_id'), table_name='user_queries')
    op.drop_index(op.f('ix_user_queries_id'), table_name='user_queries')
    op.drop_table('user_queries')
    
    op.drop_index(op.f('ix_lottery_draws_id'), table_name='lottery_draws')
    op.drop_index(op.f('ix_lottery_draws_draw_date'), table_name='lottery_draws')
    op.drop_table('lottery_draws')
    
    op.drop_index(op.f('ix_lottery_types_id'), table_name='lottery_types')
    op.drop_index(op.f('ix_lottery_types_code'), table_name='lottery_types')
    op.drop_table('lottery_types')
