FROM python:3.12-slim

WORKDIR /app

# Cài đặt các gói phụ thuộc
RUN apt-get update && apt-get install -y \
    netcat-traditional \
    && rm -rf /var/lib/apt/lists/*

# Sao chép requirements và cài đặt
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Sao chép mã nguồn ứng dụng
COPY . .

# Cấp quyền thực thi cho script khởi động
RUN chmod +x start-container.sh

# Mở cổng
EXPOSE 8000

# Khởi động ứng dụng
ENTRYPOINT ["/app/start-container.sh"]
