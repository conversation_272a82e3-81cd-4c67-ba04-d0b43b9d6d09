#!/bin/bash
set -e

echo "=== [chatbot-service] Đang khởi động dịch vụ... ==="

# In thông tin môi trường (cho mục đích gỡ lỗi)
echo "Môi trường:"
echo "- PostgreSQL: ${POSTGRES_HOST}:${POSTGRES_PORT}"
echo "- Redis: ${REDIS_HOST}:${REDIS_PORT}"
echo "- LLM Core: ${LLM_CORE_HOST}:${LLM_CORE_PORT}"

# Chờ PostgreSQL sẵn sàng
echo "Đang chờ PostgreSQL..."
while ! nc -z ${POSTGRES_HOST} ${POSTGRES_PORT}; do
  sleep 1
done
echo "PostgreSQL đã sẵn sàng!"

# Chờ Redis sẵn sàng
echo "Đang chờ Redis..."
while ! nc -z ${REDIS_HOST} ${REDIS_PORT}; do
  sleep 1
done
echo "Redis đã sẵn sàng!"

# Chờ LLM Core sẵn sàng
echo "Đang chờ LLM Core..."
while ! nc -z ${LLM_CORE_HOST} ${LLM_CORE_PORT}; do
  sleep 1
done
echo "LLM Core đã sẵn sàng!"

# Khởi động ứng dụng FastAPI
echo "=== [chatbot-service] Đang khởi động máy chủ FastAPI ==="
cd /app
exec uvicorn app.main:app --host 0.0.0.0 --port 8000
