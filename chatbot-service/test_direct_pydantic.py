#!/usr/bin/env python3
"""
Test Pydantic directly without any app imports
"""

print("=== TESTING DIRECT PYDANTIC ===")

try:
    print("1. Testing Pydantic import...")
    from pydantic import BaseModel, Field
    from typing import Dict, Any, List, Optional
    from datetime import date
    print("✅ Pydantic imports OK")
    
    print("2. Creating schema directly...")
    
    class TestSchema(BaseModel):
        id: int = Field(..., description="ID")
        name: str = Field(..., description="Name")
        
    test = TestSchema(id=1, name="test")
    print(f"✅ Direct schema works: {test.name}")
    
    print("3. Testing with date field...")
    
    class TestWithDate(BaseModel):
        id: int
        name: str
        created: date
        
    test_date = TestWithDate(id=1, name="test", created="2024-01-01")
    print(f"✅ Schema with date works: {test_date.created}")
    
    print("4. Testing with Optional and List...")
    
    class TestComplex(BaseModel):
        id: int
        name: str
        region: Optional[str] = None
        items: List[str] = []
        
    test_complex = TestComplex(id=1, name="test", region="region1", items=["a", "b"])
    print(f"✅ Complex schema works: {test_complex.region}")
    
except Exception as e:
    print(f"❌ Direct Pydantic failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("🎉 Direct Pydantic works perfectly!")
print("The issue is definitely in the app structure, not Pydantic itself.")
