# Tích hợp Redis Cache cho Chatbot Service

## Giới thiệu

Dự án chatbot-service đã được tích hợp với Redis cache để cải thiện hiệu suất và trải nghiệm người dùng. Redis được sử dụng để cache các truy vấn/câu trả lời hot, context chat và các dữ liệu khác.

## Tính năng chính

1. **Cache cho truy vấn/câu trả lời hot**
   - Theo dõi số lượng truy vấn để xác định các truy vấn hot
   - Cache câu trả lời cho các truy vấn hot để phản hồi nhanh hơn
   - API endpoint để lấy danh sách các truy vấn hot

2. **Cache cho context chat**
   - Lưu trữ lịch sử chat của người dùng trong Redis
   - Sử dụng context để cải thiện chất lượng phản hồi của LLM
   - API endpoints để quản lý context chat

3. **<PERSON><PERSON> cho kết quả xổ số**
   - Cache kết quả xổ số với thời gian TTL dài hơn
   - Giảm tải cho database

4. **Cache thông minh với TTL tùy chỉnh**
   - Thời gian cache khác nhau cho các loại dữ liệu khác nhau
   - Cấu hình linh hoạt thông qua biến môi trường

## Cấu trúc

```
chatbot-service/
├── app/
│   ├── cache/
│   │   ├── __init__.py
│   │   └── redis_client.py      # Client Redis và CacheService
│   ├── services/
│   │   ├── chat_context.py      # Quản lý context chat
│   │   └── chatbot.py           # Sử dụng cache trong xử lý chat
```

## Các loại cache

### 1. Chat Cache

Cache cho các phản hồi chat thông thường, với TTL mặc định là 1 giờ.

```python
# Lưu vào cache
await CacheService.set_json(
    cache_key, 
    response.model_dump(), 
    expire=settings.cache_chat_ttl
)

# Lấy từ cache
cached_response = await CacheService.get_json(cache_key)
```

### 2. Context Cache

Cache cho context chat của người dùng, với TTL mặc định là 1 ngày.

```python
# Lấy context
context = await ChatContextService.get_context(user_id)

# Thêm vào lịch sử
await ChatContextService.add_to_history(user_id, message, response, intent)

# Xóa lịch sử
await ChatContextService.clear_history(user_id)
```

### 3. Hot Query Cache

Cache cho các truy vấn hot (được truy vấn nhiều lần), với TTL mặc định là 12 giờ.

```python
# Theo dõi truy vấn
await CacheService.track_query(message)

# Lấy phản hồi cho hot query
hot_response = await CacheService.get_hot_response(message)

# Lấy danh sách hot queries
hot_queries = await CacheService.get_hot_queries(limit=10)
```

### 4. Lottery Result Cache

Cache cho kết quả xổ số, với TTL mặc định là 1 ngày.

```python
# Lưu kết quả xổ số vào cache
await CacheService.set(
    cache_key, 
    formatted_result, 
    expire=settings.cache_lottery_ttl
)
```

## API Endpoints

- `GET /api/v1/stats/hot-queries` - Lấy danh sách các truy vấn hot
- `GET /api/v1/context/{user_id}` - Lấy context chat của người dùng
- `DELETE /api/v1/context/{user_id}` - Xóa context chat của người dùng

## Biến môi trường

| Biến | Mô tả | Giá trị mặc định |
|------|-------|-----------------|
| `REDIS_HOST` | Host Redis | `redis` |
| `REDIS_PORT` | Port Redis | `6379` |
| `REDIS_DB` | Database Redis | `0` |
| `REDIS_PASSWORD` | Mật khẩu Redis | `` |
| `REDIS_SOCKET_TIMEOUT` | Timeout cho socket (giây) | `5.0` |
| `REDIS_CONNECT_TIMEOUT` | Timeout cho kết nối (giây) | `5.0` |
| `REDIS_RETRY_ON_TIMEOUT` | Thử lại khi timeout | `True` |
| `REDIS_MAX_CONNECTIONS` | Số kết nối tối đa | `10` |
| `CACHE_TTL` | Thời gian cache mặc định (giây) | `3600` |
| `CACHE_PREFIX` | Tiền tố cho cache key | `xosotv:` |
| `CACHE_CHAT_TTL` | Thời gian cache cho chat (giây) | `3600` |
| `CACHE_CONTEXT_TTL` | Thời gian cache cho context (giây) | `86400` |
| `CACHE_HOT_QUERY_TTL` | Thời gian cache cho truy vấn hot (giây) | `43200` |
| `CACHE_LOTTERY_TTL` | Thời gian cache cho kết quả xổ số (giây) | `86400` |
| `HOT_QUERY_THRESHOLD` | Số lần truy vấn để coi là hot | `5` |
| `HOT_QUERY_WINDOW` | Cửa sổ thời gian cho hot query (giây) | `86400` |

## Ví dụ sử dụng

### Theo dõi và cache truy vấn hot

```python
# Theo dõi truy vấn
count = await CacheService.track_query("Kết quả xổ số hôm nay là gì?")

# Nếu đạt ngưỡng, cache phản hồi
if count >= settings.hot_query_threshold:
    await CacheService.cache_hot_response(
        "Kết quả xổ số hôm nay là gì?", 
        "Kết quả xổ số hôm nay là: ..."
    )
```

### Sử dụng context chat

```python
# Lấy context chat
context = await ChatContextService.get_context(user_id)

# Định dạng context cho LLM
formatted_context = await ChatContextService.format_context_for_llm(user_id)

# Thêm vào prompt
prompt = f"{formatted_context}\n\nUser: {message}"
```

## Lợi ích

1. **Cải thiện hiệu suất**: Giảm thời gian phản hồi bằng cách cache các kết quả phổ biến
2. **Giảm tải cho database và LLM**: Giảm số lượng truy vấn đến database và LLM
3. **Trải nghiệm người dùng tốt hơn**: Phản hồi nhanh hơn cho các truy vấn phổ biến
4. **Tiết kiệm tài nguyên**: Giảm tải cho các dịch vụ khác trong hệ thống
5. **Phân tích xu hướng**: Theo dõi các truy vấn hot để hiểu nhu cầu người dùng
