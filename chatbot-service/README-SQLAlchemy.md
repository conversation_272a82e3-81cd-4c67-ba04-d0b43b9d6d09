# Tích hợp SQLAlchemy ORM

## Giới thiệu

Dự án chatbot-service đã được tích hợp với SQLAlchemy ORM để quản lý tương tác với cơ sở dữ liệu PostgreSQL. SQLAlchemy cung cấp một cách tiếp cận hướng đối tượng để làm việc với cơ sở dữ liệu, gi<PERSON>p code dễ đọc, d<PERSON> bảo trì và an toàn hơn.

## Cấu trúc

```
chatbot-service/
├── alembic/                # Quản lý migrations
│   ├── versions/           # Các file migration
│   ├── env.py              # Cấu hình môi trường Alembic
│   └── script.py.mako      # Template cho migration scripts
├── app/
│   ├── db/
│   │   ├── base.py         # Base class cho các model
│   │   ├── postgresql.py   # Kết nối PostgreSQL (legacy + SQLAlchemy)
│   │   └── session.py      # Quản lý SQLAlchemy session
│   ├── models/             # Định nghĩa các model
│   │   ├── __init__.py
│   │   ├── lottery.py      # Model cho xổ số
│   │   └── user_query.py   # Model cho lịch sử chat
```

## Models

### UserQueryModel

Model `UserQueryModel` đại diện cho bảng `user_queries` trong cơ sở dữ liệu, lưu trữ lịch sử tương tác của người dùng với chatbot.

```python
class UserQueryModel(Base):
    __tablename__ = "user_queries"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(50), nullable=False, index=True)
    query_text = Column(Text, nullable=False)
    intent = Column(String(50), nullable=True)
    response_summary = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
```

### LotteryTypeModel và LotteryDrawModel

Models `LotteryTypeModel` và `LotteryDrawModel` đại diện cho các bảng `lottery_types` và `lottery_draws` trong cơ sở dữ liệu, lưu trữ thông tin về các loại xổ số và kết quả xổ số.

```python
class LotteryTypeModel(Base):
    __tablename__ = "lottery_types"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(20), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=False)
    region = Column(String(50), nullable=True)
    description = Column(String(255), nullable=True)

    # Relationship
    draws = relationship("LotteryDrawModel", back_populates="lottery_type")

class LotteryDrawModel(Base):
    __tablename__ = "lottery_draws"
    __table_args__ = (
        UniqueConstraint('lottery_type_id', 'draw_date', name='uix_lottery_draw'),
    )

    id = Column(Integer, primary_key=True, index=True)
    lottery_type_id = Column(Integer, ForeignKey("lottery_types.id"), nullable=False)
    draw_date = Column(Date, nullable=False, index=True)
    results = Column(JSON, nullable=True)

    # Relationship
    lottery_type = relationship("LotteryTypeModel", back_populates="draws")
```

## Migrations

Dự án sử dụng Alembic để quản lý migrations. Các file migration được lưu trong thư mục `alembic/versions/`.

### Tạo migration mới

```bash
cd chatbot-service
alembic revision --autogenerate -m "description"
```

### Áp dụng migrations

```bash
cd chatbot-service
alembic upgrade head
```

### Rollback migrations

```bash
cd chatbot-service
alembic downgrade -1  # Rollback 1 version
alembic downgrade base  # Rollback tất cả
```

## Sử dụng trong code

### Dependency Injection

```python
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.postgresql import get_db_session

@app.get("/items/{item_id}")
async def read_item(item_id: int, db: AsyncSession = Depends(get_db_session)):
    # Sử dụng db session
    ...
```

### Truy vấn dữ liệu

```python
# Lấy tất cả
result = await db.execute(Model.__table__.select())
items = result.scalars().all()

# Lấy theo điều kiện
result = await db.execute(Model.__table__.select().where(Model.id == item_id))
item = result.scalars().first()

# Lấy với join
result = await db.execute(
    select(Model, RelatedModel)
    .join(RelatedModel, Model.related_id == RelatedModel.id)
    .where(Model.id == item_id)
)
item, related = result.first()
```

### Thêm dữ liệu

```python
new_item = Model(name="New Item", description="Description")
db.add(new_item)
await db.commit()
await db.refresh(new_item)
```

### Cập nhật dữ liệu

```python
result = await db.execute(Model.__table__.select().where(Model.id == item_id))
item = result.scalars().first()
item.name = "Updated Name"
await db.commit()
```

### Xóa dữ liệu

```python
await db.execute(Model.__table__.delete().where(Model.id == item_id))
await db.commit()
```

## Biến môi trường

| Biến | Mô tả | Giá trị mặc định |
|------|-------|-----------------|
| `POSTGRES_HOST` | Host PostgreSQL | `postgres` |
| `POSTGRES_PORT` | Port PostgreSQL | `5432` |
| `POSTGRES_USER` | User PostgreSQL | `xosotv_user` |
| `POSTGRES_PASSWORD` | Password PostgreSQL | `` |
| `POSTGRES_DB` | Database PostgreSQL | `xosotv` |
| `POSTGRES_POOL_SIZE` | Kích thước pool | `5` |
| `POSTGRES_MAX_OVERFLOW` | Số kết nối tối đa vượt quá pool size | `10` |
| `POSTGRES_POOL_TIMEOUT` | Thời gian timeout khi lấy kết nối từ pool (giây) | `30` |
| `POSTGRES_POOL_RECYCLE` | Thời gian tái sử dụng kết nối (giây) | `1800` |
