#!/usr/bin/env python3
"""
Debug RecursionError từ bên trong chatbot-service
"""

print("=== DEBUG RECURSION ERROR ===")

# Test 1: Basic imports
try:
    print("1. Testing basic imports...")
    from typing import Dict, Any, List, Optional
    from datetime import date
    from pydantic import BaseModel, Field
    print("✅ Basic imports OK")
except Exception as e:
    print(f"❌ Basic imports failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 1.5: Test schema import trực tiếp
try:
    print("1.5. Testing direct schema import...")
    from app.schemas.lottery import LotteryTypeSchema
    print("✅ Direct schema import OK")

    # Test schema instantiation
    lottery = LotteryTypeSchema(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Test"
    )
    print(f"✅ Schema instantiation OK: {lottery.name}")

except Exception as e:
    print(f"❌ Direct schema import failed: {e}")
    import traceback
    traceback.print_exc()
    # Không exit, tiếp tục test

# Test 2: Test simple schema creation (không import từ app)
try:
    print("2. Testing simple schema creation...")

    class TestSchema(BaseModel):
        id: int
        name: str

    test = TestSchema(id=1, name="test")
    print(f"✅ Simple schema OK: {test.name}")
except Exception as e:
    print(f"❌ Simple schema failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 3: Test app config
try:
    print("3. Testing app config...")
    from app.config import settings
    print("✅ App config OK")
except Exception as e:
    print(f"❌ App config failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 4: Test db base
try:
    print("4. Testing db base...")
    from app.db.base import Base
    print("✅ DB base OK")
except Exception as e:
    print(f"❌ DB base failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 5: Test individual schema file (không qua __init__)
try:
    print("5. Testing direct schema file import...")
    import sys
    import importlib.util

    # Import trực tiếp file schema
    spec = importlib.util.spec_from_file_location("lottery_schema", "app/schemas/lottery.py")
    lottery_schema_module = importlib.util.module_from_spec(spec)

    # Thêm vào sys.modules để tránh circular import
    sys.modules["lottery_schema"] = lottery_schema_module
    spec.loader.exec_module(lottery_schema_module)

    print("✅ Direct schema file import OK")
    print(f"Available classes: {[name for name in dir(lottery_schema_module) if name.endswith('Schema')]}")

except Exception as e:
    print(f"❌ Direct schema file import failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 Debug completed successfully!")
