#!/usr/bin/env python3
"""
Final test để kiểm tra RecursionError đã được sửa
"""

print("=== FINAL RECURSION ERROR TEST ===")

# Test 1: Import schemas
try:
    print("1. Testing schema imports...")
    from app.schemas.lottery import LotteryTypeSchema, LotteryResultResponseSchema
    print("✅ Schema imports OK")
except Exception as e:
    print(f"❌ Schema imports failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 2: Test schema instantiation
try:
    print("2. Testing schema instantiation...")

    lottery_type = LotteryTypeSchema(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Xổ số miền Bắc"
    )

    print(f"✅ Schema instantiation OK: {lottery_type.name}")

except Exception as e:
    print(f"❌ Schema instantiation failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 3: Import models
try:
    print("3. Testing model imports...")
    from app.models.lottery import LotteryTypeModel, LotteryDrawModel
    print("✅ Model imports OK")
except Exception as e:
    print(f"❌ Model imports failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 4: Import services
try:
    print("4. Testing service imports...")
    from app.services.lottery import LotteryService
    print("✅ Service imports OK")
except Exception as e:
    print(f"❌ Service imports failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 5: Test response schemas with forward references
try:
    print("5. Testing response schemas...")
    from app.schemas.lottery import LotteryTypesResponseSchema, LotteryResultsResponseSchema

    # Test với forward references
    types_response = LotteryTypesResponseSchema(
        types=[lottery_type]
    )
    print(f"✅ Response schemas OK: {len(types_response.types)} types")

except Exception as e:
    print(f"❌ Response schemas failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 ALL TESTS PASSED!")
print("✅ RecursionError has been completely resolved!")
print("✅ All schemas, models, and services can be imported successfully!")
print("✅ Forward references are working correctly!")
print("✅ System is ready to use!")
