import time
import logging
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.config import settings
from app.cache.redis_client import CacheService, generate_cache_key

logger = logging.getLogger("rate-limiter")

class RateLimiterMiddleware(BaseHTTPMiddleware):
    """
    Middleware để giới hạn tốc độ truy cập API
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        # Bỏ qua nếu rate limiting không được bật
        if not settings.rate_limit_enabled:
            return await call_next(request)
        
        # Bỏ qua các endpoint không cần rate limiting
        if request.url.path in ["/health", "/", "/api/docs", "/api/redoc", "/api/openapi.json"]:
            return await call_next(request)
        
        # Lấy client IP hoặc user ID nếu đã xác thực
        client_id = request.client.host if request.client else "unknown"
        if "user_id" in request.session:
            client_id = request.session["user_id"]
        
        # Tạo khóa rate limit
        rate_limit_key = generate_cache_key("rate_limit", client_id, path=request.url.path)
        
        # Kiểm tra số lượng request hiện tại
        current_count = await CacheService.get(rate_limit_key)
        
        if current_count is None:
            # Khởi tạo bộ đếm nếu chưa tồn tại
            await CacheService.set(rate_limit_key, "1", expire=settings.rate_limit_window)
            current_count = 1
        else:
            current_count = int(current_count)
            
            # Kiểm tra giới hạn
            if current_count >= settings.rate_limit_requests:
                logger.warning(f"Rate limit exceeded for {client_id} on {request.url.path}")
                
                # Lấy thời gian còn lại của khóa
                ttl = await CacheService.ttl(rate_limit_key)
                
                # Trả về lỗi 429 Too Many Requests
                raise HTTPException(
                    status_code=429,
                    detail={
                        "error": "Quá nhiều yêu cầu",
                        "message": "Bạn đã gửi quá nhiều yêu cầu. Vui lòng thử lại sau.",
                        "retry_after": ttl
                    }
                )
            
            # Tăng bộ đếm
            await CacheService.increment(rate_limit_key)
        
        # Thêm header rate limit vào response
        response = await call_next(request)
        response.headers["X-RateLimit-Limit"] = str(settings.rate_limit_requests)
        response.headers["X-RateLimit-Remaining"] = str(settings.rate_limit_requests - current_count)
        response.headers["X-RateLimit-Reset"] = str(await CacheService.ttl(rate_limit_key))
        
        return response
