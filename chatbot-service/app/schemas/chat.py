from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
from datetime import datetime

class ChatRequestSchema(BaseModel):
    """
    Schema cho yêu cầu chat
    """
    user_id: str = Field(..., description="Định danh duy nhất của người dùng")
    message: str = Field(..., description="Tin nhắn của người dùng")
    context: Optional[Dict[str, Any]] = Field(None, description="Ngữ cảnh bổ sung cho cuộc trò chuyện")

class ChatResponseSchema(BaseModel):
    """
    Schema cho phản hồi chat
    """
    response: str = Field(..., description="Tin nhắn phản hồi")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="Thời gian của phản hồi")
    intent: Optional[str] = Field(None, description="Ý định được phát hiện từ tin nhắn của người dùng")
    sub_intent: Optional[str] = Field(None, description="Ý định phụ")
    confidence: Optional[float] = Field(None, description="Độ tin cậy của phân tích ý định")
    data: Optional[Dict[str, Any]] = Field(None, description="Dữ liệu bổ sung (kết quả xổ số, thống kê, v.v.)")
    processing_time: Optional[float] = Field(None, description="Thời gian xử lý (giây)")

class ChatHistorySchema(BaseModel):
    """
    Schema cho mục lịch sử chat
    """
    message: str = Field(..., description="Tin nhắn của người dùng")
    response: str = Field(..., description="Phản hồi của bot")
    intent: Optional[str] = Field(None, description="Ý định được phát hiện")
    timestamp: str = Field(..., description="Thời gian của tương tác")
