from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import date

class NumberFrequencySchema(BaseModel):
    """Schema cho tần suất xuất hiện của một số"""
    number: str = Field(..., description="Số xổ số")
    frequency: int = Field(..., description="Số lần xuất hiện")
    last_appeared: Optional[date] = Field(None, description="Ngày xuất hiện gần nhất")
    days_ago: Optional[int] = Field(None, description="Số ngày kể từ lần xuất hiện gần nhất")

class PrizeFrequencySchema(BaseModel):
    """Schema cho tần suất xuất hiện theo giải"""
    prize: str = Field(..., description="Tên giải")
    numbers: List[NumberFrequencySchema] = Field(..., description="Danh sách số và tần suất")

class FrequencyStatisticsSchema(BaseModel):
    """Schema cho thống kê tần suất"""
    lottery_type: str = Field(..., description="Loại xổ số")
    start_date: date = Field(..., description="Ngày bắt đầu thống kê")
    end_date: date = Field(..., description="Ngày kết thúc thống kê")
    total_draws: int = Field(..., description="Tổng số kỳ quay")
    by_number: List[NumberFrequencySchema] = Field(..., description="Thống kê theo số")
    by_prize: Optional[List[PrizeFrequencySchema]] = Field(None, description="Thống kê theo giải")
    by_position: Optional[Dict[str, List[NumberFrequencySchema]]] = Field(None, description="Thống kê theo vị trí (đầu, đuôi)")

class LotteryPredictionSchema(BaseModel):
    """Schema cho dự đoán xổ số"""
    lottery_type: str = Field(..., description="Loại xổ số")
    prediction_date: date = Field(..., description="Ngày dự đoán")
    special_prize: List[str] = Field(..., description="Dự đoán giải đặc biệt")
    first_prize: Optional[List[str]] = Field(None, description="Dự đoán giải nhất")
    hot_numbers: List[str] = Field(..., description="Các số nóng")
    cold_numbers: List[str] = Field(..., description="Các số lạnh")
    pairs: List[List[str]] = Field(..., description="Các cặp số có khả năng về cùng nhau")
    analysis: str = Field(..., description="Phân tích chi tiết")

class LotteryCycleSchema(BaseModel):
    """Schema cho chu kỳ xổ số"""
    number: str = Field(..., description="Số xổ số")
    average_cycle: float = Field(..., description="Chu kỳ trung bình (ngày)")
    last_appeared: Optional[date] = Field(None, description="Ngày xuất hiện gần nhất")
    next_predicted: Optional[date] = Field(None, description="Ngày dự đoán xuất hiện tiếp theo")
    confidence: float = Field(..., description="Độ tin cậy (0-1)")

class CycleAnalysisSchema(BaseModel):
    """Schema cho phân tích chu kỳ"""
    lottery_type: str = Field(..., description="Loại xổ số")
    analysis_period: int = Field(..., description="Khoảng thời gian phân tích (ngày)")
    cycles: List[LotteryCycleSchema] = Field(..., description="Danh sách chu kỳ")

class DoubleNumbersSchema(BaseModel):
    """Schema cho thống kê lô kép"""
    number: str = Field(..., description="Số kép")
    frequency: int = Field(..., description="Số lần xuất hiện")
    last_appeared: Optional[date] = Field(None, description="Ngày xuất hiện gần nhất")

class HeadTailStatisticsSchema(BaseModel):
    """Schema cho thống kê đầu đuôi"""
    heads: Dict[str, int] = Field(..., description="Thống kê theo đầu số")
    tails: Dict[str, int] = Field(..., description="Thống kê theo đuôi số")
    hot_heads: List[str] = Field(..., description="Các đầu số nóng")
    hot_tails: List[str] = Field(..., description="Các đuôi số nóng")

class StatisticsRequestSchema(BaseModel):
    """Schema cho yêu cầu thống kê"""
    lottery_type: Optional[str] = Field(None, description="Mã loại xổ số")
    start_date: Optional[date] = Field(None, description="Ngày bắt đầu")
    end_date: Optional[date] = Field(None, description="Ngày kết thúc")
    limit: Optional[int] = Field(10, description="Số lượng kết quả tối đa", ge=1, le=100)
    include_details: Optional[bool] = Field(False, description="Bao gồm chi tiết")

class StatisticsResponseSchema(BaseModel):
    """Schema cho phản hồi thống kê"""
    lottery_type: str = Field(..., description="Loại xổ số")
    period: str = Field(..., description="Khoảng thời gian thống kê")
    total_draws: int = Field(..., description="Tổng số kỳ quay")
    frequency: FrequencyStatisticsSchema = Field(..., description="Thống kê tần suất")
    hot_numbers: List[NumberFrequencySchema] = Field(..., description="Các số nóng")
    cold_numbers: List[NumberFrequencySchema] = Field(..., description="Các số lạnh")
    doubles: Optional[List[DoubleNumbersSchema]] = Field(None, description="Thống kê lô kép")
    head_tail: Optional[HeadTailStatisticsSchema] = Field(None, description="Thống kê đầu đuôi")

class PredictionRequestSchema(BaseModel):
    """Schema cho yêu cầu dự đoán"""
    lottery_type: str = Field(..., description="Mã loại xổ số")
    prediction_date: Optional[date] = Field(None, description="Ngày dự đoán")
    analysis_period: Optional[int] = Field(30, description="Khoảng thời gian phân tích (ngày)", ge=7, le=365)

class PredictionResponseSchema(BaseModel):
    """Schema cho phản hồi dự đoán"""
    prediction: LotteryPredictionSchema = Field(..., description="Dự đoán xổ số")
    statistics: StatisticsResponseSchema = Field(..., description="Thống kê liên quan")
    cycles: Optional[CycleAnalysisSchema] = Field(None, description="Phân tích chu kỳ")
