"""
New clean lottery schemas without any circular imports
"""

from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import date

class LotteryTypeBaseSchema(BaseModel):
    """Schema cơ bản cho loại xổ số"""
    code: str = Field(..., description="Mã loại xổ số")
    name: str = Field(..., description="Tên loại xổ số")
    region: Optional[str] = Field(None, description="Khu vực")
    description: Optional[str] = Field(None, description="Mô tả")

class LotteryTypeCreateSchema(LotteryTypeBaseSchema):
    """Schema cho việc tạo loại xổ số mới"""
    pass

class LotteryTypeSchema(LotteryTypeBaseSchema):
    """Schema cho loại xổ số"""
    id: int = Field(..., description="ID loại xổ số")

    class Config:
        from_attributes = True

class LotteryDrawBaseSchema(BaseModel):
    """Schema cơ bản cho kết quả xổ số"""
    lottery_type_id: int = Field(..., description="ID loại xổ số")
    draw_date: date = Field(..., description="Ngày quay thưởng")
    results: Dict[str, Any] = Field(..., description="Kết quả xổ số dạng JSON")

class LotteryDrawCreateSchema(LotteryDrawBaseSchema):
    """Schema cho việc tạo kết quả xổ số mới"""
    pass

class LotteryDrawSchema(LotteryDrawBaseSchema):
    """Schema cho kết quả xổ số"""
    id: int = Field(..., description="ID kết quả xổ số")

    class Config:
        from_attributes = True

class LotteryResultResponseSchema(BaseModel):
    """Schema cho phản hồi kết quả xổ số"""
    id: int = Field(..., description="ID kết quả xổ số")
    lottery_type: str = Field(..., description="Tên loại xổ số")
    lottery_code: str = Field(..., description="Mã loại xổ số")
    region: Optional[str] = Field(None, description="Khu vực")
    draw_date: date = Field(..., description="Ngày quay thưởng")
    results: Dict[str, Any] = Field(..., description="Kết quả xổ số")
    formatted_results: str = Field(..., description="Kết quả xổ số đã định dạng")

class LotteryQueryParamsSchema(BaseModel):
    """Schema cho tham số truy vấn kết quả xổ số"""
    date: Optional[date] = Field(None, description="Ngày quay thưởng (YYYY-MM-DD)")
    type_code: Optional[str] = Field(None, description="Mã loại xổ số")
    limit: Optional[int] = Field(1, description="Số lượng kết quả tối đa", ge=1, le=10)

class LotteryTypesResponseSchema(BaseModel):
    """Schema cho phản hồi danh sách loại xổ số"""
    types: List[LotteryTypeSchema] = Field(..., description="Danh sách loại xổ số")

class LotteryResultsResponseSchema(BaseModel):
    """Schema cho phản hồi danh sách kết quả xổ số"""
    results: List[LotteryResultResponseSchema] = Field(..., description="Danh sách kết quả xổ số")
    count: int = Field(..., description="Số lượng kết quả")
