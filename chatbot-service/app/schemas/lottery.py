from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import date

class LotteryTypeBaseSchema(BaseModel):
    code: str = Field(..., description="Mã loại xổ số")
    name: str = Field(..., description="Tên loại xổ số")
    region: Optional[str] = Field(None, description="Khu vực")
    description: Optional[str] = Field(None, description="Mô tả")

class LotteryTypeCreateSchema(LotteryTypeBaseSchema):
    pass

class LotteryTypeSchema(LotteryTypeBaseSchema):
    id: int = Field(..., description="ID loại xổ số")

    class Config:
        from_attributes = True

class LotteryDrawBaseSchema(BaseModel):
    lottery_type_id: int = Field(..., description="ID loại xổ số")
    draw_date: date = Field(..., description="Ngày quay thưởng")
    results: Dict[str, Any] = Field(..., description="Kết quả xổ số dạng JSON")

class LotteryDrawCreateSchema(LotteryDrawBaseSchema):
    pass

class LotteryDrawSchema(LotteryDrawBaseSchema):
    id: int = Field(..., description="ID kết quả xổ số")

    class Config:
        from_attributes = True

class LotteryResultResponseSchema(BaseModel):
    id: int = Field(..., description="ID kết quả xổ số")
    lottery_type: str = Field(..., description="Tên loại xổ số")
    lottery_code: str = Field(..., description="Mã loại xổ số")
    region: Optional[str] = Field(None, description="Khu vực")
    draw_date: date = Field(..., description="Ngày quay thưởng")
    results: Dict[str, Any] = Field(..., description="Kết quả xổ số")
    formatted_results: str = Field(..., description="Kết quả xổ số đã định dạng")
