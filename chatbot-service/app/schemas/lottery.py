from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import date
import datetime

class LotteryTypeBase(BaseModel):
    """Schema cơ bản cho loại xổ số"""
    code: str = Field(..., description="Mã loại xổ số")
    name: str = Field(..., description="Tên loại xổ số")
    region: Optional[str] = Field(None, description="Khu vực")
    description: Optional[str] = Field(None, description="Mô tả")

class LotteryTypeCreate(LotteryTypeBase):
    """Schema cho việc tạo loại xổ số mới"""
    pass

class LotteryType(LotteryTypeBase):
    """Schema cho loại xổ số"""
    id: int = Field(..., description="ID loại xổ số")

    class Config:
        from_attributes = True

class LotteryDrawBase(BaseModel):
    """Schema cơ bản cho kết quả xổ số"""
    lottery_type_id: int = Field(..., description="ID loại xổ số")
    draw_date: datetime.date = Field(..., description="Ngày quay thưởng")
    results: Dict[str, Any] = Field(..., description="Kết quả xổ số dạng JSON")

class LotteryDrawCreate(LotteryDrawBase):
    """Schema cho việc tạo kết quả xổ số mới"""
    pass

class LotteryDraw(LotteryDrawBase):
    """Schema cho kết quả xổ số"""
    id: int = Field(..., description="ID kết quả xổ số")

    class Config:
        from_attributes = True

class LotteryDrawWithType(LotteryDraw):
    """Schema cho kết quả xổ số với thông tin loại xổ số"""
    lottery_type: LotteryType = Field(..., description="Thông tin loại xổ số")

    class Config:
        from_attributes = True

class LotteryResultResponse(BaseModel):
    """Schema cho phản hồi kết quả xổ số"""
    id: int = Field(..., description="ID kết quả xổ số")
    lottery_type: str = Field(..., description="Tên loại xổ số")
    lottery_code: str = Field(..., description="Mã loại xổ số")
    region: Optional[str] = Field(None, description="Khu vực")
    draw_date: datetime.date = Field(..., description="Ngày quay thưởng")
    results: Dict[str, Any] = Field(..., description="Kết quả xổ số")
    formatted_results: str = Field(..., description="Kết quả xổ số đã định dạng")

class LotteryQueryParams(BaseModel):
    """Schema cho tham số truy vấn kết quả xổ số"""
    draw_date: Optional[date] = Field(None, description="Ngày quay thưởng (YYYY-MM-DD)")
    type_code: Optional[str] = Field(None, description="Mã loại xổ số")
    limit: Optional[int] = Field(1, description="Số lượng kết quả tối đa", ge=1, le=10)

class LotteryTypesResponse(BaseModel):
    """Schema cho phản hồi danh sách loại xổ số"""
    types: List[LotteryType] = Field(..., description="Danh sách loại xổ số")

class LotteryResultsResponse(BaseModel):
    """Schema cho phản hồi danh sách kết quả xổ số"""
    results: List[LotteryResultResponse] = Field(..., description="Danh sách kết quả xổ số")
    count: int = Field(..., description="Số lượng kết quả")
