from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from datetime import date, datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.postgresql import get_db_session
from app.services.lottery import LotteryService
# TEMPORARILY COMMENTED OUT TO FIX RECURSION ERROR
# from app.schemas.lottery import (
#     LotteryTypeSchema,
#     LotteryResultResponseSchema,
#     LotteryTypesResponseSchema,
#     LotteryResultsResponseSchema
# )

router = APIRouter(tags=["lottery"])

@router.get("/types", response_model=LotteryTypesResponseSchema)
async def get_lottery_types(
    db: AsyncSession = Depends(get_db_session)
):
    """
    L<PERSON>y danh sách các loại xổ số
    """
    lottery_service = LotteryService(db)
    types = await lottery_service.get_lottery_types()

    return LotteryTypesResponseSchema(types=types)

@router.get("/results", response_model=LotteryResultsResponseSchema)
async def get_lottery_results(
    date: Optional[date] = Query(None, description="Ngày quay thưởng (YYYY-MM-DD)"),
    type_code: Optional[str] = Query(None, description="Mã loại xổ số"),
    limit: int = Query(1, description="Số lượng kết quả tối đa", ge=1, le=10),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Lấy kết quả xổ số theo ngày và loại (tùy chọn)

    - Nếu không cung cấp ngày, sẽ lấy kết quả mới nhất
    - Nếu không cung cấp loại, sẽ lấy tất cả các loại
    """
    lottery_service = LotteryService(db)

    if date:
        # Lấy kết quả theo ngày
        results = await lottery_service.get_lottery_results_by_date(date, type_code)
    else:
        # Lấy kết quả mới nhất
        results = await lottery_service.get_latest_lottery_results(type_code, limit)

    if not results:
        if date:
            message = f"Không tìm thấy kết quả xổ số cho ngày {date.strftime('%d/%m/%Y')}"
            if type_code:
                message += f" và loại {type_code}"
        else:
            message = "Không tìm thấy kết quả xổ số mới nhất"
            if type_code:
                message += f" cho loại {type_code}"

        return LotteryResultsResponseSchema(results=[], count=0)

    return LotteryResultsResponseSchema(results=results, count=len(results))

@router.get("/latest", response_model=LotteryResultsResponseSchema)
async def get_latest_lottery_results(
    type_code: Optional[str] = Query(None, description="Mã loại xổ số"),
    limit: int = Query(1, description="Số lượng kết quả tối đa", ge=1, le=10),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Lấy kết quả xổ số mới nhất
    """
    lottery_service = LotteryService(db)
    results = await lottery_service.get_latest_lottery_results(type_code, limit)

    if not results:
        message = "Không tìm thấy kết quả xổ số mới nhất"
        if type_code:
            message += f" cho loại {type_code}"

        return LotteryResultsResponseSchema(results=[], count=0)

    return LotteryResultsResponseSchema(results=results, count=len(results))

@router.get("/by-date/{date}", response_model=LotteryResultsResponseSchema)
async def get_lottery_results_by_date(
    date: date,
    type_code: Optional[str] = Query(None, description="Mã loại xổ số"),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Lấy kết quả xổ số theo ngày cụ thể
    """
    lottery_service = LotteryService(db)
    results = await lottery_service.get_lottery_results_by_date(date, type_code)

    if not results:
        message = f"Không tìm thấy kết quả xổ số cho ngày {date.strftime('%d/%m/%Y')}"
        if type_code:
            message += f" và loại {type_code}"

        return LotteryResultsResponseSchema(results=[], count=0)

    return LotteryResultsResponseSchema(results=results, count=len(results))

@router.get("/today", response_model=LotteryResultsResponseSchema)
async def get_today_lottery_results(
    type_code: Optional[str] = Query(None, description="Mã loại xổ số"),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Lấy kết quả xổ số của ngày hôm nay
    """
    today = date.today()
    lottery_service = LotteryService(db)
    results = await lottery_service.get_lottery_results_by_date(today, type_code)

    if not results:
        message = f"Không tìm thấy kết quả xổ số cho ngày hôm nay ({today.strftime('%d/%m/%Y')})"
        if type_code:
            message += f" và loại {type_code}"

        return LotteryResultsResponseSchema(results=[], count=0)

    return LotteryResultsResponseSchema(results=results, count=len(results))

@router.get("/yesterday", response_model=LotteryResultsResponseSchema)
async def get_yesterday_lottery_results(
    type_code: Optional[str] = Query(None, description="Mã loại xổ số"),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Lấy kết quả xổ số của ngày hôm qua
    """
    yesterday = date.today() - datetime.timedelta(days=1)
    lottery_service = LotteryService(db)
    results = await lottery_service.get_lottery_results_by_date(yesterday, type_code)

    if not results:
        message = f"Không tìm thấy kết quả xổ số cho ngày hôm qua ({yesterday.strftime('%d/%m/%Y')})"
        if type_code:
            message += f" và loại {type_code}"

        return LotteryResultsResponseSchema(results=[], count=0)

    return LotteryResultsResponseSchema(results=results, count=len(results))
