from fastapi import APIRout<PERSON>, Depends, HTTPEx<PERSON>, BackgroundTasks
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.chat import ChatRequestSchema, ChatResponseSchema, ChatHistorySchema
from app.services.chat_orchestrator import ChatOrchestrator
from app.services.chatbot import ChatbotService
#from app.services.llm_client import LLMClientService
#from app.services.chat_context import ChatContextService
from app.cache.redis_client import CacheService
from app.db.postgresql import get_db_session
from app.config import settings

router = APIRouter(tags=["chatbot"])

@router.post("/chat", response_model=ChatResponseSchema)
async def chat(
    request: ChatRequestSchema,
    db: AsyncSession = Depends(get_db_session)
):
    """
    Xử lý yêu cầu chat và trả về phản hồi - sử dụng ChatOrchestrator tối <PERSON>u
    """
    try:
        orchestrator = ChatOrchestrator(db)
        response = await orchestrator.process_chat(
            user_id=request.user_id,
            message=request.message,
            context=request.context
        )
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history/{user_id}", response_model=List[ChatHistorySchema])
async def get_chat_history(
    user_id: str,
    limit: Optional[int] = 10,
    db: AsyncSession = Depends(get_db_session),
    chatbot_service: ChatbotService = Depends(lambda: ChatbotService(db=db))
):
    """
    Lấy lịch sử chat của một người dùng cụ thể
    """
    try:
        history = await chatbot_service.get_chat_history(user_id, limit)
        return history
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/history/{user_id}")
async def clear_chat_history(
    user_id: str,
    db: AsyncSession = Depends(get_db_session),
    chatbot_service: ChatbotService = Depends(lambda: ChatbotService(db=db))
):
    """
    Xóa lịch sử chat của một người dùng cụ thể
    """
    try:
        await chatbot_service.clear_chat_history(user_id)
        return {"status": "success", "message": "Lịch sử chat đã được xóa"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/llm/status", tags=["llm"])
async def check_llm_status():
    """
    Kiểm tra trạng thái kết nối đến LLM Core
    """
    try:
        llm_client = LLMClientService()
        is_healthy = await llm_client.is_healthy(force_check=True)

        if is_healthy:
            # Lấy thông tin model
            model_info = await llm_client.get_model_info()

            return {
                "status": "ok",
                "connected": True,
                "model": model_info.get("model", "unknown"),
                "capabilities": model_info.get("capabilities", []),
                "parameters": model_info.get("parameters", {})
            }
        else:
            return {
                "status": "error",
                "connected": False,
                "message": "Không thể kết nối đến LLM Core"
            }
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail={
                "status": "error",
                "connected": False,
                "message": f"Lỗi khi kiểm tra kết nối LLM: {str(e)}"
            }
        )

@router.post("/llm/test", tags=["llm"])
async def test_llm_query(prompt: str = "Xin chào, bạn là ai?"):
    """
    Kiểm tra truy vấn LLM với một prompt đơn giản
    """
    try:
        llm_client = LLMClientService()

        # Kiểm tra kết nối
        is_healthy = await llm_client.is_healthy()
        if not is_healthy:
            raise HTTPException(
                status_code=503,
                detail={
                    "status": "error",
                    "message": "LLM Core không khả dụng"
                }
            )

        # Gửi truy vấn test
        response = await llm_client.chat_completion(
            prompt=prompt,
            system_message="Bạn là trợ lý AI của XỔ SỐ TV. Hãy trả lời ngắn gọn và thân thiện.",
            use_cache=False  # Không sử dụng cache cho truy vấn test
        )

        return {
            "status": "ok",
            "prompt": prompt,
            "response": response
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Lỗi khi test LLM: {str(e)}"
            }
        )

@router.get("/stats/hot-queries", tags=["stats"])
async def get_hot_queries(limit: int = 10):
    """
    Lấy danh sách các truy vấn phổ biến nhất
    """
    try:
        hot_queries = await CacheService.get_hot_queries(limit)
        return {
            "status": "ok",
            "hot_queries": hot_queries,
            "threshold": settings.hot_query_threshold,
            "window": settings.hot_query_window
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Lỗi khi lấy hot queries: {str(e)}"
            }
        )

@router.get("/context/{user_id}", tags=["context"])
async def get_user_context(user_id: str):
    """
    Lấy context chat của người dùng
    """
    try:
        context = await ChatContextService.get_context(user_id)
        return {
            "status": "ok",
            "user_id": user_id,
            "context": context
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Lỗi khi lấy context: {str(e)}"
            }
        )

@router.delete("/context/{user_id}", tags=["context"])
async def clear_user_context(user_id: str):
    """
    Xóa context chat của người dùng
    """
    try:
        success = await ChatContextService.clear_history(user_id)
        return {
            "status": "ok" if success else "error",
            "user_id": user_id,
            "message": "Context đã được xóa" if success else "Không thể xóa context"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Lỗi khi xóa context: {str(e)}"
            }
        )
