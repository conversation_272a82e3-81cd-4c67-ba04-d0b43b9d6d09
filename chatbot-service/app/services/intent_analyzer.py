import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, date
from pydantic import BaseModel, Field

from app.services.llm_client import LLMClientService
from app.utils.logger import get_logger

logger = get_logger("intent_analyzer")

class EntityExtractionSchema(BaseModel):
    """Thông tin được trích xuất từ câu hỏi người dùng"""
    lottery_type: Optional[str] = Field(None, description="Loại xổ số: mb, mt, mn")
    date: Optional[str] = Field(None, description="<PERSON><PERSON><PERSON> cụ thể (YYYY-MM-DD)")
    date_range: Optional[Dict[str, str]] = Field(None, description="Khoảng thời gian")
    numbers: Optional[List[str]] = Field(None, description="Các số cần kiểm tra")
    prize_type: Optional[str] = Field(None, description="Loại giải: special, first, second, etc.")
    period: Optional[str] = Field(None, description="Khoảng thời gian phân tích: week, month, year")
    analysis_type: Optional[str] = Field(None, description="Loại phân tích: frequency, hot_cold, prediction")

class IntentResultSchema(BaseModel):
    """Kết quả phân tích ý định"""
    intent: str = Field(..., description="Ý định chính")
    sub_intent: Optional[str] = Field(None, description="Ý định phụ")
    entities: EntityExtractionSchema = Field(default_factory=EntityExtractionSchema)
    confidence: float = Field(0.0, description="Độ tin cậy (0-1)")
    reasoning: Optional[str] = Field(None, description="Lý do phân tích")

class IntentAnalyzer:
    """
    Phân tích ý định người dùng sử dụng mô hình ngôn ngữ lớn (LLM)
    """

    def __init__(self):
        self.llm_client = LLMClientService()

        # Định nghĩa các ý định chính và ý định phụ
        self.intent_definitions = {
            "lottery_query": {
                "description": "Tra cứu kết quả xổ số",
                "sub_intents": [
                    "specific_date",      # Ngày cụ thể
                    "latest_result",      # Kết quả mới nhất
                    "date_range",         # Khoảng thời gian
                    "check_numbers"       # Kiểm tra vé số
                ]
            },
            "statistics": {
                "description": "Thống kê và phân tích dữ liệu xổ số",
                "sub_intents": [
                    "frequency_analysis", # Phân tích tần suất
                    "hot_cold_numbers",   # Số nóng/lạnh
                    "head_tail_stats",    # Thống kê đầu đuôi
                    "prize_statistics"    # Thống kê theo giải
                ]
            },
            "prediction": {
                "description": "Soi cầu và dự đoán",
                "sub_intents": [
                    "next_draw",          # Dự đoán kỳ tiếp theo
                    "lucky_numbers",      # Số may mắn
                    "pattern_analysis",   # Phân tích quy luật
                    "expert_prediction"   # Dự đoán chuyên gia
                ]
            },
            "general_chat": {
                "description": "Trò chuyện chung",
                "sub_intents": [
                    "greeting",           # Chào hỏi
                    "help",              # Yêu cầu trợ giúp
                    "lottery_info",      # Thông tin về xổ số
                    "other"              # Khác
                ]
            }
        }

    async def analyze_intent(self, message: str, context: Optional[Dict] = None) -> IntentResultSchema:
        """
        Phân tích ý định từ tin nhắn của người dùng

        Args:
            message: Tin nhắn của người dùng
            context: Context từ cuộc hội thoại trước

        Returns:
            IntentResultSchema: Kết quả phân tích ý định
        """
        try:
            # Tạo prompt cho LLM
            prompt = self._create_intent_analysis_prompt(message, context)

            # Gọi LLM để phân tích
            response = await self.llm_client.chat_completion(
                prompt=prompt,
                temperature=0.1,  # Thấp để đảm bảo consistency
                max_tokens=300,
                use_cache=True
            )

            # Parse kết quả JSON
            result_data = self._parse_llm_response(response)

            # Validate và tạo IntentResultSchema
            intent_result = IntentResultSchema(**result_data)

            logger.info(f"Intent analyzed: {intent_result.intent}/{intent_result.sub_intent} "
                       f"(confidence: {intent_result.confidence:.2f})")

            return intent_result

        except Exception as e:
            logger.error(f"Lỗi khi phân tích ý định: {str(e)}", exc_info=True)

            # Fallback: sử dụng rule-based đơn giản
            return self._fallback_intent_detection(message)

    def _create_intent_analysis_prompt(self, message: str, context: Optional[Dict] = None) -> str:
        """Tạo prompt cho LLM để phân tích ý định"""

        context_info = ""
        if context:
            context_info = f"\nContext cuộc hội thoại: {json.dumps(context, ensure_ascii=False)}"

        prompt = f"""
Bạn là chuyên gia phân tích ý định cho hệ thống xổ số. Hãy phân tích câu hỏi sau và trả về JSON chính xác:

Câu hỏi: "{message}"{context_info}

Các loại ý định có thể:
1. lottery_query: Tra cứu kết quả xổ số
   - specific_date: Ngày cụ thể
   - latest_result: Kết quả mới nhất
   - date_range: Khoảng thời gian
   - check_numbers: Kiểm tra vé số

2. statistics: Thống kê và phân tích
   - frequency_analysis: Phân tích tần suất
   - hot_cold_numbers: Số nóng/lạnh
   - head_tail_stats: Thống kê đầu đuôi
   - prize_statistics: Thống kê theo giải

3. prediction: Soi cầu và dự đoán
   - next_draw: Dự đoán kỳ tiếp theo
   - lucky_numbers: Số may mắn
   - pattern_analysis: Phân tích quy luật
   - expert_prediction: Dự đoán chuyên gia

4. general_chat: Trò chuyện chung
   - greeting: Chào hỏi
   - help: Yêu cầu trợ giúp
   - lottery_info: Thông tin về xổ số
   - other: Khác

Trả về JSON theo format:
{{
    "intent": "lottery_query|statistics|prediction|general_chat",
    "sub_intent": "specific_date|latest_result|...",
    "entities": {{
        "lottery_type": "mb|mt|mn|null",
        "date": "YYYY-MM-DD|null",
        "date_range": {{"start": "YYYY-MM-DD", "end": "YYYY-MM-DD"}}|null,
        "numbers": ["12345", "67890"]|null,
        "prize_type": "special|first|second|third|fourth|fifth|sixth|seventh|null",
        "period": "week|month|year|null",
        "analysis_type": "frequency|hot_cold|prediction|null"
    }},
    "confidence": 0.95,
    "reasoning": "Lý do phân tích ngắn gọn"
}}

Lưu ý:
- Nếu không rõ loại xổ số, mặc định là "mb" (miền Bắc)
- Ngày phải theo format YYYY-MM-DD
- Confidence từ 0.0 đến 1.0
- Reasoning giải thích ngắn gọn tại sao chọn intent này
"""

        return prompt

    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """Parse response từ LLM thành dictionary"""
        try:
            # Tìm JSON trong response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                raise ValueError("Không tìm thấy JSON trong response")

            json_str = response[start_idx:end_idx]
            result = json.loads(json_str)

            # Validate required fields
            if 'intent' not in result:
                raise ValueError("Missing required field: intent")

            # Set default values
            result.setdefault('entities', {})
            result.setdefault('confidence', 0.5)
            result.setdefault('reasoning', '')

            return result

        except (json.JSONDecodeError, ValueError) as e:
            logger.warning(f"Không thể parse JSON từ LLM response: {str(e)}")
            logger.debug(f"LLM response: {response}")
            raise

    def _fallback_intent_detection(self, message: str) -> IntentResultSchema:
        """Fallback intent detection sử dụng rule-based"""
        message_lower = message.lower()

        # Keywords cho từng intent
        lottery_keywords = ["kết quả", "xổ số", "quay thưởng", "giải", "về", "ra"]
        stats_keywords = ["thống kê", "tần suất", "số nóng", "số lạnh", "phân tích"]
        prediction_keywords = ["soi cầu", "dự đoán", "may mắn", "cầu", "bạch thủ"]

        # Detect intent
        if any(keyword in message_lower for keyword in lottery_keywords):
            intent = "lottery_query"
            sub_intent = "specific_date" if any(d in message_lower for d in ["ngày", "hôm", "tuần"]) else "latest_result"
        elif any(keyword in message_lower for keyword in stats_keywords):
            intent = "statistics"
            sub_intent = "frequency_analysis"
        elif any(keyword in message_lower for keyword in prediction_keywords):
            intent = "prediction"
            sub_intent = "next_draw"
        else:
            intent = "general_chat"
            sub_intent = "other"

        return IntentResultSchema(
            intent=intent,
            sub_intent=sub_intent,
            entities=EntityExtractionSchema(),
            confidence=0.6,
            reasoning="Fallback rule-based detection"
        )

    def get_intent_definitions(self) -> Dict[str, Any]:
        """Lấy định nghĩa các intent"""
        return self.intent_definitions
