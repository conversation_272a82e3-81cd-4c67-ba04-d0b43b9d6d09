import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.config import settings
from app.cache.redis_client import CacheService, generate_cache_key

logger = logging.getLogger("chat-context")

class ChatContextService:
    """
    Dịch vụ quản lý context chat
    """
    
    @staticmethod
    async def get_context(user_id: str) -> Dict[str, Any]:
        """
        Lấy context chat của người dùng
        
        Args:
            user_id: ID của người dùng
            
        Returns:
            Dict[str, Any]: Context chat của người dùng
        """
        context_key = generate_cache_key(CacheService.CONTEXT_PREFIX, user_id)
        context_data = await CacheService.get_json(context_key)
        
        if context_data:
            logger.debug(f"Đã lấy context chat cho người dùng {user_id}")
            return context_data
        
        # Trả về context trống nếu không tìm thấy
        return {
            "history": [],
            "last_interaction": None,
            "metadata": {}
        }
    
    @staticmethod
    async def save_context(user_id: str, context: Dict[str, Any]) -> bool:
        """
        Lưu context chat của ngư<PERSON>i dùng
        
        Args:
            user_id: ID của người dùng
            context: Context chat cần lưu
            
        Returns:
            bool: True nếu lưu thành công, False nếu không
        """
        # Cập nhật thời gian tương tác cuối
        context["last_interaction"] = datetime.now().isoformat()
        
        # Lưu context vào Redis
        context_key = generate_cache_key(CacheService.CONTEXT_PREFIX, user_id)
        result = await CacheService.set_json(context_key, context, expire=settings.cache_context_ttl)
        
        if result:
            logger.debug(f"Đã lưu context chat cho người dùng {user_id}")
        else:
            logger.warning(f"Không thể lưu context chat cho người dùng {user_id}")
        
        return result
    
    @staticmethod
    async def add_to_history(user_id: str, message: str, response: str, intent: Optional[str] = None) -> bool:
        """
        Thêm một tương tác vào lịch sử chat
        
        Args:
            user_id: ID của người dùng
            message: Tin nhắn của người dùng
            response: Phản hồi của chatbot
            intent: Ý định được phát hiện (tùy chọn)
            
        Returns:
            bool: True nếu thêm thành công, False nếu không
        """
        # Lấy context hiện tại
        context = await ChatContextService.get_context(user_id)
        
        # Tạo mục lịch sử mới
        history_item = {
            "message": message,
            "response": response,
            "intent": intent,
            "timestamp": datetime.now().isoformat()
        }
        
        # Thêm vào lịch sử, giới hạn số lượng mục
        history = context.get("history", [])
        history.append(history_item)
        
        # Giữ tối đa 10 mục lịch sử gần nhất
        if len(history) > 10:
            history = history[-10:]
        
        context["history"] = history
        
        # Lưu context đã cập nhật
        return await ChatContextService.save_context(user_id, context)
    
    @staticmethod
    async def clear_history(user_id: str) -> bool:
        """
        Xóa lịch sử chat của người dùng
        
        Args:
            user_id: ID của người dùng
            
        Returns:
            bool: True nếu xóa thành công, False nếu không
        """
        # Lấy context hiện tại
        context = await ChatContextService.get_context(user_id)
        
        # Xóa lịch sử
        context["history"] = []
        
        # Lưu context đã cập nhật
        return await ChatContextService.save_context(user_id, context)
    
    @staticmethod
    async def set_metadata(user_id: str, key: str, value: Any) -> bool:
        """
        Đặt metadata cho context chat
        
        Args:
            user_id: ID của người dùng
            key: Khóa metadata
            value: Giá trị metadata
            
        Returns:
            bool: True nếu đặt thành công, False nếu không
        """
        # Lấy context hiện tại
        context = await ChatContextService.get_context(user_id)
        
        # Cập nhật metadata
        if "metadata" not in context:
            context["metadata"] = {}
        
        context["metadata"][key] = value
        
        # Lưu context đã cập nhật
        return await ChatContextService.save_context(user_id, context)
    
    @staticmethod
    async def get_metadata(user_id: str, key: str, default: Any = None) -> Any:
        """
        Lấy metadata từ context chat
        
        Args:
            user_id: ID của người dùng
            key: Khóa metadata
            default: Giá trị mặc định nếu không tìm thấy
            
        Returns:
            Any: Giá trị metadata
        """
        # Lấy context hiện tại
        context = await ChatContextService.get_context(user_id)
        
        # Lấy metadata
        metadata = context.get("metadata", {})
        return metadata.get(key, default)
    
    @staticmethod
    async def get_recent_history(user_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Lấy lịch sử chat gần đây của người dùng
        
        Args:
            user_id: ID của người dùng
            limit: Số lượng mục lịch sử tối đa
            
        Returns:
            List[Dict[str, Any]]: Danh sách các mục lịch sử
        """
        # Lấy context hiện tại
        context = await ChatContextService.get_context(user_id)
        
        # Lấy lịch sử
        history = context.get("history", [])
        
        # Trả về số lượng mục lịch sử gần đây nhất
        return history[-limit:] if history else []
    
    @staticmethod
    async def format_context_for_llm(user_id: str, limit: int = 3) -> str:
        """
        Định dạng context chat để sử dụng trong prompt LLM
        
        Args:
            user_id: ID của người dùng
            limit: Số lượng tương tác gần đây để đưa vào context
            
        Returns:
            str: Context đã định dạng
        """
        # Lấy lịch sử gần đây
        history = await ChatContextService.get_recent_history(user_id, limit)
        
        if not history:
            return ""
        
        # Định dạng lịch sử thành chuỗi
        formatted_history = "Lịch sử trò chuyện gần đây:\n\n"
        
        for i, item in enumerate(history):
            formatted_history += f"Người dùng: {item['message']}\n"
            formatted_history += f"Chatbot: {item['response']}\n\n"
        
        return formatted_history
