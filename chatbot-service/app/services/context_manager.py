import json
import hashlib
from typing import Dict, Any, List, Optional, Tu<PERSON>
from datetime import datetime, timedelta
from pydantic import BaseModel

from app.models.user_query import UserQueryModel
from app.cache.redis_client import CacheService
from app.utils.logger import get_logger
from sqlalchemy.ext.asyncio import AsyncSession

logger = get_logger("context_manager")

class ContextItem(BaseModel):
    """Một item trong context"""
    message: str
    response: str
    intent: str
    timestamp: datetime
    relevance_score: float = 0.0

class ConversationContext(BaseModel):
    """Context của cuộc hội thoại"""
    user_id: str
    recent_messages: List[ContextItem] = []
    relevant_context: List[ContextItem] = []
    user_preferences: Dict[str, Any] = {}
    session_info: Dict[str, Any] = {}

class ContextManager:
    """
    Quản lý context thông minh cho cuộc hội thoại
    """

    def __init__(self, db: AsyncSession):
        self.db = db
        self.cache = CacheService

        # C<PERSON>u hình
        self.max_recent_messages = 10
        self.max_relevant_context = 5
        self.context_ttl = 3600  # 1 hour
        self.relevance_threshold = 0.7

    async def get_conversation_context(
        self,
        user_id: str,
        current_message: str,
        force_refresh: bool = False
    ) -> ConversationContext:
        """
        Lấy context cho cuộc hội thoại

        Args:
            user_id: ID người dùng
            current_message: Tin nhắn hiện tại
            force_refresh: Buộc refresh context từ database

        Returns:
            ConversationContext: Context của cuộc hội thoại
        """
        try:
            # Kiểm tra cache trước
            cache_key = f"context:{user_id}"

            if not force_refresh:
                cached_context = await self.cache.get_json(cache_key)
                if cached_context:
                    context = ConversationContext(**cached_context)

                    # Cập nhật relevant context với message hiện tại
                    context.relevant_context = await self._find_relevant_context(
                        user_id, current_message, context.recent_messages
                    )

                    return context

            # Tạo context mới từ database
            context = await self._build_context_from_db(user_id, current_message)

            # Cache context
            await self.cache.set_json(
                cache_key,
                context.model_dump(),
                expire=self.context_ttl
            )

            return context

        except Exception as e:
            logger.error(f"Lỗi khi lấy context cho user {user_id}: {str(e)}", exc_info=True)

            # Fallback: trả về context rỗng
            return ConversationContext(user_id=user_id)

    async def _build_context_from_db(self, user_id: str, current_message: str) -> ConversationContext:
        """Xây dựng context từ database"""

        # Lấy lịch sử chat gần đây
        recent_history = await self._get_recent_history(user_id)

        # Lấy context liên quan
        relevant_context = await self._find_relevant_context(
            user_id, current_message, recent_history
        )

        # Lấy preferences của user
        user_preferences = await self._get_user_preferences(user_id)

        # Tạo session info
        session_info = {
            "last_activity": datetime.now().isoformat(),
            "message_count": len(recent_history),
            "session_start": (datetime.now() - timedelta(hours=1)).isoformat()
        }

        return ConversationContext(
            user_id=user_id,
            recent_messages=recent_history,
            relevant_context=relevant_context,
            user_preferences=user_preferences,
            session_info=session_info
        )

    async def _get_recent_history(self, user_id: str) -> List[ContextItem]:
        """Lấy lịch sử chat gần đây"""
        try:
            # Query database để lấy lịch sử gần đây từ UserQuery model
            history_records = await UserQueryModel.get_history(
                self.db,
                user_id,
                limit=self.max_recent_messages
            )

            context_items = []
            for record in history_records:
                context_items.append(ContextItem(
                    message=record.query_text,
                    response=record.response_summary or "No response recorded",
                    intent=record.intent or "unknown",
                    timestamp=record.created_at,
                    relevance_score=1.0  # Recent messages có score cao
                ))

            return context_items

        except Exception as e:
            logger.error(f"Lỗi khi lấy lịch sử chat: {str(e)}")
            return []

    async def _find_relevant_context(
        self,
        user_id: str,
        current_message: str,
        recent_messages: List[ContextItem]
    ) -> List[ContextItem]:
        """
        Tìm context liên quan đến tin nhắn hiện tại
        """
        try:
            # Tính toán relevance score cho từng message
            relevant_items = []

            for item in recent_messages:
                score = await self._calculate_relevance_score(current_message, item)

                if score >= self.relevance_threshold:
                    item.relevance_score = score
                    relevant_items.append(item)

            # Sort theo relevance score và lấy top N
            relevant_items.sort(key=lambda x: x.relevance_score, reverse=True)
            return relevant_items[:self.max_relevant_context]

        except Exception as e:
            logger.error(f"Lỗi khi tìm relevant context: {str(e)}")
            return []

    async def _calculate_relevance_score(self, current_message: str, context_item: ContextItem) -> float:
        """
        Tính toán relevance score giữa tin nhắn hiện tại và context item
        """
        try:
            # Method 1: Keyword matching (simple)
            current_words = set(current_message.lower().split())
            context_words = set(context_item.message.lower().split())

            # Jaccard similarity
            intersection = len(current_words.intersection(context_words))
            union = len(current_words.union(context_words))

            if union == 0:
                return 0.0

            keyword_score = intersection / union

            # Method 2: Intent matching
            intent_score = 0.0
            # Nếu cùng intent thì có điểm cao hơn
            # (Cần implement intent detection cho current_message)

            # Method 3: Time decay
            time_diff = datetime.now() - context_item.timestamp
            time_decay = max(0, 1 - (time_diff.total_seconds() / 3600))  # Decay trong 1 giờ

            # Tổng hợp score
            final_score = (keyword_score * 0.6 + intent_score * 0.3 + time_decay * 0.1)

            return min(1.0, final_score)

        except Exception as e:
            logger.error(f"Lỗi khi tính relevance score: {str(e)}")
            return 0.0

    async def _get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Lấy preferences của user"""
        try:
            # Có thể lấy từ database hoặc cache
            cache_key = f"user_prefs:{user_id}"
            preferences = await self.cache.get_json(cache_key)

            if preferences:
                return preferences

            # Default preferences
            default_prefs = {
                "preferred_lottery_type": "mb",  # Mặc định miền Bắc
                "notification_enabled": True,
                "language": "vi",
                "timezone": "Asia/Ho_Chi_Minh"
            }

            # Cache default preferences
            await self.cache.set_json(cache_key, default_prefs, expire=86400)  # 24h

            return default_prefs

        except Exception as e:
            logger.error(f"Lỗi khi lấy user preferences: {str(e)}")
            return {}

    async def update_context(
        self,
        user_id: str,
        message: str,
        response: str,
        intent: str
    ) -> None:
        """
        Cập nhật context sau khi có tương tác mới
        """
        try:
            # Tạo context item mới
            new_item = ContextItem(
                message=message,
                response=response,
                intent=intent,
                timestamp=datetime.now(),
                relevance_score=1.0
            )

            # Lấy context hiện tại
            context = await self.get_conversation_context(user_id, message)

            # Thêm item mới vào đầu danh sách
            context.recent_messages.insert(0, new_item)

            # Giới hạn số lượng messages
            if len(context.recent_messages) > self.max_recent_messages:
                context.recent_messages = context.recent_messages[:self.max_recent_messages]

            # Cập nhật session info
            context.session_info["last_activity"] = datetime.now().isoformat()
            context.session_info["message_count"] = len(context.recent_messages)

            # Cache context đã cập nhật
            cache_key = f"context:{user_id}"
            await self.cache.set_json(
                cache_key,
                context.model_dump(),
                expire=self.context_ttl
            )

        except Exception as e:
            logger.error(f"Lỗi khi cập nhật context: {str(e)}", exc_info=True)

    async def clear_context(self, user_id: str) -> None:
        """Xóa context của user"""
        try:
            cache_key = f"context:{user_id}"
            await self.cache.delete(cache_key)

            logger.info(f"Đã xóa context cho user {user_id}")

        except Exception as e:
            logger.error(f"Lỗi khi xóa context: {str(e)}")

    def format_context_for_llm(self, context: ConversationContext) -> str:
        """
        Format context thành string để gửi cho LLM
        """
        try:
            context_parts = []

            # User preferences
            if context.user_preferences:
                prefs_str = ", ".join([f"{k}: {v}" for k, v in context.user_preferences.items()])
                context_parts.append(f"Preferences: {prefs_str}")

            # Recent relevant context
            if context.relevant_context:
                context_parts.append("Relevant conversation history:")
                for item in context.relevant_context[:3]:  # Top 3 most relevant
                    context_parts.append(f"- User: {item.message}")
                    context_parts.append(f"  Bot: {item.response[:100]}...")

            # Session info
            if context.session_info:
                context_parts.append(f"Session: {context.session_info.get('message_count', 0)} messages")

            return "\n".join(context_parts)

        except Exception as e:
            logger.error(f"Lỗi khi format context: {str(e)}")
            return ""
