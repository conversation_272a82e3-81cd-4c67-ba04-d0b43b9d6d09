import random
from typing import Dict, Any, List, Optional
from datetime import datetime, date, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.statistics import StatisticsService
from app.cache.multi_level_cache import multi_cache
from app.utils.logger import get_logger

logger = get_logger("prediction_service")

class PredictionService:
    """
    Service dự đoán và soi cầu xổ số
    """
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.statistics_service = StatisticsService(db)
        
        # Cache TTL
        self.cache_ttl = {
            'prediction': 1800,    # 30 minutes
            'lucky_numbers': 3600, # 1 hour
            'patterns': 7200       # 2 hours
        }
    
    async def predict_next_draw(
        self, 
        lottery_type: str,
        analysis_days: int = 30
    ) -> Dict[str, Any]:
        """
        Dự đoán kỳ quay tiếp theo
        
        Args:
            lottery_type: Loại xổ số (mb, mt, mn)
            analysis_days: S<PERSON> ngày phân tích (mặc định 30 ngày)
            
        Returns:
            Dict chứa d<PERSON> đo<PERSON>
        """
        try:
            # Kiểm tra cache
            cache_key = multi_cache.generate_cache_key(
                "next_draw_prediction", lottery_type, analysis_days
            )
            
            cached_result = await multi_cache.get(cache_key)
            if cached_result:
                return cached_result
            
            # Lấy dữ liệu thống kê
            end_date = date.today()
            start_date = end_date - timedelta(days=analysis_days)
            
            draws = await self.statistics_service.get_draws_in_period(
                lottery_type, start_date, end_date
            )
            
            if not draws:
                return {
                    "error": f"Không có dữ liệu để dự đoán cho {lottery_type}",
                    "lottery_type": lottery_type
                }
            
            # Phân tích tần suất
            frequency_analysis = await self.statistics_service.get_frequency_analysis(
                lottery_type, "month"
            )
            
            # Lấy số nóng và số lạnh
            hot_numbers = await self.statistics_service.get_hot_numbers(
                lottery_type, "month", limit=15
            )
            cold_numbers = await self.statistics_service.get_cold_numbers(
                lottery_type, "month", limit=15
            )
            
            # Phân tích chu kỳ
            cycle_analysis = self.statistics_service.analyze_cycles(draws, top_n=20)
            
            # Dự đoán các số có khả năng cao
            prediction_numbers = self._generate_prediction_numbers(
                hot_numbers, cold_numbers, cycle_analysis
            )
            
            # Dự đoán theo giải
            prize_predictions = self._predict_by_prizes(prediction_numbers)
            
            # Tạo phân tích chi tiết
            analysis = self._generate_prediction_analysis(
                lottery_type, hot_numbers, cold_numbers, cycle_analysis, analysis_days
            )
            
            result_data = {
                "lottery_type": lottery_type,
                "prediction_date": (date.today() + timedelta(days=1)).isoformat(),
                "analysis_period": f"{analysis_days} ngày",
                "confidence_level": "Trung bình",
                "hot_numbers": hot_numbers[:10],
                "cold_numbers": cold_numbers[:10],
                "cycle_numbers": [c.number for c in cycle_analysis.cycles[:5]],
                "predicted_numbers": prediction_numbers,
                "prize_predictions": prize_predictions,
                "analysis": analysis,
                "disclaimer": "Dự đoán chỉ mang tính chất tham khảo. Xổ số là trò chơi may rủi."
            }
            
            # Cache kết quả
            await multi_cache.set(
                cache_key,
                result_data,
                ttl_l1=300,  # 5 minutes
                ttl_l2=self.cache_ttl['prediction']
            )
            
            return result_data
            
        except Exception as e:
            logger.error(f"Lỗi khi dự đoán kỳ quay tiếp theo: {str(e)}", exc_info=True)
            return {
                "error": f"Không thể dự đoán: {str(e)}",
                "lottery_type": lottery_type
            }
    
    async def get_lucky_numbers(
        self, 
        lottery_type: str,
        count: int = 10
    ) -> Dict[str, Any]:
        """
        Lấy số may mắn
        
        Args:
            lottery_type: Loại xổ số
            count: Số lượng số may mắn
            
        Returns:
            Dict chứa số may mắn
        """
        try:
            cache_key = multi_cache.generate_cache_key(
                "lucky_numbers", lottery_type, count
            )
            
            cached_result = await multi_cache.get(cache_key)
            if cached_result:
                return cached_result
            
            # Lấy số nóng
            hot_numbers = await self.statistics_service.get_hot_numbers(
                lottery_type, "month", limit=20
            )
            
            # Lấy thống kê đầu đuôi
            head_tail_stats = await self.statistics_service.get_head_tail_statistics(
                lottery_type, "month"
            )
            
            # Tạo số may mắn dựa trên các yếu tố
            lucky_numbers = self._generate_lucky_numbers(
                hot_numbers, head_tail_stats, count
            )
            
            result_data = {
                "lottery_type": lottery_type,
                "lucky_numbers": lucky_numbers,
                "generation_method": "Dựa trên thống kê số nóng và đầu đuôi",
                "confidence": "Cao",
                "generated_at": datetime.now().isoformat()
            }
            
            # Cache kết quả
            await multi_cache.set(
                cache_key,
                result_data,
                ttl_l1=600,  # 10 minutes
                ttl_l2=self.cache_ttl['lucky_numbers']
            )
            
            return result_data
            
        except Exception as e:
            logger.error(f"Lỗi khi tạo số may mắn: {str(e)}")
            return {
                "error": f"Không thể tạo số may mắn: {str(e)}",
                "lottery_type": lottery_type
            }
    
    def _generate_prediction_numbers(
        self, 
        hot_numbers: List[str], 
        cold_numbers: List[str], 
        cycle_analysis: Any
    ) -> List[str]:
        """
        Tạo danh sách số dự đoán
        """
        prediction_numbers = []
        
        # 50% từ số nóng
        hot_count = min(5, len(hot_numbers))
        prediction_numbers.extend(hot_numbers[:hot_count])
        
        # 30% từ số lạnh (có thể sắp về)
        cold_count = min(3, len(cold_numbers))
        prediction_numbers.extend(cold_numbers[:cold_count])
        
        # 20% từ phân tích chu kỳ
        cycle_numbers = [c.number for c in cycle_analysis.cycles[:2]]
        prediction_numbers.extend(cycle_numbers)
        
        # Loại bỏ trùng lặp và giới hạn số lượng
        unique_numbers = list(dict.fromkeys(prediction_numbers))
        
        return unique_numbers[:10]
    
    def _predict_by_prizes(self, prediction_numbers: List[str]) -> Dict[str, List[str]]:
        """
        Dự đoán theo từng giải
        """
        if len(prediction_numbers) < 3:
            return {}
        
        # Shuffle để tạo tính ngẫu nhiên
        shuffled = prediction_numbers.copy()
        random.shuffle(shuffled)
        
        return {
            "special": shuffled[:1],      # Giải đặc biệt: 1 số
            "first": shuffled[1:2],       # Giải nhất: 1 số  
            "second": shuffled[2:4],      # Giải nhì: 2 số
            "third": shuffled[4:7],       # Giải ba: 3 số
            "others": shuffled[7:10]      # Các giải khác: 3 số
        }
    
    def _generate_lucky_numbers(
        self, 
        hot_numbers: List[str], 
        head_tail_stats: Dict[str, Any], 
        count: int
    ) -> List[str]:
        """
        Tạo số may mắn
        """
        lucky_numbers = []
        
        # Lấy một số từ hot numbers
        if hot_numbers:
            lucky_numbers.extend(hot_numbers[:count//2])
        
        # Tạo số dựa trên đầu đuôi nóng
        if head_tail_stats and "hot_heads" in head_tail_stats and "hot_tails" in head_tail_stats:
            hot_heads = head_tail_stats["hot_heads"]
            hot_tails = head_tail_stats["hot_tails"]
            
            # Tạo số 2 chữ số từ đầu đuôi nóng
            for _ in range(count - len(lucky_numbers)):
                if hot_heads and hot_tails:
                    head = random.choice(hot_heads)
                    tail = random.choice(hot_tails)
                    
                    # Tạo số 2 chữ số
                    if head != tail:
                        number = head + tail
                    else:
                        # Nếu đầu và đuôi giống nhau, tạo số khác
                        middle = str(random.randint(0, 9))
                        number = head + middle + tail
                    
                    if number not in lucky_numbers:
                        lucky_numbers.append(number)
        
        # Đảm bảo đủ số lượng
        while len(lucky_numbers) < count:
            # Tạo số ngẫu nhiên 2-3 chữ số
            random_number = str(random.randint(10, 999)).zfill(2)
            if random_number not in lucky_numbers:
                lucky_numbers.append(random_number)
        
        return lucky_numbers[:count]
    
    def _generate_prediction_analysis(
        self, 
        lottery_type: str, 
        hot_numbers: List[str], 
        cold_numbers: List[str], 
        cycle_analysis: Any, 
        analysis_days: int
    ) -> str:
        """
        Tạo phân tích dự đoán
        """
        lottery_name = {
            "mb": "miền Bắc",
            "mt": "miền Trung", 
            "mn": "miền Nam"
        }.get(lottery_type, lottery_type)
        
        analysis = f"""
🎯 DỰ ĐOÁN XỔ SỐ {lottery_name.upper()}

📊 Phân tích dựa trên {analysis_days} ngày gần đây:

🔥 SỐ NÓNG (xuất hiện nhiều):
{', '.join(hot_numbers[:5]) if hot_numbers else 'Không có dữ liệu'}

❄️ SỐ LẠNH (lâu chưa về):
{', '.join(cold_numbers[:5]) if cold_numbers else 'Không có dữ liệu'}

🔄 SỐ THEO CHU KỲ:
{', '.join([c.number for c in cycle_analysis.cycles[:3]]) if cycle_analysis.cycles else 'Không có dữ liệu'}

💡 KHUYẾN NGHỊ:
- Ưu tiên chọn số từ nhóm số nóng
- Kết hợp với 1-2 số lạnh có thể sắp về
- Theo dõi các số có chu kỳ đều đặn

⚠️ LƯU Ý:
Dự đoán chỉ mang tính chất tham khảo dựa trên thống kê.
Xổ số là trò chơi may rủi, hãy chơi có trách nhiệm!
        """.strip()
        
        return analysis
