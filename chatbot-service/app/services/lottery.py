import logging
from typing import List, Dict, Any, Optional
from datetime import date, datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from app.config import settings
from app.models.lottery import LotteryTypeModel, LotteryDrawModel
#from app.schemas.lottery_clean import LotteryTypeSchema, LotteryResultResponseSchema
from app.utils.helper import format_lottery_results
from app.cache.redis_client import CacheService, generate_cache_key

logger = logging.getLogger("lottery-service")

class LotteryService:
    """
    Dịch vụ xử lý kết quả xổ số
    """

    def __init__(self, db: AsyncSession):
        """Khởi tạo với SQLAlchemy session"""
        self.db = db

    async def get_lottery_types(self) -> List[LotteryTypeSchema]:
        """
        Lấy danh sách các loại xổ số

        Returns:
            List[LotteryTypeSchema]: <PERSON>h sách các loại xổ số
        """
        # Tạo cache key
        cache_key = generate_cache_key(CacheService.LOTTERY_PREFIX, "types")

        # Ki<PERSON>m tra cache
        cached_types = await CacheService.get_json(cache_key)
        if cached_types:
            logger.debug("Lấy danh sách loại xổ số từ cache")
            return [LotteryTypeSchema(**type_data) for type_data in cached_types]

        # Lấy từ database
        try:
            types = await LotteryTypeModel.get_all(self.db)

            # Chuyển đổi sang Pydantic schemas
            schema_types = []
            for type_model in types:
                schema_types.append(LotteryTypeSchema(
                    id=type_model.id,
                    code=type_model.code,
                    name=type_model.name,
                    region=type_model.region,
                    description=type_model.description
                ))

            # Cache kết quả (loại xổ số ít thay đổi nên cache lâu hơn)
            await CacheService.set_json(
                cache_key,
                [schema_type.model_dump() for schema_type in schema_types],
                expire=86400 * 7  # 7 ngày
            )

            return schema_types
        except Exception as e:
            logger.error(f"Lỗi khi lấy danh sách loại xổ số: {str(e)}", exc_info=True)
            return []

    async def get_lottery_type_by_code(self, code: str) -> Optional[LotteryTypeSchema]:
        """
        Lấy loại xổ số theo mã

        Args:
            code: Mã loại xổ số

        Returns:
            Optional[LotteryTypeSchema]: Loại xổ số nếu tìm thấy, None nếu không
        """
        # Tạo cache key
        cache_key = generate_cache_key(CacheService.LOTTERY_PREFIX, "type", code=code)

        # Kiểm tra cache
        cached_type = await CacheService.get_json(cache_key)
        if cached_type:
            logger.debug(f"Lấy loại xổ số {code} từ cache")
            return LotteryTypeSchema(**cached_type)

        # Lấy từ database
        try:
            lottery_type = await LotteryTypeModel.get_by_code(self.db, code)

            if lottery_type:
                # Chuyển đổi sang Pydantic schema
                schema_type = LotteryTypeSchema(
                    id=lottery_type.id,
                    code=lottery_type.code,
                    name=lottery_type.name,
                    region=lottery_type.region,
                    description=lottery_type.description
                )

                # Cache kết quả
                await CacheService.set_json(
                    cache_key,
                    schema_type.model_dump(),
                    expire=86400 * 7  # 7 ngày
                )

                return schema_type

            return None
        except Exception as e:
            logger.error(f"Lỗi khi lấy loại xổ số {code}: {str(e)}", exc_info=True)
            return None

    async def get_lottery_results_by_date(
        self,
        query_date: date,
        type_code: Optional[str] = None
    ) -> List[LotteryResultResponseSchema]:
        """
        Lấy kết quả xổ số theo ngày và loại (tùy chọn)

        Args:
            query_date: Ngày cần truy vấn
            type_code: Mã loại xổ số (tùy chọn)

        Returns:
            List[LotteryResultResponseSchema]: Danh sách kết quả xổ số
        """
        # Tạo cache key
        cache_key = generate_cache_key(
            CacheService.LOTTERY_PREFIX,
            "results",
            date=query_date.isoformat(),
            type=type_code or "all"
        )

        # Kiểm tra cache
        cached_results = await CacheService.get_json(cache_key)
        if cached_results:
            logger.debug(f"Lấy kết quả xổ số ngày {query_date} từ cache")
            return [LotteryResultResponseSchema(**result) for result in cached_results]

        # Lấy từ database
        try:
            # Lấy loại xổ số nếu có
            lottery_type_id = None
            if type_code:
                lottery_type = await self.get_lottery_type_by_code(type_code)
                if lottery_type:
                    lottery_type_id = lottery_type.id

            # Lấy kết quả xổ số
            draws = await LotteryDrawModel.get_by_date(self.db, query_date, lottery_type_id)

            # Chuyển đổi sang schema response
            results = []
            for draw in draws:
                # Lấy thông tin lottery_type riêng biệt để tránh circular reference
                from sqlalchemy import select
                result = await self.db.execute(
                    select(LotteryTypeModel).where(LotteryTypeModel.id == draw.lottery_type_id)
                )
                lottery_type = result.scalar_one_or_none()

                if lottery_type:
                    formatted_results = format_lottery_results(
                        lottery_type.name,
                        draw.draw_date,
                        draw.results
                    )

                    results.append(LotteryResultResponseSchema(
                        id=draw.id,
                        lottery_type=lottery_type.name,
                        lottery_code=lottery_type.code,
                        region=lottery_type.region,
                        draw_date=draw.draw_date,
                        results=draw.results,
                        formatted_results=formatted_results
                    ))

            # Cache kết quả
            if results:
                await CacheService.set_json(
                    cache_key,
                    [result.model_dump() for result in results],
                    expire=settings.cache_lottery_ttl
                )

            return results
        except Exception as e:
            logger.error(f"Lỗi khi lấy kết quả xổ số ngày {query_date}: {str(e)}", exc_info=True)
            return []

    async def get_latest_lottery_results(
        self,
        type_code: Optional[str] = None,
        limit: int = 1
    ) -> List[LotteryResultResponseSchema]:
        """
        Lấy kết quả xổ số mới nhất

        Args:
            type_code: Mã loại xổ số (tùy chọn)
            limit: Số lượng kết quả tối đa

        Returns:
            List[LotteryResultResponseSchema]: Danh sách kết quả xổ số
        """
        # Tạo cache key
        cache_key = generate_cache_key(
            CacheService.LOTTERY_PREFIX,
            "latest",
            type=type_code or "all",
            limit=limit
        )

        # Kiểm tra cache
        cached_results = await CacheService.get_json(cache_key)
        if cached_results:
            logger.debug(f"Lấy kết quả xổ số mới nhất từ cache")
            return [LotteryResultResponseSchema(**result) for result in cached_results]

        # Lấy từ database
        try:
            # Lấy loại xổ số nếu có
            lottery_type_id = None
            if type_code:
                lottery_type = await self.get_lottery_type_by_code(type_code)
                if lottery_type:
                    lottery_type_id = lottery_type.id

            # Lấy kết quả xổ số mới nhất
            draws = await LotteryDrawModel.get_latest(self.db, lottery_type_id, limit)

            # Chuyển đổi sang schema response
            results = []
            for draw in draws:
                # Lấy thông tin lottery_type riêng biệt để tránh circular reference
                from sqlalchemy import select
                result = await self.db.execute(
                    select(LotteryTypeModel).where(LotteryTypeModel.id == draw.lottery_type_id)
                )
                lottery_type = result.scalar_one_or_none()

                if lottery_type:
                    formatted_results = format_lottery_results(
                        lottery_type.name,
                        draw.draw_date,
                        draw.results
                    )

                    results.append(LotteryResultResponseSchema(
                        id=draw.id,
                        lottery_type=lottery_type.name,
                        lottery_code=lottery_type.code,
                        region=lottery_type.region,
                        draw_date=draw.draw_date,
                        results=draw.results,
                        formatted_results=formatted_results
                    ))

            # Cache kết quả
            if results:
                await CacheService.set_json(
                    cache_key,
                    [result.model_dump() for result in results],
                    expire=3600  # Cache 1 giờ cho kết quả mới nhất
                )

            return results
        except Exception as e:
            logger.error(f"Lỗi khi lấy kết quả xổ số mới nhất: {str(e)}", exc_info=True)
            return []

    async def get_results_for_date_range(
        self,
        start_date: date,
        end_date: date,
        type_code: Optional[str] = None
    ) -> Dict[date, List[LotteryResultResponseSchema]]:
        """
        Lấy kết quả xổ số trong khoảng thời gian

        Args:
            start_date: Ngày bắt đầu
            end_date: Ngày kết thúc
            type_code: Mã loại xổ số (tùy chọn)

        Returns:
            Dict[date, List[LotteryResultResponseSchema]]: Kết quả xổ số theo ngày
        """
        results = {}
        current_date = start_date

        while current_date <= end_date:
            day_results = await self.get_lottery_results_by_date(current_date, type_code)
            if day_results:
                results[current_date] = day_results

            current_date += timedelta(days=1)

        return results
