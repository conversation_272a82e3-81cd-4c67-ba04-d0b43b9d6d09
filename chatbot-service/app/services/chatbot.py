import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from app.config import settings
from app.db.postgresql import get_db_session
from app.cache.redis_client import CacheService, generate_cache_key
from app.utils.helper import detect_intent, extract_date_from_message
from app.services.llm_client import LLMClientService, LLMMessage
from app.services.chat_context import ChatContextService
#from app.services.lottery import LotteryService
from app.schemas.chat import ChatResponseSchema, ChatHistorySchema
from app.models.user_query import UserQueryModel

logger = logging.getLogger("chatbot-service")

class ChatbotService:
    """
    Dịch vụ xử lý tương tác chatbot
    """

    def __init__(self, db: AsyncSession = None):
        """Khởi tạo với SQLAlchemy session (tùy chọn)"""
        self.db = db

    async def get_db(self) -> AsyncSession:
        """Lấy SQLAlchemy session nếu chưa có"""
        if not self.db:
            self.db = await get_db_session()
        return self.db

    async def process_chat(
        self,
        user_id: str,
        message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> ChatResponseSchema:
        """
        Xử lý tin nhắn chat và trả về phản hồi
        """
        logger.info(f"Xử lý tin nhắn từ người dùng {user_id}: {message[:50]}...")

        # Theo dõi truy vấn để xác định hot queries
        await CacheService.track_query(message)

        # Kiểm tra xem có phải là hot query không
        hot_response = await CacheService.get_hot_response(message)
        if hot_response:
            logger.debug(f"Tìm thấy phản hồi cho hot query: {message[:50]}...")

            # Phát hiện ý định để ghi log
            intent = detect_intent(message)

            # Ghi lại tương tác
            await self._log_interaction(user_id, message, hot_response, intent)

            # Cập nhật context chat
            await ChatContextService.add_to_history(user_id, message, hot_response, intent)

            # Trả về phản hồi
            return ChatResponseSchema(
                response=hot_response,
                timestamp=datetime.now().isoformat(),
                intent=intent
            )

        # Phát hiện ý định của người dùng
        intent = detect_intent(message)
        logger.debug(f"Ý định được phát hiện: {intent}")

        # Tạo cache key dựa trên nội dung tin nhắn và người dùng
        cache_key = generate_cache_key(CacheService.CHAT_PREFIX, user_id, message=message)

        # Kiểm tra cache cho các truy vấn tương tự
        cached_response = await CacheService.get_json(cache_key)

        if cached_response:
            # Trả về phản hồi đã cache nếu có
            logger.debug(f"Tìm thấy phản hồi trong cache cho key: {cache_key}")

            # Ghi lại tương tác
            await self._log_interaction(user_id, message, cached_response["response"], intent)

            # Cập nhật context chat
            await ChatContextService.add_to_history(user_id, message, cached_response["response"], intent)

            return ChatResponseSchema(**cached_response)

        # Lấy context chat nếu cần
        chat_context = None
        if not context:
            # Lấy context từ cache
            formatted_context = await ChatContextService.format_context_for_llm(user_id)
            if formatted_context:
                chat_context = {"chat_history": formatted_context}
        else:
            chat_context = context

        # Xử lý dựa trên ý định
        try:
            if intent == "result_query":
                # Truy vấn kết quả xổ số từ cơ sở dữ liệu
                logger.debug(f"Xử lý truy vấn kết quả xổ số: {message[:50]}...")
                response_text = await self._process_lottery_query(message)
            else:
                # Gửi đến LLM để xử lý chat chung
                logger.debug(f"Gửi tin nhắn đến LLM: {message[:50]}...")
                response_text = await self._query_llm(message, chat_context)
        except Exception as e:
            logger.error(f"Lỗi khi xử lý tin nhắn: {str(e)}", exc_info=True)
            response_text = f"Xin lỗi, tôi đang gặp sự cố khi xử lý yêu cầu của bạn. Vui lòng thử lại sau."

        # Tạo phản hồi
        response = ChatResponseSchema(
            response=response_text,
            timestamp=datetime.now().isoformat(),
            intent=intent
        )

        # Lưu vào cache
        await CacheService.set_json(
            cache_key,
            response.model_dump(),
            expire=settings.cache_chat_ttl
        )

        # Kiểm tra và cache cho hot query nếu cần
        await CacheService.cache_hot_response(message, response_text)

        # Ghi lại tương tác
        await self._log_interaction(user_id, message, response_text, intent)

        # Cập nhật context chat
        await ChatContextService.add_to_history(user_id, message, response_text, intent)

        logger.info(f"Phản hồi đã được gửi đến người dùng {user_id}")
        return response

    async def get_chat_history(self, user_id: str, limit: int = 10) -> List[ChatHistorySchema]:
        """
        Lấy lịch sử chat của một người dùng cụ thể
        """
        logger.info(f"Lấy lịch sử chat cho người dùng {user_id}, giới hạn {limit} mục")

        # Kiểm tra cache trước
        cache_key = generate_cache_key(CacheService.HISTORY_PREFIX, user_id, limit=limit)
        cached_history = await CacheService.get_json(cache_key)

        if cached_history:
            logger.debug(f"Tìm thấy lịch sử chat trong cache cho người dùng {user_id}")
            return [ChatHistorySchema(**item) for item in cached_history]

        try:
            # Thử lấy từ context cache trước
            context_history = await ChatContextService.get_recent_history(user_id, limit)
            if context_history and len(context_history) > 0:
                logger.debug(f"Lấy lịch sử chat từ context cho người dùng {user_id}")

                history = []
                for item in context_history:
                    history.append(ChatHistorySchema(
                        message=item["message"],
                        response=item["response"],
                        intent=item.get("intent"),
                        timestamp=item["timestamp"]
                    ))

                # Lưu vào cache
                await CacheService.set_json(
                    cache_key,
                    [h.model_dump() for h in history],
                    expire=300  # Cache 5 phút
                )

                return history

            # Nếu không có trong context cache, lấy từ database
            db = await self.get_db()
            queries = await UserQueryModel.get_history(db, user_id, limit)

            history = []
            for query in queries:
                history.append(ChatHistorySchema(
                    message=query.query_text,
                    response=query.response_summary,
                    intent=query.intent,
                    timestamp=query.created_at.isoformat()
                ))

            # Lưu vào cache
            await CacheService.set_json(
                cache_key,
                [h.model_dump() for h in history],
                expire=300  # Cache 5 phút
            )

            return history
        except Exception as e:
            logger.error(f"Lỗi khi lấy lịch sử chat: {str(e)}", exc_info=True)
            return []

    async def clear_chat_history(self, user_id: str) -> None:
        """
        Xóa lịch sử chat của một người dùng cụ thể
        """
        logger.info(f"Xóa lịch sử chat cho người dùng {user_id}")

        try:
            # Xóa dữ liệu sử dụng SQLAlchemy ORM
            db = await self.get_db()
            await UserQueryModel.clear_history(db, user_id)

            # Xóa context chat
            await ChatContextService.clear_history(user_id)

            # Xóa cache liên quan
            # 1. Xóa cache lịch sử
            history_pattern = generate_cache_key(CacheService.HISTORY_PREFIX, user_id, "*")
            history_keys = await CacheService.keys(history_pattern)
            if history_keys:
                for key in history_keys:
                    await CacheService.delete(key)
                logger.debug(f"Đã xóa {len(history_keys)} khóa cache lịch sử chat")

            # 2. Xóa cache chat
            chat_pattern = generate_cache_key(CacheService.CHAT_PREFIX, user_id, "*")
            chat_keys = await CacheService.keys(chat_pattern)
            if chat_keys:
                for key in chat_keys:
                    await CacheService.delete(key)
                logger.debug(f"Đã xóa {len(chat_keys)} khóa cache chat")

            logger.info(f"Lịch sử chat của người dùng {user_id} đã được xóa thành công")
        except Exception as e:
            logger.error(f"Lỗi khi xóa lịch sử chat: {str(e)}", exc_info=True)
            raise

    async def _process_lottery_query(self, message: str) -> str:
        """
        Xử lý truy vấn kết quả xổ số
        """
        # Trích xuất ngày và loại xổ số từ tin nhắn
        try:
            # Sử dụng hàm trích xuất ngày từ tin nhắn
            query_date = extract_date_from_message(message)
            logger.debug(f"Ngày được trích xuất từ tin nhắn: {query_date}")

            # Sử dụng LotteryService để lấy kết quả xổ số
            db = await self.get_db()
            lottery_service = LotteryService(db)

            # Lấy kết quả xổ số theo ngày
            results = await lottery_service.get_lottery_results_by_date(query_date)

            if results and len(results) > 0:
                # Nếu có nhiều kết quả, ghép lại
                if len(results) > 1:
                    combined_result = f"Kết quả xổ số ngày {query_date.strftime('%d/%m/%Y')}:\n\n"
                    for result in results:
                        combined_result += f"{result.lottery_type} ({result.region or 'Không rõ khu vực'}):\n"
                        combined_result += result.formatted_results.split(":\n\n")[1] + "\n\n"
                    return combined_result
                else:
                    # Chỉ có một kết quả
                    return results[0].formatted_results
            else:
                no_result_msg = f"Không tìm thấy kết quả xổ số cho ngày {query_date.strftime('%d/%m/%Y')}."
                return no_result_msg

        except Exception as e:
            logger.error(f"Lỗi khi xử lý truy vấn kết quả xổ số: {str(e)}", exc_info=True)
            return "Xin lỗi, tôi không thể tìm thấy kết quả xổ số bạn yêu cầu. Vui lòng thử lại với câu hỏi cụ thể hơn."

    async def _query_llm(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Truy vấn dịch vụ LLM để lấy phản hồi
        """
        logger.debug(f"Truy vấn LLM với tin nhắn: {message[:50]}...")

        try:
            # Chuẩn bị messages
            messages = []

            # Thêm context nếu có
            if context:
                system_message = f"Context thông tin: {json.dumps(context, ensure_ascii=False)}"
                messages.append(LLMMessage(role="system", content=system_message))

            # Thêm tin nhắn của người dùng
            messages.append(LLMMessage(role="user", content=message))

            # Sử dụng LLM Client Service
            llm_client = LLMClientService()
            response = await llm_client.chat(
                messages=messages,
                max_tokens=settings.llm.default_max_tokens,
                temperature=settings.llm.default_temperature,
                use_cache=True
            )

            return response.output
        except Exception as e:
            logger.error(f"Lỗi khi truy vấn LLM: {str(e)}", exc_info=True)
            return "Xin lỗi, tôi đang gặp sự cố khi xử lý yêu cầu của bạn. Vui lòng thử lại sau."

    async def _log_interaction(self, user_id: str, query: str, response: str, intent: str) -> None:
        """
        Ghi lại tương tác của người dùng vào cơ sở dữ liệu
        """
        try:
            # Rút gọn phản hồi nếu quá dài
            response_summary = response
            if len(response_summary) > 1000:
                response_summary = response_summary[:997] + "..."

            # Lưu vào cơ sở dữ liệu sử dụng SQLAlchemy ORM
            db = await self.get_db()
            user_query = await UserQueryModel.create(db,
                user_id=user_id,
                query_text=query,
                intent=intent,
                response_summary=response_summary
            )

            logger.debug(f"Tương tác đã được ghi lại với ID: {user_query.id}")

            # Xóa cache lịch sử chat để cập nhật dữ liệu mới
            cache_pattern = generate_cache_key("history", user_id, "*")
            keys = await CacheService.keys(cache_pattern)
            if keys:
                for key in keys:
                    await CacheService.delete(key)
                logger.debug(f"Đã xóa {len(keys)} khóa cache lịch sử chat để cập nhật dữ liệu mới")

        except Exception as e:
            logger.error(f"Lỗi khi ghi lại tương tác: {str(e)}", exc_info=True)
