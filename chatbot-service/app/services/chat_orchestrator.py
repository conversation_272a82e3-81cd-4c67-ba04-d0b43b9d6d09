import time
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

from app.services.intent_analyzer import Intent<PERSON><PERSON><PERSON><PERSON>, IntentResultSchema
from app.services.context_manager import ContextManager, ConversationContext
#from app.services.lottery import LotteryService
#from app.services.statistics import StatisticsService
#from app.services.prediction import PredictionService
from app.services.llm_client import LLMClientService
from app.cache.multi_level_cache import multi_cache
from app.schemas.chat import ChatResponseSchema
from app.utils.logger import get_logger
from sqlalchemy.ext.asyncio import AsyncSession

logger = get_logger("chat_orchestrator")

class ChatOrchestrator:
    """
    Thành phần điều phối chính cho chat system
    Tích hợp tất cả các services và xử lý luồng chat một cách tối ưu
    """

    def __init__(self, db: AsyncSession):
        self.db = db

        # Initialize services
        self.intent_analyzer = IntentAnalyzer()
        self.context_manager = ContextManager(db)
        #self.lottery_service = LotteryService(db)
        #self.statistics_service = StatisticsService(db)
        #self.prediction_service = PredictionService(db)
        self.llm_client = LLMClientService()

        # Cache configuration
        self.cache_ttl = {
            'chat_response': 1800,    # 30 minutes
            'intent_analysis': 3600,  # 1 hour
            'lottery_data': 7200,     # 2 hours
            'statistics': 3600,       # 1 hour
            'predictions': 1800       # 30 minutes
        }

        logger.info("Chat Orchestrator initialized")

    async def process_chat(
        self,
        user_id: str,
        message: str,
        context: Optional[Dict] = None
    ) -> ChatResponseSchema:
        """
        Xử lý chat request chính

        Args:
            user_id: ID người dùng
            message: Tin nhắn từ người dùng
            context: Context bổ sung (nếu có)

        Returns:
            ChatResponseSchema: Phản hồi từ chatbot
        """
        start_time = time.time()

        try:
            # 1. Kiểm tra cache cho response hoàn chỉnh
            cache_key = multi_cache.generate_cache_key(
                "chat_response", user_id, message
            )

            cached_response = await multi_cache.get(cache_key)
            if cached_response:
                logger.info(f"Cache hit for chat response: {user_id}")
                return ChatResponseSchema(**cached_response)

            # 2. Lấy conversation context
            conversation_context = await self.context_manager.get_conversation_context(
                user_id, message
            )

            # 3. Phân tích ý định
            intent_result = await self._analyze_intent_with_cache(message, conversation_context)

            # 4. Xử lý dựa trên intent
            response_data = await self._process_by_intent(
                intent_result, message, conversation_context
            )

            # 5. Tạo response cuối cùng
            response = ChatResponseSchema(
                response=response_data['response'],
                intent=intent_result.intent,
                sub_intent=intent_result.sub_intent,
                confidence=intent_result.confidence,
                data=response_data.get('data'),
                timestamp=datetime.now().isoformat(),
                processing_time=time.time() - start_time
            )

            # 6. Cache response
            await multi_cache.set(
                cache_key,
                response.dict(),
                ttl_l1=300,  # 5 minutes in memory
                ttl_l2=self.cache_ttl['chat_response']  # 30 minutes in Redis
            )

            # 7. Cập nhật context
            await self.context_manager.update_context(
                user_id, message, response.response, intent_result.intent
            )

            logger.info(f"Chat processed successfully for user {user_id} "
                       f"(intent: {intent_result.intent}, time: {response.processing_time:.3f}s)")

            return response

        except Exception as e:
            logger.error(f"Lỗi khi xử lý chat cho user {user_id}: {str(e)}", exc_info=True)

            # Fallback response
            return ChatResponseSchema(
                response="Xin lỗi, tôi đang gặp sự cố khi xử lý yêu cầu của bạn. Vui lòng thử lại sau.",
                intent="error",
                confidence=0.0,
                timestamp=datetime.now().isoformat(),
                processing_time=time.time() - start_time
            )

    async def _analyze_intent_with_cache(
        self,
        message: str,
        context: ConversationContext
    ) -> IntentResultSchema:
        """Phân tích ý định với cache"""

        # Tạo cache key bao gồm cả context
        context_summary = self._summarize_context(context)
        cache_key = multi_cache.generate_cache_key(
            "intent_analysis", message, context_summary
        )

        # Kiểm tra cache
        cached_intent = await multi_cache.get(cache_key)
        if cached_intent:
            logger.debug("Cache hit for intent analysis")
            return IntentResultSchema(**cached_intent)

        # Phân tích intent mới
        intent_result = await self.intent_analyzer.analyze_intent(
            message, context.dict() if context else None
        )

        # Cache kết quả
        await multi_cache.set(
            cache_key,
            intent_result.dict(),
            ttl_l1=600,  # 10 minutes
            ttl_l2=self.cache_ttl['intent_analysis']
        )

        return intent_result

    async def _process_by_intent(
        self,
        intent_result: IntentResultSchema,
        message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Xử lý dựa trên intent được phân tích"""

        intent = intent_result.intent
        sub_intent = intent_result.sub_intent
        entities = intent_result.entities

        try:
            if intent == "lottery_query":
                return await self._handle_lottery_query(sub_intent, entities, message, context)

            elif intent == "statistics":
                return await self._handle_statistics_query(sub_intent, entities, message, context)

            elif intent == "prediction":
                return await self._handle_prediction_query(sub_intent, entities, message, context)

            elif intent == "general_chat":
                return await self._handle_general_chat(sub_intent, message, context)

            else:
                # Unknown intent
                return await self._handle_unknown_intent(message, context)

        except Exception as e:
            logger.error(f"Lỗi khi xử lý intent {intent}: {str(e)}", exc_info=True)
            return {
                'response': f"Xin lỗi, tôi gặp sự cố khi xử lý yêu cầu về {intent}. Vui lòng thử lại.",
                'data': None
            }

    async def _handle_lottery_query(
        self,
        sub_intent: str,
        entities: Any,
        message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Xử lý truy vấn kết quả xổ số"""

        try:
            if sub_intent == "specific_date":
                # Truy vấn kết quả ngày cụ thể
                lottery_type = entities.lottery_type or context.user_preferences.get('preferred_lottery_type', 'mb')
                date = entities.date

                if not date:
                    return {
                        'response': "Bạn muốn xem kết quả xổ số ngày nào? Vui lòng cho tôi biết ngày cụ thể.",
                        'data': None
                    }

                # Lấy kết quả từ service
                results = await self.lottery_service.get_lottery_results(lottery_type, date)

                if results:
                    # Format kết quả bằng LLM
                    formatted_response = await self._format_lottery_results_with_llm(
                        results, lottery_type, date
                    )
                    return {
                        'response': formatted_response,
                        'data': results
                    }
                else:
                    return {
                        'response': f"Không tìm thấy kết quả xổ số {lottery_type.upper()} ngày {date}. "
                                  f"Có thể đây là ngày trong tương lai hoặc chưa có kết quả.",
                        'data': None
                    }

            elif sub_intent == "latest_result":
                # Lấy kết quả mới nhất
                lottery_type = entities.lottery_type or context.user_preferences.get('preferred_lottery_type', 'mb')

                results = await self.lottery_service.get_latest_results(lottery_type, limit=1)

                if results:
                    latest = results[0]
                    formatted_response = await self._format_lottery_results_with_llm(
                        latest, lottery_type, latest['draw_date']
                    )
                    return {
                        'response': formatted_response,
                        'data': latest
                    }
                else:
                    return {
                        'response': f"Không tìm thấy kết quả xổ số {lottery_type.upper()} gần đây.",
                        'data': None
                    }

            elif sub_intent == "check_numbers":
                # Kiểm tra vé số
                numbers = entities.numbers
                lottery_type = entities.lottery_type or 'mb'
                date = entities.date

                if not numbers:
                    return {
                        'response': "Bạn muốn kiểm tra số nào? Vui lòng cho tôi biết các số cần kiểm tra.",
                        'data': None
                    }

                check_results = await self.lottery_service.check_numbers(
                    lottery_type, date, numbers
                )

                formatted_response = await self._format_number_check_with_llm(
                    check_results, numbers, lottery_type, date
                )

                return {
                    'response': formatted_response,
                    'data': check_results
                }

            else:
                # Fallback cho lottery query
                return await self._handle_general_lottery_question(message, context)

        except Exception as e:
            logger.error(f"Lỗi khi xử lý lottery query: {str(e)}")
            raise

    async def _handle_statistics_query(
        self,
        sub_intent: str,
        entities: Any,
        message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Xử lý truy vấn thống kê"""

        try:
            lottery_type = entities.lottery_type or context.user_preferences.get('preferred_lottery_type', 'mb')
            period = entities.period or 'month'

            if sub_intent == "frequency_analysis":
                stats = await self.statistics_service.get_frequency_analysis(
                    lottery_type, period
                )

                formatted_response = await self._format_statistics_with_llm(
                    stats, "frequency_analysis", lottery_type, period
                )

                return {
                    'response': formatted_response,
                    'data': stats
                }

            elif sub_intent == "hot_cold_numbers":
                hot_numbers = await self.statistics_service.get_hot_numbers(lottery_type, period)
                cold_numbers = await self.statistics_service.get_cold_numbers(lottery_type, period)

                data = {
                    'hot_numbers': hot_numbers,
                    'cold_numbers': cold_numbers,
                    'lottery_type': lottery_type,
                    'period': period
                }

                formatted_response = await self._format_hot_cold_numbers_with_llm(data)

                return {
                    'response': formatted_response,
                    'data': data
                }

            else:
                # Fallback cho statistics
                return await self._handle_general_statistics_question(message, context)

        except Exception as e:
            logger.error(f"Lỗi khi xử lý statistics query: {str(e)}")
            raise

    async def _handle_prediction_query(
        self,
        sub_intent: str,
        entities: Any,
        message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Xử lý truy vấn dự đoán/soi cầu"""

        try:
            lottery_type = entities.lottery_type or context.user_preferences.get('preferred_lottery_type', 'mb')

            if sub_intent == "next_draw":
                prediction = await self.prediction_service.predict_next_draw(lottery_type)

                formatted_response = await self._format_prediction_with_llm(
                    prediction, lottery_type
                )

                return {
                    'response': formatted_response,
                    'data': prediction
                }

            else:
                # Fallback cho prediction
                return await self._handle_general_prediction_question(message, context)

        except Exception as e:
            logger.error(f"Lỗi khi xử lý prediction query: {str(e)}")
            raise

    async def _handle_general_chat(
        self,
        sub_intent: str,
        message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Xử lý chat chung"""

        # Tạo system prompt với context
        system_prompt = self._create_system_prompt(context)

        # Gọi LLM để tạo phản hồi
        response = await self.llm_client.chat_completion(
            prompt=message,
            system_message=system_prompt,
            temperature=0.7,
            max_tokens=500,
            use_cache=True
        )

        return {
            'response': response,
            'data': None
        }

    def _create_system_prompt(self, context: ConversationContext) -> str:
        """Tạo system prompt cho LLM"""

        context_str = self.context_manager.format_context_for_llm(context)

        system_prompt = f"""
Bạn là trợ lý AI thông minh của XỔ SỐ TV. Nhiệm vụ của bạn:

1. Trả lời các câu hỏi về xổ số một cách chính xác và hữu ích
2. Cung cấp thông tin về kết quả, thống kê, và dự đoán xổ số
3. Tương tác thân thiện và tự nhiên với người dùng
4. Sử dụng context cuộc hội thoại để đưa ra phản hồi phù hợp

Context người dùng:
{context_str}

Lưu ý:
- Luôn trả lời bằng tiếng Việt
- Đưa ra thông tin chính xác và có căn cứ
- Nếu không chắc chắn, hãy thừa nhận và đề xuất cách khác
- Khuyến khích chơi có trách nhiệm
"""

        return system_prompt

    def _summarize_context(self, context: ConversationContext) -> str:
        """Tóm tắt context để tạo cache key"""
        if not context or not context.recent_messages:
            return "empty"

        # Lấy 2 tin nhắn gần nhất
        recent = context.recent_messages[:2]
        summary_parts = []

        for msg in recent:
            summary_parts.append(f"{msg.intent}:{msg.message[:50]}")

        return "|".join(summary_parts)

    async def _format_lottery_results_with_llm(
        self,
        results: Dict[str, Any],
        lottery_type: str,
        date: str
    ) -> str:
        """Format kết quả xổ số bằng LLM"""

        prompt = f"""
Hãy định dạng kết quả xổ số sau thành câu trả lời thân thiện và dễ đọc:

Loại xổ số: {lottery_type.upper()}
Ngày: {date}
Kết quả: {results}

Yêu cầu:
- Trình bày rõ ràng từng giải
- Sử dụng emoji phù hợp
- Thân thiện và dễ hiểu
- Kết thúc bằng lời chúc may mắn
"""

        return await self.llm_client.chat_completion(
            prompt=prompt,
            temperature=0.3,
            max_tokens=400,
            use_cache=True
        )

    async def _format_number_check_with_llm(
        self,
        check_results: Dict[str, Any],
        numbers: List[str],
        lottery_type: str,
        date: str
    ) -> str:
        """Format kết quả kiểm tra số bằng LLM"""

        prompt = f"""
Hãy thông báo kết quả kiểm tra vé số sau:

Các số kiểm tra: {', '.join(numbers)}
Loại xổ số: {lottery_type.upper()}
Ngày: {date}
Kết quả kiểm tra: {check_results}

Yêu cầu:
- Thông báo rõ ràng số nào trúng giải gì
- Tính tổng tiền thưởng (nếu có)
- Chúc mừng nếu trúng, an ủi nếu không trúng
- Khuyến khích chơi có trách nhiệm
"""

        return await self.llm_client.chat_completion(
            prompt=prompt,
            temperature=0.3,
            max_tokens=300,
            use_cache=True
        )

    async def _format_statistics_with_llm(
        self,
        stats: Dict[str, Any],
        analysis_type: str,
        lottery_type: str,
        period: str
    ) -> str:
        """Format thống kê bằng LLM"""

        prompt = f"""
Hãy trình bày thống kê xổ số sau một cách dễ hiểu:

Loại thống kê: {analysis_type}
Loại xổ số: {lottery_type.upper()}
Khoảng thời gian: {period}
Dữ liệu thống kê: {stats}

Yêu cầu:
- Trình bày các con số quan trọng
- Giải thích ý nghĩa của thống kê
- Đưa ra nhận xét và gợi ý
- Sử dụng emoji và format dễ đọc
"""

        return await self.llm_client.chat_completion(
            prompt=prompt,
            temperature=0.4,
            max_tokens=500,
            use_cache=True
        )

    async def _format_hot_cold_numbers_with_llm(self, data: Dict[str, Any]) -> str:
        """Format số nóng/lạnh bằng LLM"""

        prompt = f"""
Hãy trình bày thông tin về số nóng và số lạnh:

Dữ liệu: {data}

Yêu cầu:
- Giải thích số nóng và số lạnh là gì
- Liệt kê các số nóng và lạnh
- Đưa ra gợi ý cho người chơi
- Nhắc nhở chơi có trách nhiệm
"""

        return await self.llm_client.chat_completion(
            prompt=prompt,
            temperature=0.4,
            max_tokens=400,
            use_cache=True
        )

    async def _format_prediction_with_llm(
        self,
        prediction: Dict[str, Any],
        lottery_type: str
    ) -> str:
        """Format dự đoán bằng LLM"""

        prompt = f"""
Hãy trình bày dự đoán xổ số sau:

Loại xổ số: {lottery_type.upper()}
Dự đoán: {prediction}

Yêu cầu:
- Trình bày các số dự đoán
- Giải thích cơ sở dự đoán
- Nhấn mạnh tính chất tham khảo
- Khuyến khích chơi có trách nhiệm
- Sử dụng emoji phù hợp
"""

        return await self.llm_client.chat_completion(
            prompt=prompt,
            temperature=0.5,
            max_tokens=500,
            use_cache=True
        )

    async def _handle_general_lottery_question(
        self,
        message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Xử lý câu hỏi chung về xổ số"""

        system_prompt = f"""
Bạn là chuyên gia xổ số. Hãy trả lời câu hỏi về xổ số một cách chính xác và hữu ích.

Context: {self.context_manager.format_context_for_llm(context)}

Lưu ý:
- Cung cấp thông tin chính xác về xổ số Việt Nam
- Khuyến khích chơi có trách nhiệm
- Nếu cần dữ liệu cụ thể, hướng dẫn cách hỏi
"""

        response = await self.llm_client.chat_completion(
            prompt=message,
            system_message=system_prompt,
            temperature=0.6,
            max_tokens=400,
            use_cache=True
        )

        return {
            'response': response,
            'data': None
        }

    async def _handle_general_statistics_question(
        self,
        message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Xử lý câu hỏi chung về thống kê"""

        system_prompt = f"""
Bạn là chuyên gia phân tích thống kê xổ số. Hãy trả lời câu hỏi về thống kê xổ số.

Context: {self.context_manager.format_context_for_llm(context)}

Lưu ý:
- Giải thích các khái niệm thống kê một cách dễ hiểu
- Đưa ra ví dụ cụ thể nếu cần
- Nhấn mạnh tính chất tham khảo của thống kê
"""

        response = await self.llm_client.chat_completion(
            prompt=message,
            system_message=system_prompt,
            temperature=0.5,
            max_tokens=400,
            use_cache=True
        )

        return {
            'response': response,
            'data': None
        }

    async def _handle_general_prediction_question(
        self,
        message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Xử lý câu hỏi chung về dự đoán"""

        system_prompt = f"""
Bạn là chuyên gia soi cầu xổ số. Hãy trả lời câu hỏi về dự đoán và soi cầu.

Context: {self.context_manager.format_context_for_llm(context)}

Lưu ý:
- Giải thích các phương pháp soi cầu phổ biến
- Nhấn mạnh tính chất tham khảo
- Khuyến khích chơi có trách nhiệm
- Không đưa ra cam kết chắc chắn
"""

        response = await self.llm_client.chat_completion(
            prompt=message,
            system_message=system_prompt,
            temperature=0.6,
            max_tokens=400,
            use_cache=True
        )

        return {
            'response': response,
            'data': None
        }

    async def _handle_unknown_intent(
        self,
        message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Xử lý intent không xác định"""

        system_prompt = f"""
Bạn là trợ lý AI của XỔ SỐ TV. Người dùng vừa hỏi một câu hỏi mà bạn không rõ ý định.

Context: {self.context_manager.format_context_for_llm(context)}

Hãy:
- Xin lỗi vì không hiểu rõ câu hỏi
- Đề xuất các câu hỏi mà bạn có thể trả lời
- Hướng dẫn cách hỏi rõ ràng hơn
"""

        response = await self.llm_client.chat_completion(
            prompt=message,
            system_message=system_prompt,
            temperature=0.7,
            max_tokens=300,
            use_cache=True
        )

        return {
            'response': response,
            'data': None
        }
