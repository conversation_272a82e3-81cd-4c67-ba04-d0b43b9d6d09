import httpx
import json
import logging
import asyncio
import time
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel

from app.config import settings
from app.cache.multi_level_cache import multi_cache

logger = logging.getLogger("llm-client")

class LLMMessage(BaseModel):
    """Model cho tin nhắn LLM"""
    role: str  # "system", "user", "assistant"
    content: str

class LLMRequest(BaseModel):
    """Model cho yêu cầu LLM"""
    messages: List[LLMMessage]
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    stop: Optional[List[str]] = None

class LLMResponse(BaseModel):
    """Model cho phản hồi LLM"""
    output: str
    usage: Dict[str, Any]

class LLMClientService:
    """
    Dịch vụ client để giao tiếp với LLM Core API
    """

    _instance = None
    _health_check_time = 0
    _is_healthy = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LLMClientService, cls).__new__(cls)
            cls._instance._init()
        return cls._instance

    def _init(self):
        """Khởi tạo client"""
        self.base_url = settings.llm.api_url
        self.timeout = settings.llm.timeout
        self.max_retries = settings.llm.max_retries
        self.retry_delay = settings.llm.retry_delay
        self.health_check_interval = settings.llm.health_check_interval

        # Khởi tạo httpx client
        self.client = httpx.AsyncClient(
            timeout=self.timeout,
            limits=httpx.Limits(max_keepalive_connections=10, max_connections=20)
        )

        logger.info(f"LLM Client đã được khởi tạo với base URL: {self.base_url}")

    async def close(self):
        """Đóng client"""
        if hasattr(self, 'client'):
            await self.client.aclose()
            logger.info("LLM Client đã được đóng")

    async def is_healthy(self, force_check: bool = False) -> bool:
        """
        Kiểm tra trạng thái hoạt động của LLM Core

        Args:
            force_check: Bắt buộc kiểm tra ngay lập tức, bỏ qua cache

        Returns:
            bool: True nếu LLM Core đang hoạt động, False nếu không
        """
        current_time = time.time()

        # Sử dụng kết quả cache nếu có và chưa hết hạn
        if not force_check and current_time - self._health_check_time < self.health_check_interval:
            return self._is_healthy

        try:
            # Gọi health check endpoint
            response = await self.client.get(f"{self.base_url}/health", timeout=5.0)

            # Cập nhật trạng thái
            self._is_healthy = response.status_code == 200
            self._health_check_time = current_time

            if self._is_healthy:
                logger.debug("LLM Core health check: OK")
            else:
                logger.warning(f"LLM Core health check failed: {response.status_code}")

            return self._is_healthy
        except Exception as e:
            self._is_healthy = False
            self._health_check_time = current_time
            logger.error(f"LLM Core health check error: {str(e)}")
            return False

    async def get_model_info(self) -> Dict[str, Any]:
        """
        Lấy thông tin về model LLM

        Returns:
            Dict[str, Any]: Thông tin model
        """
        cache_key = multi_cache.generate_cache_key("llm_model_info")

        # Kiểm tra cache
        cached_info = await multi_cache.get(cache_key)
        if cached_info:
            return cached_info

        try:
            # Gọi API lấy thông tin model
            response = await self.client.get(f"{self.base_url}/models")

            if response.status_code == 200:
                model_info = response.json()

                # Cache thông tin model (1 giờ)
                await multi_cache.set(cache_key, model_info, ttl_l1=600, ttl_l2=3600)

                return model_info
            else:
                logger.error(f"Lỗi khi lấy thông tin model: {response.status_code}")
                return {"error": "Không thể lấy thông tin model"}
        except Exception as e:
            logger.error(f"Lỗi khi lấy thông tin model: {str(e)}")
            return {"error": str(e)}

    async def chat(
        self,
        messages: List[Union[Dict[str, str], LLMMessage]],
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        stop: Optional[List[str]] = None,
        use_cache: bool = True
    ) -> LLMResponse:
        """
        Gửi yêu cầu chat đến LLM Core

        Args:
            messages: Danh sách tin nhắn
            max_tokens: Số lượng token tối đa cho phản hồi
            temperature: Nhiệt độ sampling (0.0 - 1.0)
            top_p: Top-p sampling (0.0 - 1.0)
            stop: Danh sách các chuỗi dừng
            use_cache: Sử dụng cache hay không

        Returns:
            LLMResponse: Phản hồi từ LLM
        """
        # Chuẩn bị tin nhắn
        prepared_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                prepared_messages.append(LLMMessage(**msg))
            else:
                prepared_messages.append(msg)

        # Tạo request
        request = LLMRequest(
            messages=prepared_messages,
            max_tokens=max_tokens or settings.llm.default_max_tokens,
            temperature=temperature or settings.llm.default_temperature,
            top_p=top_p or settings.llm.default_top_p,
            stop=stop
        )

        # Tạo cache key
        if use_cache:
            cache_key = multi_cache.generate_cache_key(
                "llm_chat",
                json.dumps([m.dict() for m in request.messages], sort_keys=True),
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                stop=json.dumps(request.stop) if request.stop else None
            )

            # Kiểm tra cache
            cached_response = await multi_cache.get(cache_key)
            if cached_response:
                logger.debug(f"Tìm thấy phản hồi LLM trong cache")
                return LLMResponse(**cached_response)

        # Kiểm tra trạng thái LLM Core
        if not await self.is_healthy():
            logger.error("LLM Core không khả dụng")
            raise Exception("LLM Core không khả dụng")

        # Thực hiện request với retry
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Gọi LLM API (lần thử {attempt + 1}/{self.max_retries})")

                response = await self.client.post(
                    f"{self.base_url}/chat",
                    json=request.dict(exclude_none=True)
                )

                if response.status_code == 200:
                    result = response.json()
                    llm_response = LLMResponse(**result)

                    # Cache kết quả
                    if use_cache:
                        await multi_cache.set(
                            cache_key,
                            llm_response.dict(),
                            ttl_l1=300,  # 5 minutes in memory
                            ttl_l2=settings.cache_ttl  # Redis cache
                        )

                    # Log thông tin sử dụng
                    logger.debug(f"Thông tin sử dụng LLM: {llm_response.usage}")

                    return llm_response
                elif response.status_code == 429:
                    # Rate limit, thử lại sau
                    retry_after = int(response.headers.get("Retry-After", self.retry_delay))
                    logger.warning(f"LLM API rate limit, thử lại sau {retry_after}s")
                    await asyncio.sleep(retry_after)
                else:
                    error_msg = f"Lỗi khi gọi LLM API: {response.status_code} - {response.text}"
                    logger.error(error_msg)

                    # Nếu là lỗi server (5xx), thử lại
                    if 500 <= response.status_code < 600:
                        await asyncio.sleep(self.retry_delay * (attempt + 1))
                    else:
                        # Lỗi client (4xx), không thử lại
                        raise Exception(error_msg)
            except httpx.TimeoutException:
                logger.warning(f"Timeout khi gọi LLM API (lần thử {attempt + 1}/{self.max_retries})")
                await asyncio.sleep(self.retry_delay * (attempt + 1))
            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"Lỗi khi gọi LLM API: {str(e)}, thử lại sau {self.retry_delay}s")
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                else:
                    logger.error(f"Lỗi khi gọi LLM API sau {self.max_retries} lần thử: {str(e)}")
                    raise

        # Nếu đã thử hết số lần mà vẫn không thành công
        raise Exception(f"Không thể kết nối đến LLM Core sau {self.max_retries} lần thử")

    async def chat_completion(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        use_cache: bool = True
    ) -> str:
        """
        Phương thức tiện ích để gửi một prompt đơn giản và nhận phản hồi dạng text

        Args:
            prompt: Nội dung prompt
            system_message: Tin nhắn hệ thống (tùy chọn)
            max_tokens: Số lượng token tối đa cho phản hồi
            temperature: Nhiệt độ sampling (0.0 - 1.0)
            use_cache: Sử dụng cache hay không

        Returns:
            str: Phản hồi dạng text
        """
        messages = []

        # Thêm system message nếu có
        if system_message:
            messages.append(LLMMessage(role="system", content=system_message))

        # Thêm user message
        messages.append(LLMMessage(role="user", content=prompt))

        try:
            response = await self.chat(
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                use_cache=use_cache
            )

            return response.output
        except Exception as e:
            logger.error(f"Lỗi khi gọi chat_completion: {str(e)}")
            return f"Xin lỗi, tôi đang gặp sự cố khi xử lý yêu cầu của bạn. Vui lòng thử lại sau."
