import logging
import random
from typing import List, Dict, Any, Optional, Tuple
from datetime import date, datetime, timedelta
from collections import Counter, defaultdict
from sqlalchemy.ext.asyncio import AsyncSession
import numpy as np
from scipy import stats

from app.config import settings
from app.models.lottery import LotteryTypeModel, LotteryDrawModel
from app.services.lottery import LotteryService
# TEMPORARILY COMMENTED OUT TO FIX RECURSION ERROR
# from app.schemas.statistics import (
#     NumberFrequencySchema, PrizeFrequencySchema, FrequencyStatisticsSchema,
#     LotteryPredictionSchema, LotteryCycleSchema, CycleAnalysisSchema,
#     DoubleNumbersSchema, HeadTailStatisticsSchema, StatisticsResponseSchema
# )
from app.cache.multi_level_cache import multi_cache

logger = logging.getLogger("statistics-service")

class StatisticsService:
    """
    Dịch vụ thống kê và soi cầu xổ số
    """

    def __init__(self, db: AsyncSession):
        """Khởi tạo với SQLAlchemy session"""
        self.db = db
        self.lottery_service = LotteryService(db)

    async def get_draws_in_period(
        self,
        lottery_type_code: str,
        start_date: date,
        end_date: date
    ) -> List[Dict[str, Any]]:
        """
        Lấy tất cả các kỳ quay trong một khoảng thời gian

        Args:
            lottery_type_code: Mã loại xổ số
            start_date: Ngày bắt đầu
            end_date: Ngày kết thúc

        Returns:
            List[Dict[str, Any]]: Danh sách các kỳ quay
        """
        # Lấy loại xổ số
        lottery_type = await self.lottery_service.get_lottery_type_by_code(lottery_type_code)
        if not lottery_type:
            logger.warning(f"Không tìm thấy loại xổ số với mã {lottery_type_code}")
            return []

        # Tạo cache key
        cache_key = multi_cache.generate_cache_key(
            "draws_period",
            lottery_type_code,
            start_date.isoformat(),
            end_date.isoformat()
        )

        # Kiểm tra cache
        cached_draws = await multi_cache.get(cache_key)
        if cached_draws:
            logger.debug(f"Lấy các kỳ quay từ cache cho {lottery_type_code} từ {start_date} đến {end_date}")
            return cached_draws

        # Lấy tất cả các kỳ quay trong khoảng thời gian
        query = LotteryDrawModel.__table__.select().where(
            LotteryDrawModel.lottery_type_id == lottery_type.id,
            LotteryDrawModel.draw_date >= start_date,
            LotteryDrawModel.draw_date <= end_date
        ).order_by(LotteryDrawModel.draw_date)

        result = await self.db.execute(query)
        draws = result.fetchall()

        # Chuyển đổi sang dạng dict
        draws_data = []
        for draw in draws:
            draw_dict = dict(draw)
            draw_dict["lottery_type"] = lottery_type.code
            draw_dict["lottery_name"] = lottery_type.name
            draws_data.append(draw_dict)

        # Cache kết quả
        if draws_data:
            await multi_cache.set(
                cache_key,
                draws_data,
                ttl_l1=600,  # 10 minutes in memory
                ttl_l2=86400  # 1 day in Redis
            )

        return draws_data

    def extract_all_numbers(self, results: Dict[str, Any]) -> List[str]:
        """
        Trích xuất tất cả các số từ kết quả xổ số

        Args:
            results: Kết quả xổ số dạng JSON

        Returns:
            List[str]: Danh sách các số
        """
        all_numbers = []

        for prize, numbers in results.items():
            if isinstance(numbers, list):
                all_numbers.extend(numbers)
            else:
                all_numbers.append(numbers)

        return all_numbers

    def extract_numbers_by_prize(self, results: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Trích xuất các số theo giải

        Args:
            results: Kết quả xổ số dạng JSON

        Returns:
            Dict[str, List[str]]: Các số theo giải
        """
        numbers_by_prize = {}

        for prize, numbers in results.items():
            if isinstance(numbers, list):
                numbers_by_prize[prize] = numbers
            else:
                numbers_by_prize[prize] = [numbers]

        return numbers_by_prize

    def get_number_frequency(
        self,
        draws: List[Dict[str, Any]],
        today: date = None
    ) -> List[NumberFrequencySchema]:
        """
        Tính tần suất xuất hiện của các số

        Args:
            draws: Danh sách các kỳ quay
            today: Ngày hiện tại (mặc định là ngày hôm nay)

        Returns:
            List[NumberFrequencySchema]: Danh sách tần suất xuất hiện
        """
        if today is None:
            today = date.today()

        # Đếm tần suất xuất hiện
        all_numbers = []
        last_appearance = {}

        for draw in draws:
            draw_date = draw["draw_date"]
            numbers = self.extract_all_numbers(draw["results"])

            all_numbers.extend(numbers)

            # Cập nhật ngày xuất hiện gần nhất
            for number in numbers:
                last_appearance[number] = draw_date

        # Đếm tần suất
        counter = Counter(all_numbers)

        # Tạo danh sách kết quả
        frequency_list = []
        for number, count in counter.items():
            last_date = last_appearance.get(number)
            days_ago = None

            if last_date:
                days_ago = (today - last_date).days

            frequency_list.append(NumberFrequencySchema(
                number=number,
                frequency=count,
                last_appeared=last_date,
                days_ago=days_ago
            ))

        # Sắp xếp theo tần suất giảm dần
        frequency_list.sort(key=lambda x: x.frequency, reverse=True)

        return frequency_list

    def get_prize_frequency(
        self,
        draws: List[Dict[str, Any]]
    ) -> List[PrizeFrequencySchema]:
        """
        Tính tần suất xuất hiện theo giải

        Args:
            draws: Danh sách các kỳ quay

        Returns:
            List[PrizeFrequencySchema]: Danh sách tần suất theo giải
        """
        # Đếm tần suất xuất hiện theo giải
        numbers_by_prize = defaultdict(list)
        last_appearance = {}
        today = date.today()

        for draw in draws:
            draw_date = draw["draw_date"]
            prize_numbers = self.extract_numbers_by_prize(draw["results"])

            for prize, numbers in prize_numbers.items():
                numbers_by_prize[prize].extend(numbers)

                # Cập nhật ngày xuất hiện gần nhất
                for number in numbers:
                    key = (prize, number)
                    last_appearance[key] = draw_date

        # Tạo danh sách kết quả
        prize_frequency_list = []

        for prize, numbers in numbers_by_prize.items():
            counter = Counter(numbers)

            number_frequency = []
            for number, count in counter.items():
                last_date = last_appearance.get((prize, number))
                days_ago = None

                if last_date:
                    days_ago = (today - last_date).days

                number_frequency.append(NumberFrequencySchema(
                    number=number,
                    frequency=count,
                    last_appeared=last_date,
                    days_ago=days_ago
                ))

            # Sắp xếp theo tần suất giảm dần
            number_frequency.sort(key=lambda x: x.frequency, reverse=True)

            prize_frequency_list.append(PrizeFrequencySchema(
                prize=prize,
                numbers=number_frequency
            ))

        return prize_frequency_list

    def get_head_tail_statistics(
        self,
        draws: List[Dict[str, Any]]
    ) -> HeadTailStatisticsSchema:
        """
        Thống kê đầu đuôi

        Args:
            draws: Danh sách các kỳ quay

        Returns:
            HeadTailStatisticsSchema: Thống kê đầu đuôi
        """
        heads = defaultdict(int)
        tails = defaultdict(int)

        for draw in draws:
            numbers = self.extract_all_numbers(draw["results"])

            for number in numbers:
                # Lấy đầu số (chữ số đầu tiên)
                if len(number) > 0:
                    head = number[0]
                    heads[head] += 1

                # Lấy đuôi số (chữ số cuối cùng)
                if len(number) > 0:
                    tail = number[-1]
                    tails[tail] += 1

        # Sắp xếp đầu số và đuôi số theo tần suất
        sorted_heads = sorted(heads.items(), key=lambda x: x[1], reverse=True)
        sorted_tails = sorted(tails.items(), key=lambda x: x[1], reverse=True)

        # Lấy các đầu số và đuôi số nóng (top 3)
        hot_heads = [h[0] for h in sorted_heads[:3]]
        hot_tails = [t[0] for t in sorted_tails[:3]]

        return HeadTailStatisticsSchema(
            heads=dict(heads),
            tails=dict(tails),
            hot_heads=hot_heads,
            hot_tails=hot_tails
        )

    def get_double_numbers(
        self,
        draws: List[Dict[str, Any]]
    ) -> List[DoubleNumbersSchema]:
        """
        Thống kê lô kép

        Args:
            draws: Danh sách các kỳ quay

        Returns:
            List[DoubleNumbersSchema]: Danh sách lô kép
        """
        doubles = defaultdict(int)
        last_appearance = {}
        today = date.today()

        for draw in draws:
            draw_date = draw["draw_date"]
            numbers = self.extract_all_numbers(draw["results"])

            for number in numbers:
                # Kiểm tra xem có phải là số kép không
                if len(number) >= 2:
                    # Kiểm tra các cặp chữ số liên tiếp
                    for i in range(len(number) - 1):
                        if number[i] == number[i+1]:
                            doubles[number] += 1
                            last_appearance[number] = draw_date
                            break

        # Tạo danh sách kết quả
        double_list = []

        for number, count in doubles.items():
            last_date = last_appearance.get(number)

            double_list.append(DoubleNumbersSchema(
                number=number,
                frequency=count,
                last_appeared=last_date
            ))

        # Sắp xếp theo tần suất giảm dần
        double_list.sort(key=lambda x: x.frequency, reverse=True)

        return double_list

    def analyze_cycles(
        self,
        draws: List[Dict[str, Any]],
        top_n: int = 10
    ) -> CycleAnalysisSchema:
        """
        Phân tích chu kỳ xuất hiện của các số

        Args:
            draws: Danh sách các kỳ quay
            top_n: Số lượng số cần phân tích

        Returns:
            CycleAnalysisSchema: Phân tích chu kỳ
        """
        # Sắp xếp các kỳ quay theo ngày
        sorted_draws = sorted(draws, key=lambda x: x["draw_date"])

        # Lấy loại xổ số
        lottery_type = sorted_draws[0]["lottery_name"] if sorted_draws else "Unknown"

        # Tính khoảng thời gian phân tích
        start_date = sorted_draws[0]["draw_date"] if sorted_draws else date.today()
        end_date = sorted_draws[-1]["draw_date"] if sorted_draws else date.today()
        analysis_period = (end_date - start_date).days + 1

        # Đếm tần suất xuất hiện
        number_frequency = self.get_number_frequency(draws)

        # Lấy top N số xuất hiện nhiều nhất
        top_numbers = [nf.number for nf in number_frequency[:top_n]]

        # Phân tích chu kỳ cho mỗi số
        cycles = []

        for number in top_numbers:
            # Tìm các ngày xuất hiện của số
            appearances = []

            for draw in sorted_draws:
                numbers = self.extract_all_numbers(draw["results"])
                if number in numbers:
                    appearances.append(draw["draw_date"])

            if len(appearances) < 2:
                continue

            # Tính khoảng cách giữa các lần xuất hiện
            intervals = [(appearances[i+1] - appearances[i]).days for i in range(len(appearances)-1)]

            # Tính chu kỳ trung bình
            avg_cycle = sum(intervals) / len(intervals) if intervals else 0

            # Dự đoán ngày xuất hiện tiếp theo
            last_appeared = appearances[-1]
            next_predicted = last_appeared + timedelta(days=round(avg_cycle))

            # Tính độ tin cậy dựa trên độ lệch chuẩn của các khoảng cách
            std_dev = np.std(intervals) if len(intervals) > 1 else 0
            confidence = 1.0 / (1.0 + std_dev / max(1, avg_cycle)) if avg_cycle > 0 else 0

            cycles.append(LotteryCycleSchema(
                number=number,
                average_cycle=round(avg_cycle, 1),
                last_appeared=last_appeared,
                next_predicted=next_predicted,
                confidence=round(confidence, 2)
            ))

        # Sắp xếp theo độ tin cậy giảm dần
        cycles.sort(key=lambda x: x.confidence, reverse=True)

        return CycleAnalysisSchema(
            lottery_type=lottery_type,
            analysis_period=analysis_period,
            cycles=cycles
        )

    def predict_lottery(
        self,
        draws: List[Dict[str, Any]],
        prediction_date: date
    ) -> LotteryPredictionSchema:
        """
        Dự đoán kết quả xổ số

        Args:
            draws: Danh sách các kỳ quay
            prediction_date: Ngày dự đoán

        Returns:
            LotteryPredictionSchema: Dự đoán kết quả xổ số
        """
        # Lấy loại xổ số
        lottery_type = draws[0]["lottery_name"] if draws else "Unknown"

        # Phân tích tần suất
        number_frequency = self.get_number_frequency(draws, prediction_date)

        # Lấy các số nóng (xuất hiện nhiều nhất)
        hot_numbers = [nf.number for nf in number_frequency[:10]]

        # Lấy các số lạnh (lâu chưa về)
        cold_numbers = sorted(
            [nf for nf in number_frequency if nf.days_ago and nf.days_ago > 7],
            key=lambda x: x.days_ago,
            reverse=True
        )
        cold_numbers = [nf.number for nf in cold_numbers[:10]]

        # Phân tích chu kỳ
        cycle_analysis = self.analyze_cycles(draws)

        # Lấy các số có chu kỳ đến ngày dự đoán
        cycle_numbers = []
        for cycle in cycle_analysis.cycles:
            if cycle.next_predicted and cycle.next_predicted == prediction_date:
                cycle_numbers.append(cycle.number)

        # Dự đoán giải đặc biệt
        special_candidates = set(hot_numbers + cycle_numbers)
        special_prize = random.sample(
            list(special_candidates),
            min(3, len(special_candidates))
        )

        # Dự đoán giải nhất
        first_candidates = set(hot_numbers) - set(special_prize)
        first_prize = random.sample(
            list(first_candidates),
            min(3, len(first_candidates))
        )

        # Tìm các cặp số thường xuất hiện cùng nhau
        pairs = []
        for i, draw in enumerate(draws[:-1]):
            numbers_i = set(self.extract_all_numbers(draw["results"]))

            for j in range(i+1, min(i+5, len(draws))):
                numbers_j = set(self.extract_all_numbers(draws[j]["results"]))
                common = numbers_i.intersection(numbers_j)

                if len(common) >= 2:
                    pairs.append(list(common)[:2])

        # Lấy tối đa 5 cặp số
        if len(pairs) > 5:
            pairs = random.sample(pairs, 5)

        # Tạo phân tích
        analysis = f"Dự đoán xổ số {lottery_type} ngày {prediction_date.strftime('%d/%m/%Y')}:\n\n"
        analysis += f"Dựa trên phân tích {len(draws)} kỳ quay gần nhất, các số có khả năng về cao là: {', '.join(special_prize)}\n\n"
        analysis += f"Các số nóng (xuất hiện nhiều): {', '.join(hot_numbers[:5])}\n"
        analysis += f"Các số lạnh (lâu chưa về): {', '.join(cold_numbers[:5])}\n\n"

        if cycle_numbers:
            analysis += f"Các số đến chu kỳ: {', '.join(cycle_numbers)}\n\n"

        if pairs:
            analysis += "Các cặp số thường về cùng nhau:\n"
            for pair in pairs:
                analysis += f"- {' và '.join(pair)}\n"

        return LotteryPredictionSchema(
            lottery_type=lottery_type,
            prediction_date=prediction_date,
            special_prize=special_prize,
            first_prize=first_prize,
            hot_numbers=hot_numbers,
            cold_numbers=cold_numbers,
            pairs=pairs,
            analysis=analysis
        )

    async def get_statistics(
        self,
        lottery_type_code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        include_details: bool = False
    ) -> StatisticsResponseSchema:
        """
        Lấy thống kê xổ số

        Args:
            lottery_type_code: Mã loại xổ số
            start_date: Ngày bắt đầu (mặc định là 30 ngày trước)
            end_date: Ngày kết thúc (mặc định là ngày hôm nay)
            include_details: Bao gồm chi tiết

        Returns:
            StatisticsResponseSchema: Thống kê xổ số
        """
        # Thiết lập ngày mặc định
        today = date.today()
        if not end_date:
            end_date = today
        if not start_date:
            start_date = end_date - timedelta(days=30)

        # Tạo cache key
        cache_key = generate_cache_key(
            CacheService.LOTTERY_PREFIX,
            "statistics",
            type=lottery_type_code,
            start=start_date.isoformat(),
            end=end_date.isoformat(),
            details=str(include_details)
        )

        # Kiểm tra cache
        cached_stats = await CacheService.get_json(cache_key)
        if cached_stats:
            logger.debug(f"Lấy thống kê từ cache cho {lottery_type_code} từ {start_date} đến {end_date}")
            return StatisticsResponse(**cached_stats)

        # Lấy loại xổ số
        lottery_type = await self.lottery_service.get_lottery_type_by_code(lottery_type_code)
        if not lottery_type:
            logger.warning(f"Không tìm thấy loại xổ số với mã {lottery_type_code}")
            raise ValueError(f"Không tìm thấy loại xổ số với mã {lottery_type_code}")

        # Lấy các kỳ quay trong khoảng thời gian
        draws = await self.get_draws_in_period(lottery_type_code, start_date, end_date)

        if not draws:
            logger.warning(f"Không có dữ liệu xổ số cho {lottery_type_code} từ {start_date} đến {end_date}")
            raise ValueError(f"Không có dữ liệu xổ số cho {lottery_type_code} từ {start_date} đến {end_date}")

        # Tính tần suất xuất hiện
        number_frequency = self.get_number_frequency(draws)

        # Tính tần suất theo giải
        prize_frequency = None
        if include_details:
            prize_frequency = self.get_prize_frequency(draws)

        # Thống kê đầu đuôi
        head_tail = self.get_head_tail_statistics(draws)

        # Thống kê lô kép
        doubles = None
        if include_details:
            doubles = self.get_double_numbers(draws)

        # Tạo thống kê tần suất
        frequency_stats = FrequencyStatisticsSchema(
            lottery_type=lottery_type.name,
            start_date=start_date,
            end_date=end_date,
            total_draws=len(draws),
            by_number=number_frequency,
            by_prize=prize_frequency,
            by_position={"heads": head_tail.heads, "tails": head_tail.tails} if include_details else None
        )

        # Lấy các số nóng và lạnh
        hot_numbers = number_frequency[:10]  # Top 10 số nóng

        cold_numbers = sorted(
            [nf for nf in number_frequency if nf.days_ago and nf.days_ago > 0],
            key=lambda x: x.days_ago,
            reverse=True
        )[:10]  # Top 10 số lạnh

        # Tạo phản hồi
        response = StatisticsResponseSchema(
            lottery_type=lottery_type.name,
            period=f"Từ {start_date.strftime('%d/%m/%Y')} đến {end_date.strftime('%d/%m/%Y')}",
            total_draws=len(draws),
            frequency=frequency_stats,
            hot_numbers=hot_numbers,
            cold_numbers=cold_numbers,
            doubles=doubles,
            head_tail=head_tail if include_details else None
        )

        # Cache kết quả
        await CacheService.set_json(
            cache_key,
            response.model_dump(),
            expire=3600  # Cache 1 giờ
        )

        return response

    async def get_prediction(
        self,
        lottery_type_code: str,
        prediction_date: Optional[date] = None,
        analysis_period: int = 30
    ) -> Tuple[LotteryPredictionSchema, StatisticsResponseSchema, CycleAnalysisSchema]:
        """
        Lấy dự đoán xổ số

        Args:
            lottery_type_code: Mã loại xổ số
            prediction_date: Ngày dự đoán (mặc định là ngày hôm nay)
            analysis_period: Khoảng thời gian phân tích (ngày)

        Returns:
            Tuple[LotteryPredictionSchema, StatisticsResponseSchema, CycleAnalysisSchema]: Dự đoán, thống kê và phân tích chu kỳ
        """
        # Thiết lập ngày mặc định
        today = date.today()
        if not prediction_date:
            prediction_date = today

        # Tạo cache key
        cache_key = generate_cache_key(
            CacheService.LOTTERY_PREFIX,
            "prediction",
            type=lottery_type_code,
            date=prediction_date.isoformat(),
            period=analysis_period
        )

        # Kiểm tra cache
        cached_prediction = await CacheService.get_json(cache_key)
        if cached_prediction:
            logger.debug(f"Lấy dự đoán từ cache cho {lottery_type_code} ngày {prediction_date}")
            return (
                LotteryPredictionSchema(**cached_prediction["prediction"]),
                StatisticsResponseSchema(**cached_prediction["statistics"]),
                CycleAnalysisSchema(**cached_prediction["cycles"])
            )

        # Tính ngày bắt đầu phân tích
        start_date = prediction_date - timedelta(days=analysis_period)

        # Lấy thống kê
        statistics = await self.get_statistics(
            lottery_type_code,
            start_date,
            prediction_date - timedelta(days=1),  # Không bao gồm ngày dự đoán
            include_details=True
        )

        # Lấy các kỳ quay trong khoảng thời gian
        draws = await self.get_draws_in_period(
            lottery_type_code,
            start_date,
            prediction_date - timedelta(days=1)
        )

        # Phân tích chu kỳ
        cycles = self.analyze_cycles(draws)

        # Dự đoán kết quả
        prediction = self.predict_lottery(draws, prediction_date)

        # Cache kết quả
        await CacheService.set_json(
            cache_key,
            {
                "prediction": prediction.model_dump(),
                "statistics": statistics.model_dump(),
                "cycles": cycles.model_dump()
            },
            expire=3600  # Cache 1 giờ
        )

        return prediction, statistics, cycles
