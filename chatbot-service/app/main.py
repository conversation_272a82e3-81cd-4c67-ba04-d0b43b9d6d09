import time
from datetime import datetime
from fastapi import FastAPI, Depends, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exception_handlers import http_exception_handler
from contextlib import asynccontextmanager

from app.api import v1
#from app.api import lottery, statistics
from app.config import settings
from app.db.postgresql import init_db, close_db
from app.cache.redis_client import init_redis, close_redis
from app.services.llm_client import LLMClientService
from app.middleware.logging_middleware import LoggingMiddleware
from app.middleware.rate_limiter import RateLimiterMiddleware
from app.utils.healthcheck import get_health_status
from app.utils.logger import get_logger
from app.cache.multi_level_cache import multi_cache
from app.db.session import get_db_session
from sqlalchemy.ext.asyncio import AsyncSession

# Lấy logger
logger = get_logger("main")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Khởi tạo kết nối khi ứng dụng khởi động
    logger.info("Đang khởi tạo kết nối...")
    await init_db()
    await init_redis()

    # Kiểm tra kết nối đến LLM Core
    llm_client = LLMClientService()
    is_healthy = await llm_client.is_healthy(force_check=True)
    if is_healthy:
        logger.info("Kết nối đến LLM Core thành công")
    else:
        logger.warning("Không thể kết nối đến LLM Core, dịch vụ vẫn sẽ hoạt động nhưng có thể gặp vấn đề với các tính năng LLM")

    # Lấy thông tin model
    try:
        model_info = await llm_client.get_model_info()
        logger.info(f"Thông tin model LLM: {model_info.get('model', 'unknown')}")
    except Exception as e:
        logger.warning(f"Không thể lấy thông tin model LLM: {str(e)}")

    logger.info("Kết nối đã được khởi tạo thành công!")
    yield

    # Đóng kết nối khi ứng dụng tắt
    logger.info("Đang đóng kết nối...")
    await close_db()
    await close_redis()
    await llm_client.close()
    logger.info("Kết nối đã được đóng!")

app = FastAPI(
    title="XỔ SỐ TV Chatbot API",
    description="API cho Dịch vụ Chatbot XỔ SỐ TV",
    version="1.1.0",
    lifespan=lifespan,
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json"
)

# Thêm CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Thêm các middleware
app.add_middleware(LoggingMiddleware)
app.add_middleware(RateLimiterMiddleware)

# Middleware để đo thời gian xử lý
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# Đăng ký các route API
app.include_router(v1.router, prefix="/api/v1")
#app.include_router(lottery.router, prefix="/api/v1/lottery")
#app.include_router(statistics.router, prefix="/api/v1/statistics")

# Xử lý ngoại lệ toàn cục
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Lỗi không xử lý được: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Đã xảy ra lỗi nội bộ. Vui lòng thử lại sau."}
    )

@app.exception_handler(HTTPException)
async def custom_http_exception_handler(request: Request, exc: HTTPException):
    logger.warning(f"HTTPException: {exc.status_code} - {exc.detail}")
    return await http_exception_handler(request, exc)

@app.get("/health")
async def health_check():
    """Endpoint kiểm tra trạng thái hoạt động chi tiết"""
    health_data = await get_health_status()
    return health_data

@app.get("/health/simple")
async def simple_health_check():
    """Endpoint kiểm tra trạng thái hoạt động đơn giản"""
    return {
        "status": "ok",
        "service": "chatbot-service",
        "version": settings.version,
        "environment": settings.environment
    }

@app.get("/cache/stats")
async def get_cache_stats():
    """Endpoint để xem thống kê cache"""
    try:
        stats = await multi_cache.get_stats()
        return {
            "status": "success",
            "cache_stats": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Lỗi khi lấy cache stats: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/timescaledb/info")
async def get_timescaledb_info(db: AsyncSession = Depends(get_db_session)):
    """Endpoint để xem thông tin TimescaleDB"""
    try:
        from sqlalchemy import text

        # Kiểm tra phiên bản TimescaleDB extension
        result = await db.execute(text("SELECT extversion FROM pg_extension WHERE extname = 'timescaledb'"))
        timescaledb_version = result.scalar()

        # Kiểm tra các hypertable
        result = await db.execute(text("""
            SELECT hypertable_name, num_chunks, compression_enabled
            FROM timescaledb_information.hypertables
        """))
        hypertables = [dict(row._mapping) for row in result.fetchall()]

        # Kiểm tra thống kê nén dữ liệu
        result = await db.execute(text("""
            SELECT
                hypertable_name,
                SUM(before_compression_total_bytes) as before_compression,
                SUM(after_compression_total_bytes) as after_compression,
                ROUND(
                    (SUM(before_compression_total_bytes) - SUM(after_compression_total_bytes))::numeric /
                    SUM(before_compression_total_bytes) * 100, 2
                ) as compression_ratio_percent
            FROM timescaledb_information.chunks
            WHERE compression_status = 'Compressed'
            GROUP BY hypertable_name
        """))
        compression_stats = [dict(row._mapping) for row in result.fetchall()]

        return {
            "status": "success",
            "timescaledb_version": timescaledb_version,
            "hypertables": hypertables,
            "compression_stats": compression_stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Lỗi khi lấy TimescaleDB info: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/storage/info")
async def get_storage_info(db: AsyncSession = Depends(get_db_session)):
    """Endpoint để xem thông tin storage và lưu trữ vĩnh viễn"""
    try:
        from sqlalchemy import text

        # Lấy thông tin kích thước database
        result = await db.execute(text("SELECT * FROM get_database_size_info()"))
        size_info = [dict(row._mapping) for row in result.fetchall()]

        # Lấy thống kê compression
        result = await db.execute(text("SELECT * FROM get_compression_stats()"))
        compression_stats = [dict(row._mapping) for row in result.fetchall()]

        # Lấy storage monitoring
        result = await db.execute(text("SELECT * FROM storage_monitoring"))
        storage_monitoring = [dict(row._mapping) for row in result.fetchall()]

        # Tính tổng kích thước
        total_size_bytes = sum(item['size_bytes'] for item in size_info)
        total_size_gb = round(total_size_bytes / (1024**3), 2)

        return {
            "status": "success",
            "storage_policy": "Lưu trữ vĩnh viễn - Không có retention policy",
            "total_size_gb": total_size_gb,
            "table_sizes": size_info,
            "compression_stats": compression_stats,
            "storage_monitoring": storage_monitoring,
            "recommendations": [
                "Chạy optimize_storage() định kỳ để tối ưu hiệu suất",
                "Monitor compression ratio để tiết kiệm storage",
                "Backup metadata định kỳ",
                "Theo dõi growth rate để lập kế hoạch storage"
            ],
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Lỗi khi lấy storage info: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.post("/storage/optimize")
async def optimize_storage(db: AsyncSession = Depends(get_db_session)):
    """Endpoint để tối ưu hóa storage"""
    try:
        from sqlalchemy import text

        # Chạy function optimize_storage
        result = await db.execute(text("SELECT optimize_storage()"))
        optimization_result = result.scalar()

        return {
            "status": "success",
            "message": "Tối ưu hóa storage hoàn tất",
            "details": optimization_result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Lỗi khi tối ưu storage: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/storage/report")
async def get_monthly_storage_report(db: AsyncSession = Depends(get_db_session)):
    """Endpoint để lấy báo cáo storage hàng tháng"""
    try:
        from sqlalchemy import text

        # Tạo báo cáo hàng tháng
        result = await db.execute(text("SELECT generate_monthly_storage_report()"))
        report = result.scalar()

        return {
            "status": "success",
            "report": report,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Lỗi khi tạo báo cáo storage: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/")
async def root():
    """Endpoint gốc chuyển hướng đến tài liệu API"""
    return {"message": "Chào mừng đến với XỔ SỐ TV Chatbot API", "docs": "/api/docs"}
