import redis.asyncio as redis
from typing import Optional, Any, Dict, List, Union, Tuple
import json
import logging
import hashlib
from datetime import timedelta

from app.config import settings

logger = logging.getLogger("redis-client")

# Redis client toàn cục
_redis: Optional[redis.Redis] = None

async def init_redis():
    """Khởi tạo kết nối Redis"""
    global _redis

    if _redis is None:
        logger.info(f"Đang kết nối đến Redis: {settings.redis.host}:{settings.redis.port}")
        _redis = redis.Redis(
            host=settings.redis.host,
            port=settings.redis.port,
            db=settings.redis.db,
            password=settings.redis.password,
            decode_responses=True,
            socket_timeout=settings.redis.socket_timeout,
            socket_connect_timeout=settings.redis.socket_connect_timeout,
            retry_on_timeout=settings.redis.retry_on_timeout,
            max_connections=settings.redis.max_connections,
            health_check_interval=30  # <PERSON><PERSON><PERSON> tra kết nối mỗi 30 giây
        )

        # <PERSON><PERSON><PERSON> tra kết nối
        try:
            await _redis.ping()
            logger.info(f"Kết nối Redis đã được khởi tạo: {settings.redis.host}:{settings.redis.port}")
        except Exception as e:
            logger.error(f"Lỗi khi kết nối đến Redis: {str(e)}")
            raise

async def close_redis():
    """Đóng kết nối Redis"""
    global _redis

    if _redis:
        await _redis.close()
        _redis = None
        logger.info("Kết nối Redis đã được đóng")

async def get_redis_connection():
    """Lấy kết nối Redis"""
    global _redis

    if _redis is None:
        await init_redis()

    return _redis

def generate_cache_key(prefix: str, *args, **kwargs) -> str:
    """Tạo khóa cache từ các tham số"""
    key_parts = [settings.cache_prefix + prefix]

    # Thêm các tham số vị trí
    for arg in args:
        if isinstance(arg, (str, int, float, bool)):
            key_parts.append(str(arg))
        else:
            # Hash các đối tượng phức tạp
            key_parts.append(hashlib.md5(json.dumps(arg, sort_keys=True).encode()).hexdigest())

    # Thêm các tham số từ khóa, sắp xếp để đảm bảo tính nhất quán
    for k, v in sorted(kwargs.items()):
        if isinstance(v, (str, int, float, bool)):
            key_parts.append(f"{k}:{v}")
        else:
            # Hash các đối tượng phức tạp
            key_parts.append(f"{k}:{hashlib.md5(json.dumps(v, sort_keys=True).encode()).hexdigest()}")

    return ":".join(key_parts)

class CacheService:
    """Dịch vụ cache với Redis"""

    # Các prefix cho các loại cache khác nhau
    CHAT_PREFIX = "chat"
    CONTEXT_PREFIX = "context"
    HOT_QUERY_PREFIX = "hot_query"
    HOT_RESPONSE_PREFIX = "hot_response"
    LOTTERY_PREFIX = "lottery"
    HISTORY_PREFIX = "history"
    STATS_PREFIX = "stats"

    @staticmethod
    async def get(key: str) -> Optional[str]:
        """Lấy giá trị từ cache"""
        redis = await get_redis_connection()
        return await redis.get(key)

    @staticmethod
    async def set(key: str, value: str, expire: int = None) -> bool:
        """Lưu giá trị vào cache"""
        redis = await get_redis_connection()
        return await redis.set(key, value, ex=expire or settings.cache_ttl)

    @staticmethod
    async def delete(key: str) -> int:
        """Xóa khóa khỏi cache"""
        redis = await get_redis_connection()
        return await redis.delete(key)

    @staticmethod
    async def exists(key: str) -> bool:
        """Kiểm tra xem khóa có tồn tại không"""
        redis = await get_redis_connection()
        return await redis.exists(key) > 0

    @staticmethod
    async def get_json(key: str) -> Optional[Dict[str, Any]]:
        """Lấy và phân tích JSON từ cache"""
        value = await CacheService.get(key)
        if value:
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                logger.warning(f"Không thể phân tích JSON từ cache key: {key}")
        return None

    @staticmethod
    async def set_json(key: str, value: Dict[str, Any], expire: int = None) -> bool:
        """Lưu đối tượng JSON vào cache"""
        from datetime import datetime

        def json_serializer(obj):
            """Custom JSON serializer để handle datetime objects"""
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

        return await CacheService.set(key, json.dumps(value, default=json_serializer), expire)

    @staticmethod
    async def increment(key: str, amount: int = 1) -> int:
        """Tăng giá trị của khóa"""
        redis = await get_redis_connection()
        return await redis.incrby(key, amount)

    @staticmethod
    async def expire(key: str, seconds: int) -> bool:
        """Đặt thời gian hết hạn cho khóa"""
        redis = await get_redis_connection()
        return await redis.expire(key, seconds)

    @staticmethod
    async def ttl(key: str) -> int:
        """Lấy thời gian còn lại của khóa"""
        redis = await get_redis_connection()
        return await redis.ttl(key)

    @staticmethod
    async def keys(pattern: str) -> List[str]:
        """Lấy danh sách khóa theo mẫu"""
        redis = await get_redis_connection()
        return await redis.keys(pattern)

    @staticmethod
    async def flush_all() -> bool:
        """Xóa tất cả các khóa trong DB hiện tại"""
        redis = await get_redis_connection()
        return await redis.flushdb()

    @staticmethod
    async def hash_set(key: str, mapping: Dict[str, Any], expire: int = None) -> bool:
        """Lưu hash map vào cache"""
        redis = await get_redis_connection()
        await redis.hset(key, mapping=mapping)
        if expire:
            await redis.expire(key, expire)
        return True

    @staticmethod
    async def hash_get(key: str, field: str = None) -> Union[str, Dict[str, str]]:
        """Lấy giá trị từ hash map"""
        redis = await get_redis_connection()
        if field:
            return await redis.hget(key, field)
        return await redis.hgetall(key)

    @staticmethod
    async def list_push(key: str, *values, expire: int = None) -> int:
        """Thêm giá trị vào danh sách"""
        redis = await get_redis_connection()
        result = await redis.rpush(key, *values)
        if expire:
            await redis.expire(key, expire)
        return result

    @staticmethod
    async def list_range(key: str, start: int = 0, end: int = -1) -> List[str]:
        """Lấy phạm vi từ danh sách"""
        redis = await get_redis_connection()
        return await redis.lrange(key, start, end)

    @staticmethod
    async def set_add(key: str, *values, expire: int = None) -> int:
        """Thêm giá trị vào tập hợp"""
        redis = await get_redis_connection()
        result = await redis.sadd(key, *values)
        if expire:
            await redis.expire(key, expire)
        return result

    @staticmethod
    async def set_members(key: str) -> List[str]:
        """Lấy tất cả các thành viên của tập hợp"""
        redis = await get_redis_connection()
        return await redis.smembers(key)

    @staticmethod
    async def publish(channel: str, message: str) -> int:
        """Xuất bản thông điệp đến kênh"""
        redis = await get_redis_connection()
        return await redis.publish(channel, message)

    # Các phương thức cho hot queries

    @staticmethod
    async def track_query(query: str) -> int:
        """Theo dõi truy vấn và tăng bộ đếm"""
        # Chuẩn hóa truy vấn (lowercase, loại bỏ khoảng trắng thừa)
        normalized_query = query.lower().strip()

        # Tạo khóa cho bộ đếm truy vấn
        key = generate_cache_key(CacheService.STATS_PREFIX, "query_count", query=normalized_query)

        # Tăng bộ đếm và đặt thời gian hết hạn
        redis = await get_redis_connection()
        count = await redis.incr(key)

        # Đặt thời gian hết hạn nếu là lần đầu tiên
        if count == 1:
            await redis.expire(key, settings.hot_query_window)

        # Nếu đạt ngưỡng hot query, thêm vào danh sách hot queries
        if count >= settings.hot_query_threshold:
            hot_queries_key = generate_cache_key(CacheService.HOT_QUERY_PREFIX, "list")
            await redis.zadd(hot_queries_key, {normalized_query: count})
            await redis.expire(hot_queries_key, settings.hot_query_window)

            logger.debug(f"Truy vấn '{normalized_query}' đã trở thành hot query với {count} lần truy vấn")

        return count

    @staticmethod
    async def get_hot_queries(limit: int = 10) -> List[Dict[str, Any]]:
        """Lấy danh sách các truy vấn hot"""
        hot_queries_key = generate_cache_key(CacheService.HOT_QUERY_PREFIX, "list")
        redis = await get_redis_connection()

        # Lấy các truy vấn hot với số lượng truy vấn, sắp xếp giảm dần
        result = await redis.zrevrange(hot_queries_key, 0, limit-1, withscores=True)

        # Chuyển đổi kết quả thành danh sách dict
        hot_queries = []
        for query, count in result:
            hot_queries.append({
                "query": query,
                "count": int(count)
            })

        return hot_queries

    @staticmethod
    async def cache_hot_response(query: str, response: str) -> bool:
        """Lưu trữ phản hồi cho truy vấn hot"""
        # Chuẩn hóa truy vấn
        normalized_query = query.lower().strip()

        # Kiểm tra xem có phải là hot query không
        stats_key = generate_cache_key(CacheService.STATS_PREFIX, "query_count", query=normalized_query)
        redis = await get_redis_connection()
        count = await redis.get(stats_key)

        if count and int(count) >= settings.hot_query_threshold:
            # Lưu phản hồi vào cache
            response_key = generate_cache_key(CacheService.HOT_RESPONSE_PREFIX, normalized_query)
            await redis.set(response_key, response, ex=settings.cache_hot_query_ttl)
            logger.debug(f"Phản hồi cho hot query '{normalized_query}' đã được cache")
            return True

        return False

    @staticmethod
    async def get_hot_response(query: str) -> Optional[str]:
        """Lấy phản hồi đã cache cho truy vấn hot"""
        # Chuẩn hóa truy vấn
        normalized_query = query.lower().strip()

        # Lấy phản hồi từ cache
        response_key = generate_cache_key(CacheService.HOT_RESPONSE_PREFIX, normalized_query)
        return await CacheService.get(response_key)
