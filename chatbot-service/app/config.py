import os
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class DatabaseSettings(BaseModel):
    host: str = os.getenv("POSTGRES_HOST", "postgres")
    port: int = int(os.getenv("POSTGRES_PORT", "5432"))
    user: str = os.getenv("POSTGRES_USER", "xosotv_user")
    password: str = os.getenv("POSTGRES_PASSWORD", "")
    database: str = os.getenv("POSTGRES_DB", "xosotv")

    # SQLAlchemy connection pool settings
    pool_size: int = int(os.getenv("POSTGRES_POOL_SIZE", "5"))
    max_overflow: int = int(os.getenv("POSTGRES_MAX_OVERFLOW", "10"))
    pool_timeout: int = int(os.getenv("POSTGRES_POOL_TIMEOUT", "30"))
    pool_recycle: int = int(os.getenv("POSTGRES_POOL_RECYCLE", "1800"))  # 30 phút

    @property
    def connection_string(self) -> str:
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"

    @property
    def async_connection_string(self) -> str:
        return f"postgresql+asyncpg://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"

class RedisSettings(BaseModel):
    host: str = os.getenv("REDIS_HOST", "redis")
    port: int = int(os.getenv("REDIS_PORT", "6379"))
    db: int = int(os.getenv("REDIS_DB", "0"))
    password: Optional[str] = os.getenv("REDIS_PASSWORD", None)
    socket_timeout: float = float(os.getenv("REDIS_SOCKET_TIMEOUT", "5.0"))
    socket_connect_timeout: float = float(os.getenv("REDIS_CONNECT_TIMEOUT", "5.0"))
    retry_on_timeout: bool = os.getenv("REDIS_RETRY_ON_TIMEOUT", "True").lower() == "true"
    max_connections: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "10"))

    @property
    def connection_string(self) -> str:
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        return f"redis://{self.host}:{self.port}/{self.db}"

class LLMSettings(BaseModel):
    host: str = os.getenv("LLM_CORE_HOST", "llm-core")
    port: int = int(os.getenv("LLM_CORE_PORT", "8080"))
    timeout: float = float(os.getenv("LLM_TIMEOUT", "30.0"))  # Timeout cho request LLM (giây)
    max_retries: int = int(os.getenv("LLM_MAX_RETRIES", "3"))  # Số lần thử lại tối đa
    retry_delay: float = float(os.getenv("LLM_RETRY_DELAY", "1.0"))  # Thời gian chờ giữa các lần thử (giây)
    health_check_interval: int = int(os.getenv("LLM_HEALTH_CHECK_INTERVAL", "60"))  # Khoảng thời gian giữa các lần health check (giây)
    default_max_tokens: int = int(os.getenv("LLM_DEFAULT_MAX_TOKENS", "512"))  # Số token mặc định
    default_temperature: float = float(os.getenv("LLM_DEFAULT_TEMPERATURE", "0.7"))  # Nhiệt độ mặc định
    default_top_p: float = float(os.getenv("LLM_DEFAULT_TOP_P", "0.95"))  # Top-p mặc định

    @property
    def api_url(self) -> str:
        return f"http://{self.host}:{self.port}/v1"

class SecuritySettings(BaseModel):
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    algorithm: str = os.getenv("JWT_ALGORITHM", "HS256")
    access_token_expire_minutes: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    refresh_token_expire_days: int = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

class LoggingSettings(BaseModel):
    level: str = os.getenv("LOG_LEVEL", "INFO")
    format: str = "%(asctime)s | %(levelname)s | %(name)s | %(message)s"
    file_path: Optional[str] = os.getenv("LOG_FILE", "logs/chatbot-service.log")
    json_file_path: Optional[str] = os.getenv("JSON_LOG_FILE", "logs/chatbot-service.json.log")
    rotation: str = os.getenv("LOG_ROTATION", "1 day")
    retention: str = os.getenv("LOG_RETENTION", "7 days")
    log_request_body: bool = os.getenv("LOG_REQUEST_BODY", "False").lower() == "true"
    log_response_body: bool = os.getenv("LOG_RESPONSE_BODY", "False").lower() == "true"

class Settings(BaseModel):
    app_name: str = "XỔ SỐ TV Chatbot"
    debug: bool = os.getenv("DEBUG", "False").lower() == "true"
    timezone: str = os.getenv("TZ", "Asia/Ho_Chi_Minh")
    environment: str = os.getenv("ENVIRONMENT", "development")
    version: str = os.getenv("VERSION", "1.0.0")
    port: int = int(os.getenv("CHATBOT_PORT", "8000"))

    # CORS settings
    cors_origins: List[str] = Field(
        default_factory=lambda: os.getenv("CORS_ORIGINS", "*").split(",")
    )

    # Cài đặt cache
    cache_ttl: int = int(os.getenv("CACHE_TTL", "3600"))  # Mặc định 1 giờ
    cache_prefix: str = os.getenv("CACHE_PREFIX", "xosotv:")

    # Cài đặt cache cho các loại dữ liệu cụ thể
    cache_chat_ttl: int = int(os.getenv("CACHE_CHAT_TTL", "3600"))  # Cache cho chat (1 giờ)
    cache_context_ttl: int = int(os.getenv("CACHE_CONTEXT_TTL", "86400"))  # Cache cho context (1 ngày)
    cache_hot_query_ttl: int = int(os.getenv("CACHE_HOT_QUERY_TTL", "43200"))  # Cache cho truy vấn hot (12 giờ)
    cache_lottery_ttl: int = int(os.getenv("CACHE_LOTTERY_TTL", "86400"))  # Cache cho kết quả xổ số (1 ngày)

    # Cài đặt cho hot queries
    hot_query_threshold: int = int(os.getenv("HOT_QUERY_THRESHOLD", "5"))  # Số lần truy vấn để coi là hot
    hot_query_window: int = int(os.getenv("HOT_QUERY_WINDOW", "86400"))  # Cửa sổ thời gian cho hot query (1 ngày)

    # Cài đặt rate limiting
    rate_limit_enabled: bool = os.getenv("RATE_LIMIT_ENABLED", "True").lower() == "true"
    rate_limit_requests: int = int(os.getenv("RATE_LIMIT_REQUESTS", "100"))
    rate_limit_window: int = int(os.getenv("RATE_LIMIT_WINDOW", "3600"))  # Giải hạn trong 1 giờ

    # Kết nối các dịch vụ
    db: DatabaseSettings = DatabaseSettings()
    redis: RedisSettings = RedisSettings()
    llm: LLMSettings = LLMSettings()
    security: SecuritySettings = SecuritySettings()
    logging: LoggingSettings = LoggingSettings()

# Tạo instance cài đặt toàn cục
settings = Settings()
