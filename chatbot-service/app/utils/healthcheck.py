import logging
import time
from typing import Dict, Any, List, <PERSON><PERSON>
import httpx
import asyncpg
import redis.asyncio as redis

from app.config import settings
from app.db.postgresql import get_db_connection
from app.cache.redis_client import get_redis_connection
from app.services.llm_client import LLMClientService

logger = logging.getLogger("healthcheck")

async def check_database() -> Tuple[bool, str]:
    """
    Kiểm tra kết nối đến PostgreSQL
    
    Returns:
        Tuple[bool, str]: (trạng thái, thông báo)
    """
    try:
        start_time = time.time()
        pool = await get_db_connection()
        
        # Thực hiện truy vấn đơn giản
        result = await pool.fetchval("SELECT 1")
        
        if result == 1:
            elapsed = time.time() - start_time
            return True, f"Kết nối PostgreSQL OK ({elapsed:.3f}s)"
        else:
            return False, "<PERSON>ết nối PostgreSQL thất bại: <PERSON><PERSON><PERSON> vấn không trả về kết quả mong đợi"
    except asyncpg.exceptions.PostgresError as e:
        return False, f"Lỗi PostgreSQL: {str(e)}"
    except Exception as e:
        return False, f"Lỗi không xác định khi kết nối PostgreSQL: {str(e)}"

async def check_redis() -> Tuple[bool, str]:
    """
    Kiểm tra kết nối đến Redis
    
    Returns:
        Tuple[bool, str]: (trạng thái, thông báo)
    """
    try:
        start_time = time.time()
        redis_client = await get_redis_connection()
        
        # Thực hiện lệnh đơn giản
        result = await redis_client.ping()
        
        if result:
            elapsed = time.time() - start_time
            return True, f"Kết nối Redis OK ({elapsed:.3f}s)"
        else:
            return False, "Kết nối Redis thất bại: Lệnh PING không trả về kết quả mong đợi"
    except redis.RedisError as e:
        return False, f"Lỗi Redis: {str(e)}"
    except Exception as e:
        return False, f"Lỗi không xác định khi kết nối Redis: {str(e)}"

async def check_llm_core() -> Tuple[bool, str]:
    """
    Kiểm tra kết nối đến LLM Core
    
    Returns:
        Tuple[bool, str]: (trạng thái, thông báo)
    """
    try:
        start_time = time.time()
        llm_client = LLMClientService()
        
        # Kiểm tra kết nối
        is_healthy = await llm_client.is_healthy(force_check=True)
        
        if is_healthy:
            # Lấy thông tin model
            model_info = await llm_client.get_model_info()
            model_name = model_info.get("model", "unknown")
            
            elapsed = time.time() - start_time
            return True, f"Kết nối LLM Core OK ({elapsed:.3f}s) - Model: {model_name}"
        else:
            return False, "Kết nối LLM Core thất bại: Dịch vụ không khả dụng"
    except httpx.HTTPError as e:
        return False, f"Lỗi HTTP khi kết nối LLM Core: {str(e)}"
    except Exception as e:
        return False, f"Lỗi không xác định khi kết nối LLM Core: {str(e)}"

async def check_internal_endpoints() -> List[Dict[str, Any]]:
    """
    Kiểm tra các endpoint nội bộ quan trọng
    
    Returns:
        List[Dict[str, Any]]: Danh sách kết quả kiểm tra
    """
    results = []
    
    # Danh sách các endpoint cần kiểm tra
    endpoints = [
        {"name": "API Root", "path": "/api/v1", "method": "GET"},
        {"name": "Chat API", "path": "/api/v1/chat", "method": "POST", "data": {"user_id": "healthcheck", "message": "Hello"}},
    ]
    
    async with httpx.AsyncClient(timeout=5.0) as client:
        for endpoint in endpoints:
            try:
                start_time = time.time()
                
                if endpoint["method"] == "GET":
                    response = await client.get(f"http://localhost:{settings.port}{endpoint['path']}")
                elif endpoint["method"] == "POST":
                    response = await client.post(
                        f"http://localhost:{settings.port}{endpoint['path']}",
                        json=endpoint.get("data", {})
                    )
                else:
                    continue
                
                elapsed = time.time() - start_time
                
                results.append({
                    "name": endpoint["name"],
                    "status": response.status_code < 400,
                    "status_code": response.status_code,
                    "response_time": f"{elapsed:.3f}s",
                    "message": f"HTTP {response.status_code}"
                })
            except Exception as e:
                results.append({
                    "name": endpoint["name"],
                    "status": False,
                    "status_code": None,
                    "response_time": None,
                    "message": f"Lỗi: {str(e)}"
                })
    
    return results

async def get_system_info() -> Dict[str, Any]:
    """
    Lấy thông tin hệ thống

    Returns:
        Dict[str, Any]: Thông tin hệ thống
    """
    import platform
    import os
    import shutil

    try:
        # Thông tin cơ bản từ platform
        system_info = {
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "python_version": platform.python_version(),
            "architecture": platform.architecture()[0]
        }

        # Thử lấy thông tin bộ nhớ từ /proc/meminfo (Linux)
        try:
            if os.path.exists("/proc/meminfo"):
                with open("/proc/meminfo", "r") as f:
                    meminfo = f.read()
                    lines = meminfo.split("\n")
                    mem_total = None
                    mem_available = None

                    for line in lines:
                        if line.startswith("MemTotal:"):
                            mem_total = int(line.split()[1]) * 1024  # Convert KB to bytes
                        elif line.startswith("MemAvailable:"):
                            mem_available = int(line.split()[1]) * 1024  # Convert KB to bytes

                    if mem_total and mem_available:
                        mem_used = mem_total - mem_available
                        system_info["memory"] = {
                            "total": f"{mem_total / (1024**3):.2f} GB",
                            "used": f"{mem_used / (1024**3):.2f} GB",
                            "available": f"{mem_available / (1024**3):.2f} GB",
                            "percent": f"{(mem_used / mem_total) * 100:.1f}%"
                        }
        except Exception:
            pass

        # Thông tin đĩa
        try:
            disk_usage = shutil.disk_usage("/")
            disk_total = disk_usage.total / (1024**3)  # GB
            disk_used = (disk_usage.total - disk_usage.free) / (1024**3)  # GB
            disk_free = disk_usage.free / (1024**3)  # GB

            system_info["disk"] = {
                "total": f"{disk_total:.2f} GB",
                "used": f"{disk_used:.2f} GB",
                "free": f"{disk_free:.2f} GB",
                "percent": f"{(disk_used / disk_total) * 100:.1f}%"
            }
        except Exception:
            pass

        return system_info

    except Exception as e:
        logger.error(f"Lỗi khi lấy thông tin hệ thống: {str(e)}")
        return {
            "system": platform.system(),
            "error": f"Không thể lấy thông tin chi tiết: {str(e)}"
        }

async def get_health_status() -> Dict[str, Any]:
    """
    Lấy trạng thái sức khỏe tổng thể của hệ thống
    
    Returns:
        Dict[str, Any]: Trạng thái sức khỏe
    """
    # Kiểm tra các thành phần
    db_status, db_message = await check_database()
    redis_status, redis_message = await check_redis()
    llm_status, llm_message = await check_llm_core()
    
    # Kiểm tra các endpoint
    endpoint_results = await check_internal_endpoints()
    
    # Lấy thông tin hệ thống
    system_info = await get_system_info()
    
    # Tính toán trạng thái tổng thể
    overall_status = all([
        db_status,
        redis_status,
        llm_status,
        all(result["status"] for result in endpoint_results)
    ])
    
    # Tạo kết quả
    return {
        "service": "chatbot-service",
        "status": "healthy" if overall_status else "unhealthy",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "version": settings.version,
        "environment": settings.environment,
        "components": {
            "database": {
                "status": "up" if db_status else "down",
                "message": db_message
            },
            "redis": {
                "status": "up" if redis_status else "down",
                "message": redis_message
            },
            "llm_core": {
                "status": "up" if llm_status else "down",
                "message": llm_message
            }
        },
        "endpoints": endpoint_results,
        "system_info": system_info
    }
