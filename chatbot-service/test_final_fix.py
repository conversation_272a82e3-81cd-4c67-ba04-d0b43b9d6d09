#!/usr/bin/env python3
"""
Final test để kiểm tra RecursionError đã được sửa hoàn toàn
"""

print("=== FINAL RECURSION ERROR FIX TEST ===")

# Test 1: Import schemas
try:
    print("1. Testing schema imports...")
    from app.schemas.lottery import (
        LotteryTypeSchema, 
        LotteryResultResponseSchema,
        LotteryTypesResponseSchema,
        LotteryResultsResponseSchema
    )
    print("✅ Schema imports OK")
except Exception as e:
    print(f"❌ Schema imports failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 2: Test schema instantiation
try:
    print("2. Testing schema instantiation...")
    
    lottery_type = LotteryTypeSchema(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Xổ số miền Bắc"
    )
    
    print(f"✅ Schema instantiation OK: {lottery_type.name}")
    
except Exception as e:
    print(f"❌ Schema instantiation failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 3: Test response schemas (không có forward references)
try:
    print("3. Testing response schemas...")
    
    # Test LotteryTypesResponseSchema
    types_response = LotteryTypesResponseSchema(
        types=[lottery_type]
    )
    print(f"✅ LotteryTypesResponseSchema OK: {len(types_response.types)} types")
    
    # Test LotteryResultResponseSchema
    result = LotteryResultResponseSchema(
        id=1,
        lottery_type="Miền Bắc",
        lottery_code="mb",
        region="Bắc",
        draw_date="2024-01-01",
        results={"special": "12345"},
        formatted_results="Giải đặc biệt: 12345"
    )
    
    # Test LotteryResultsResponseSchema
    results_response = LotteryResultsResponseSchema(
        results=[result],
        count=1
    )
    print(f"✅ LotteryResultsResponseSchema OK: {results_response.count} results")
    
except Exception as e:
    print(f"❌ Response schemas failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 4: Import models
try:
    print("4. Testing model imports...")
    from app.models.lottery import LotteryTypeModel, LotteryDrawModel
    print("✅ Model imports OK")
except Exception as e:
    print(f"❌ Model imports failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 5: Import services
try:
    print("5. Testing service imports...")
    from app.services.lottery import LotteryService
    print("✅ Service imports OK")
except Exception as e:
    print(f"❌ Service imports failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 ALL TESTS PASSED!")
print("✅ RecursionError has been completely resolved!")
print("✅ All schemas, models, and services can be imported successfully!")
print("✅ No forward references needed!")
print("✅ System is ready to use!")
print("✅ Schemas work perfectly with proper class ordering!")
