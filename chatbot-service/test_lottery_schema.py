#!/usr/bin/env python3
"""
Test lottery schema imports để tìm RecursionError
"""

print("=== TESTING LOTTERY SCHEMA IMPORTS ===")

# Test 1: Basic imports
try:
    print("1. Testing basic imports...")
    from typing import Dict, Any, List, Optional
    from datetime import date
    print("✅ Basic imports OK")
except Exception as e:
    print(f"❌ Basic imports failed: {str(e)}")
    exit(1)

# Test 2: Pydantic
try:
    print("2. Testing Pydantic...")
    from pydantic import BaseModel, Field
    print("✅ Pydantic OK")
except Exception as e:
    print(f"❌ Pydantic failed: {str(e)}")
    exit(1)

# Test 3: Simple schema
try:
    print("3. Testing simple schema...")
    
    class SimpleSchema(BaseModel):
        id: int
        name: str
        
    print("✅ Simple schema OK")
except Exception as e:
    print(f"❌ Simple schema failed: {str(e)}")
    exit(1)

# Test 4: Schema with date
try:
    print("4. Testing schema with date...")
    
    class DateSchema(BaseModel):
        id: int
        created_date: date
        
    print("✅ Schema with date OK")
except Exception as e:
    print(f"❌ Schema with date failed: {str(e)}")
    exit(1)

# Test 5: Schema with Dict[str, Any]
try:
    print("5. Testing schema with Dict[str, Any]...")
    
    class DictSchema(BaseModel):
        id: int
        data: Dict[str, Any]
        
    print("✅ Schema with Dict OK")
except Exception as e:
    print(f"❌ Schema with Dict failed: {str(e)}")
    exit(1)

# Test 6: Schema with inheritance
try:
    print("6. Testing schema inheritance...")
    
    class BaseSchema(BaseModel):
        id: int
        name: str
        
    class ExtendedSchema(BaseSchema):
        description: str
        
    print("✅ Schema inheritance OK")
except Exception as e:
    print(f"❌ Schema inheritance failed: {str(e)}")
    exit(1)

# Test 7: Schema with Field
try:
    print("7. Testing schema with Field...")
    
    class FieldSchema(BaseModel):
        id: int = Field(..., description="ID field")
        name: str = Field(..., description="Name field")
        
    print("✅ Schema with Field OK")
except Exception as e:
    print(f"❌ Schema with Field failed: {str(e)}")
    exit(1)

# Test 8: Schema with Optional and List
try:
    print("8. Testing schema with Optional and List...")
    
    class ComplexSchema(BaseModel):
        id: int
        name: str
        tags: Optional[List[str]] = None
        metadata: Optional[Dict[str, Any]] = None
        
    print("✅ Complex schema OK")
except Exception as e:
    print(f"❌ Complex schema failed: {str(e)}")
    exit(1)

# Test 9: Recreate LotteryTypeBase
try:
    print("9. Testing LotteryTypeBase recreation...")
    
    class LotteryTypeBase(BaseModel):
        code: str = Field(..., description="Mã loại xổ số")
        name: str = Field(..., description="Tên loại xổ số")
        region: Optional[str] = Field(None, description="Khu vực")
        description: Optional[str] = Field(None, description="Mô tả")
        
    print("✅ LotteryTypeBase recreation OK")
except Exception as e:
    print(f"❌ LotteryTypeBase recreation failed: {str(e)}")
    exit(1)

# Test 10: Recreate LotteryType
try:
    print("10. Testing LotteryType recreation...")
    
    class LotteryType(LotteryTypeBase):
        id: int = Field(..., description="ID loại xổ số")

        class Config:
            from_attributes = True
            
    print("✅ LotteryType recreation OK")
except Exception as e:
    print(f"❌ LotteryType recreation failed: {str(e)}")
    exit(1)

# Test 11: Actual lottery schema import
try:
    print("11. Testing actual lottery schema import...")
    from app.schemas.lottery import LotteryType
    print("✅ Actual lottery schema import OK")
except Exception as e:
    print(f"❌ Actual lottery schema import failed: {str(e)}")
    print(f"Error details: {repr(e)}")

print("=== LOTTERY SCHEMA TESTING COMPLETE ===")
