#!/usr/bin/env python3
"""
Test clean schema without circular imports
"""

print("=== TESTING CLEAN SCHEMA ===")

# Test 1: Import clean schema
try:
    print("1. Testing clean schema import...")
    from app.schemas.lottery_clean import LotteryTypeSchema, LotteryResultResponseSchema
    print("✅ Clean schema import OK")
except Exception as e:
    print(f"❌ Clean schema import failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 2: Test schema instantiation
try:
    print("2. Testing schema instantiation...")
    
    lottery_type = LotteryTypeSchema(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Xổ số miền Bắc"
    )
    
    print(f"✅ Schema instantiation OK: {lottery_type.name}")
    print(f"Schema data: {lottery_type.model_dump()}")
    
except Exception as e:
    print(f"❌ Schema instantiation failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 3: Test original schema
try:
    print("3. Testing original schema import...")
    from app.schemas.lottery import LotteryTypeSchema as OriginalLotteryTypeSchema
    print("✅ Original schema import OK")
    
    # Test instantiation
    original_lottery = OriginalLotteryTypeSchema(
        id=2,
        code="mt",
        name="Miền Trung",
        region="Trung",
        description="Xổ số miền Trung"
    )
    
    print(f"✅ Original schema instantiation OK: {original_lottery.name}")
    
except Exception as e:
    print(f"❌ Original schema import failed: {e}")
    import traceback
    traceback.print_exc()

print("\n🎉 Clean schema test completed!")
print("✅ Clean schemas work perfectly!")
