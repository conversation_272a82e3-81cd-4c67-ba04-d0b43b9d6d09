# API Tra cứu kết quả xổ số

## Giới thiệu

API tra cứu kết quả xổ số cho phép người dùng truy vấn kết quả xổ số theo ng<PERSON>, lo<PERSON>i xổ số, và các tiêu chí khác. API này được tích hợp vào chatbot-service và cung cấp các endpoint RESTful để truy vấn dữ liệu.

## Endpoints

### 1. Lấy danh sách loại xổ số

```
GET /api/v1/lottery/types
```

Trả về danh sách các loại xổ số có trong hệ thống.

**Phản hồi:**

```json
{
  "types": [
    {
      "id": 1,
      "code": "mb",
      "name": "Xổ số Miền Bắc",
      "region": "Miền Bắc",
      "description": "Xổ số kiến thiết Miền Bắc"
    },
    {
      "id": 2,
      "code": "mt",
      "name": "<PERSON>ổ số Miền Trung",
      "region": "Miền Trung",
      "description": "<PERSON>ổ số kiến thiết Miền Trung"
    }
  ]
}
```

### 2. Lấy kết quả xổ số

```
GET /api/v1/lottery/results
```

Lấy kết quả xổ số theo ngày và loại (tùy chọn).

**Tham số:**

- `date` (tùy chọn): Ngày quay thưởng (YYYY-MM-DD)
- `type_code` (tùy chọn): Mã loại xổ số
- `limit` (tùy chọn): Số lượng kết quả tối đa (mặc định: 1, tối đa: 10)

**Phản hồi:**

```json
{
  "results": [
    {
      "id": 1,
      "lottery_type": "Xổ số Miền Bắc",
      "lottery_code": "mb",
      "region": "Miền Bắc",
      "draw_date": "2023-11-15",
      "results": {
        "special": "12345",
        "first": "67890",
        "second": ["11111", "22222"]
      },
      "formatted_results": "Kết quả xổ số Miền Bắc ngày 15/11/2023:\n\nGiải đặc biệt: 12345\nGiải nhất: 67890\nGiải nhì: 11111, 22222"
    }
  ],
  "count": 1
}
```

### 3. Lấy kết quả xổ số mới nhất

```
GET /api/v1/lottery/latest
```

Lấy kết quả xổ số mới nhất.

**Tham số:**

- `type_code` (tùy chọn): Mã loại xổ số
- `limit` (tùy chọn): Số lượng kết quả tối đa (mặc định: 1, tối đa: 10)

**Phản hồi:**

Tương tự như endpoint `/api/v1/lottery/results`.

### 4. Lấy kết quả xổ số theo ngày cụ thể

```
GET /api/v1/lottery/by-date/{date}
```

Lấy kết quả xổ số theo ngày cụ thể.

**Tham số:**

- `date`: Ngày quay thưởng (YYYY-MM-DD)
- `type_code` (tùy chọn): Mã loại xổ số

**Phản hồi:**

Tương tự như endpoint `/api/v1/lottery/results`.

### 5. Lấy kết quả xổ số của ngày hôm nay

```
GET /api/v1/lottery/today
```

Lấy kết quả xổ số của ngày hôm nay.

**Tham số:**

- `type_code` (tùy chọn): Mã loại xổ số

**Phản hồi:**

Tương tự như endpoint `/api/v1/lottery/results`.

### 6. Lấy kết quả xổ số của ngày hôm qua

```
GET /api/v1/lottery/yesterday
```

Lấy kết quả xổ số của ngày hôm qua.

**Tham số:**

- `type_code` (tùy chọn): Mã loại xổ số

**Phản hồi:**

Tương tự như endpoint `/api/v1/lottery/results`.

## Mô hình dữ liệu

### LotteryType

```json
{
  "id": 1,
  "code": "mb",
  "name": "Xổ số Miền Bắc",
  "region": "Miền Bắc",
  "description": "Xổ số kiến thiết Miền Bắc"
}
```

### LotteryResultResponse

```json
{
  "id": 1,
  "lottery_type": "Xổ số Miền Bắc",
  "lottery_code": "mb",
  "region": "Miền Bắc",
  "draw_date": "2023-11-15",
  "results": {
    "special": "12345",
    "first": "67890",
    "second": ["11111", "22222"]
  },
  "formatted_results": "Kết quả xổ số Miền Bắc ngày 15/11/2023:\n\nGiải đặc biệt: 12345\nGiải nhất: 67890\nGiải nhì: 11111, 22222"
}
```

## Caching

API sử dụng Redis để cache kết quả xổ số, giúp cải thiện hiệu suất và giảm tải cho database.

- Danh sách loại xổ số: Cache 7 ngày
- Kết quả xổ số theo ngày: Cache theo cấu hình `cache_lottery_ttl` (mặc định: 1 ngày)
- Kết quả xổ số mới nhất: Cache 1 giờ

## Ví dụ sử dụng

### Lấy danh sách loại xổ số

```bash
curl -X GET "http://localhost:8000/api/v1/lottery/types"
```

### Lấy kết quả xổ số theo ngày

```bash
curl -X GET "http://localhost:8000/api/v1/lottery/by-date/2023-11-15?type_code=mb"
```

### Lấy kết quả xổ số mới nhất

```bash
curl -X GET "http://localhost:8000/api/v1/lottery/latest?limit=3"
```

## Tích hợp với Chatbot

API này được tích hợp với chatbot thông qua phương thức `_process_lottery_query` trong `ChatbotService`. Khi người dùng hỏi về kết quả xổ số, chatbot sẽ trích xuất ngày và loại xổ số từ tin nhắn, sau đó sử dụng `LotteryService` để lấy kết quả và trả về cho người dùng.

```python
# Trích xuất ngày từ tin nhắn
query_date = extract_date_from_message(message)

# Sử dụng LotteryService để lấy kết quả xổ số
lottery_service = LotteryService(db)
results = await lottery_service.get_lottery_results_by_date(query_date)

# Trả về kết quả cho người dùng
return results[0].formatted_results
```
