# XỔ SỐ TV Chatbot Service

Dịch vụ chatbot cho hệ thống XỔ SỐ TV, xử lý các tương tác chat, truy vấn kết quả xổ số và tích hợp với LLM.

## Tính năng chính

- Xử lý tin nhắn chat từ người dùng
- <PERSON><PERSON><PERSON> hiện ý định (intent detection)
- Truy vấn kết quả xổ số từ cơ sở dữ liệu
- Tích hợp với LLM Core để xử lý các câu hỏi chung
- Lưu trữ và quản lý lịch sử chat
- Caching thông minh với Redis
- Rate limiting để bảo vệ API

## Cấu trúc dự án

```
chatbot-service/
├── Dockerfile
├── requirements.txt
├── start-container.sh
├── app/
│   ├── __init__.py
│   ├── main.py          # Điểm vào FastAPI
│   ├── config.py        # Cấu hình từ biến môi trường
│   ├── api/             # Đ<PERSON>nh nghĩa các route
│   │   ├── __init__.py
│   │   └── v1.py
│   ├── services/        # Logic, business layer
│   │   ├── __init__.py
│   │   └── chatbot.py
│   ├── db/              # Kết nối database
│   │   ├── __init__.py
│   │   └── postgresql.py
│   ├── cache/           # Kết nối Redis, cache
│   │   ├── __init__.py
│   │   └── redis_client.py
│   ├── schemas/         # Định nghĩa Pydantic schema
│   │   ├── __init__.py
│   │   └── chat.py
│   ├── utils/           # Helper, công cụ
│   │   ├── __init__.py
│   │   └── helper.py
│   └── middleware/      # Middleware
│       ├── __init__.py
│       ├── logging_middleware.py
│       └── rate_limiter.py
```

## Cải tiến đã thực hiện

1. **Logging toàn diện**
   - Sử dụng Python logging module
   - Log chi tiết cho mọi hoạt động
   - Định dạng log chuẩn với timestamp và level

2. **Error handling nâng cao**
   - Xử lý ngoại lệ toàn cục
   - Phân loại và ghi log lỗi
   - Phản hồi lỗi thân thiện với người dùng

3. **Caching thông minh**
   - Cache nhiều cấp độ (multi-level caching)
   - Cache key thông minh với hashing
   - TTL (Time-To-Live) tùy chỉnh cho từng loại dữ liệu

4. **Database Connection Pooling**
   - Quản lý pool kết nối hiệu quả
   - Tự động đóng kết nối không sử dụng
   - Xử lý lỗi kết nối

5. **Rate Limiting**
   - Giới hạn số lượng request theo IP hoặc user ID
   - Cấu hình linh hoạt thông qua biến môi trường
   - Header chuẩn để thông báo giới hạn

6. **Tích hợp LLM nâng cao**
   - Client service riêng cho LLM Core
   - Hỗ trợ định dạng chat mới
   - Xử lý context thông minh
   - Timeout và retry cho các request LLM
   - Circuit breaker pattern với health check
   - Caching thông minh cho các truy vấn LLM

7. **Cấu hình linh hoạt**
   - Tất cả cấu hình thông qua biến môi trường
   - Giá trị mặc định hợp lý
   - Phân chia cấu hình theo module

## Biến môi trường

| Biến | Mô tả | Giá trị mặc định |
|------|-------|-----------------|
| `DEBUG` | Chế độ debug | `False` |
| `ENVIRONMENT` | Môi trường (development, staging, production) | `development` |
| `TZ` | Múi giờ | `Asia/Ho_Chi_Minh` |
| `CORS_ORIGINS` | Danh sách nguồn được phép CORS (phân cách bằng dấu phẩy) | `*` |
| `CACHE_TTL` | Thời gian cache mặc định (giây) | `3600` |
| `CACHE_PREFIX` | Tiền tố cho cache key | `xosotv:` |
| `RATE_LIMIT_ENABLED` | Bật/tắt rate limiting | `True` |
| `RATE_LIMIT_REQUESTS` | Số lượng request tối đa trong cửa sổ thời gian | `100` |
| `RATE_LIMIT_WINDOW` | Cửa sổ thời gian cho rate limiting (giây) | `3600` |
| `POSTGRES_HOST` | Host PostgreSQL | `postgres` |
| `POSTGRES_PORT` | Port PostgreSQL | `5432` |
| `POSTGRES_USER` | User PostgreSQL | `xosotv_user` |
| `POSTGRES_PASSWORD` | Password PostgreSQL | `` |
| `POSTGRES_DB` | Database PostgreSQL | `xosotv` |
| `REDIS_HOST` | Host Redis | `redis` |
| `REDIS_PORT` | Port Redis | `6379` |
| `REDIS_DB` | Database Redis | `0` |
| `LLM_CORE_HOST` | Host LLM Core | `llm-core` |
| `LLM_CORE_PORT` | Port LLM Core | `8080` |
| `LLM_TIMEOUT` | Timeout cho request LLM (giây) | `30.0` |
| `LLM_MAX_RETRIES` | Số lần thử lại tối đa | `3` |
| `LLM_RETRY_DELAY` | Thời gian chờ giữa các lần thử (giây) | `1.0` |
| `LLM_HEALTH_CHECK_INTERVAL` | Khoảng thời gian giữa các lần health check (giây) | `60` |
| `LLM_DEFAULT_MAX_TOKENS` | Số token mặc định | `512` |
| `LLM_DEFAULT_TEMPERATURE` | Nhiệt độ mặc định | `0.7` |
| `LLM_DEFAULT_TOP_P` | Top-p mặc định | `0.95` |
| `SECRET_KEY` | Khóa bí mật cho JWT | `your-secret-key-change-in-production` |
| `JWT_ALGORITHM` | Thuật toán JWT | `HS256` |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | Thời gian hết hạn access token (phút) | `30` |
| `REFRESH_TOKEN_EXPIRE_DAYS` | Thời gian hết hạn refresh token (ngày) | `7` |
| `LOG_LEVEL` | Cấp độ log | `INFO` |
| `LOG_FILE` | File log (để trống để log ra stdout) | `` |

## API Endpoints

- `GET /health` - Kiểm tra trạng thái dịch vụ
- `GET /` - Trang chủ, chuyển hướng đến tài liệu API
- `POST /api/v1/chat` - Xử lý tin nhắn chat
- `GET /api/v1/history/{user_id}` - Lấy lịch sử chat
- `DELETE /api/v1/history/{user_id}` - Xóa lịch sử chat
- `GET /api/docs` - Tài liệu API (Swagger UI)
- `GET /api/redoc` - Tài liệu API (ReDoc)

## Hướng dẫn phát triển

1. Cài đặt các gói phụ thuộc:
   ```bash
   pip install -r requirements.txt
   ```

2. Chạy ứng dụng trong môi trường phát triển:
   ```bash
   uvicorn app.main:app --reload
   ```

3. Chạy với Docker:
   ```bash
   docker build -t chatbot-service .
   docker run -p 8000:8000 chatbot-service
   ```

4. Chạy với Docker Compose:
   ```bash
   docker-compose up -d chatbot-service
   ```
