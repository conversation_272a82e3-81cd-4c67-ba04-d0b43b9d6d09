#!/usr/bin/env python3
"""
Test minimal schema
"""

print("=== TESTING MINIMAL SCHEMA ===")

try:
    print("Testing minimal schema imports...")
    from app.schemas.lottery import LotteryTypeSchema, LotteryResultResponseSchema
    print("✅ Minimal schema imports OK")
    
    lottery_type = LotteryTypeSchema(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Test"
    )
    print(f"✅ Schema works: {lottery_type.name}")
    
except Exception as e:
    print(f"❌ Failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("🎉 Minimal schema works!")
