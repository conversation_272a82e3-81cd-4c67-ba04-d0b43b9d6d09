#!/usr/bin/env python3
"""
Simple test để tìm RecursionError
"""

import sys
import os

# Thêm đường dẫn
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'chatbot-service'))

print("=== SIMPLE IMPORT TEST ===")

# Test 1: Import schema trực tiếp
try:
    print("1. Testing direct schema import...")
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'chatbot-service'))
    
    # Import trực tiếp từ file schema
    import importlib.util
    spec = importlib.util.spec_from_file_location(
        "lottery_schema", 
        os.path.join(os.path.dirname(__file__), 'chatbot-service', 'app', 'schemas', 'lottery.py')
    )
    lottery_schema = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(lottery_schema)
    
    print("✅ Direct schema import OK")
    print(f"Found: {dir(lottery_schema)}")
    
except Exception as e:
    print(f"❌ Direct schema import failed: {e}")
    import traceback
    traceback.print_exc()

print("\n=== TEST COMPLETE ===")
