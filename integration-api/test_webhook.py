#!/usr/bin/env python3
import requests
import json
import hmac
import hashlib
import argparse
from datetime import date, datetime

def generate_signature(payload, secret_key):
    """Tạo chữ ký HMAC-SHA256 cho payload"""
    return hmac.new(
        secret_key.encode(),
        payload.encode(),
        hashlib.sha256
    ).hexdigest()

def send_webhook(url, lottery_type, draw_date, prizes, secret_key=None):
    """Gửi webhook đến URL đích"""
    # Tạo payload
    payload = {
        "lottery_type": lottery_type,
        "draw_date": draw_date,
        "prizes": prizes,
        "source": "test-script",
        "timestamp": datetime.now().isoformat()
    }

    # Chuyển đổi payload thành JSON
    payload_json = json.dumps(payload)

    # Tạo headers
    headers = {
        "Content-Type": "application/json"
    }

    # Thêm chữ ký nếu có secret key
    if secret_key:
        signature = generate_signature(payload_json, secret_key)
        headers["X-Webhook-Signature"] = signature
        print(f"Chữ ký: {signature}")

    # <PERSON><PERSON><PERSON> request
    print(f"Gửi webhook đến {url}")
    print(f"Payload: {payload_json}")

    response = requests.post(url, data=payload_json, headers=headers)

    # In kết quả
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")

    return response

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description="Gửi webhook test đến integration-api")
    parser.add_argument("--url", default="http://localhost:9000/api/webhook/lottery", help="URL webhook")
    parser.add_argument("--type", default="mb", choices=["mb", "mt", "mn"], help="Loại xổ số")
    parser.add_argument("--date", default=date.today().isoformat(), help="Ngày quay thưởng (YYYY-MM-DD)")
    parser.add_argument("--prize", default="special", help="Tên giải")
    parser.add_argument("--numbers", default="12345", help="Số trúng thưởng (phân cách bằng dấu phẩy nếu nhiều số)")
    parser.add_argument("--secret", help="Secret key để tạo chữ ký")

    args = parser.parse_args()

    # Xử lý numbers
    if "," in args.numbers:
        numbers = args.numbers.split(",")
    else:
        numbers = args.numbers

    # Tạo prizes
    prizes = [
        {
            "prize_name": args.prize,
            "numbers": numbers
        }
    ]

    # Gửi webhook
    send_webhook(args.url, args.type, args.date, prizes, args.secret)

if __name__ == "__main__":
    main()
