import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from app.config.settings import settings

class WebhookLogger:
    """
    Logger chuyên dụng cho webhook
    """
    
    def __init__(self):
        """Khởi tạo logger"""
        self.logger = logging.getLogger("webhook")
        self.logger.setLevel(logging.INFO)
        
        # Ki<PERSON>m tra xem đã có handler chưa
        if not self.logger.handlers:
            # Thêm file handler
            if settings.logging.webhook_log_file:
                log_path = Path(settings.logging.webhook_log_file)
                log_path.parent.mkdir(parents=True, exist_ok=True)
                
                file_handler = logging.FileHandler(settings.logging.webhook_log_file)
                file_handler.setLevel(logging.INFO)
                
                formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
                file_handler.setFormatter(formatter)
                
                self.logger.addHandler(file_handler)
            
            # Thêm console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(formatter)
            
            self.logger.addHandler(console_handler)
    
    def log_request(
        self, 
        request_data: Dict[str, Any], 
        ip_address: Optional[str] = None,
        signature: Optional[str] = None
    ):
        """
        Ghi log request webhook
        
        Args:
            request_data: Dữ liệu request
            ip_address: Địa chỉ IP của người gửi
            signature: Chữ ký webhook
        """
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "type": "request",
            "ip_address": ip_address,
            "signature": signature,
            "data": request_data
        }
        
        self.logger.info(json.dumps(log_data))
    
    def log_response(
        self, 
        response_data: Dict[str, Any], 
        status_code: int,
        request_data: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None
    ):
        """
        Ghi log response webhook
        
        Args:
            response_data: Dữ liệu response
            status_code: Mã trạng thái HTTP
            request_data: Dữ liệu request
            ip_address: Địa chỉ IP của người gửi
        """
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "type": "response",
            "status_code": status_code,
            "ip_address": ip_address,
            "request_data": request_data,
            "response_data": response_data
        }
        
        self.logger.info(json.dumps(log_data))
    
    def log_error(
        self, 
        error_message: str, 
        request_data: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None
    ):
        """
        Ghi log lỗi webhook
        
        Args:
            error_message: Thông báo lỗi
            request_data: Dữ liệu request
            ip_address: Địa chỉ IP của người gửi
        """
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "type": "error",
            "error_message": error_message,
            "ip_address": ip_address,
            "request_data": request_data
        }
        
        self.logger.error(json.dumps(log_data))

# Tạo instance logger
webhook_logger = WebhookLogger()
