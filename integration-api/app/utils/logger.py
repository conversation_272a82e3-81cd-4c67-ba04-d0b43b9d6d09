import os
import sys
import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Union

from app.config.settings import settings

# Định dạng log
LOG_FORMAT = "%(asctime)s | %(levelname)s | %(name)s | %(message)s"
JSON_LOG_FORMAT = {
    "timestamp": "%(asctime)s",
    "level": "%(levelname)s",
    "logger": "%(name)s",
    "message": "%(message)s",
    "module": "%(module)s",
    "function": "%(funcName)s",
    "line": "%(lineno)d",
    "process": "%(process)d",
    "thread": "%(thread)d"
}

# Tạo thư mục logs nếu chưa tồn tại
logs_dir = Path("logs")
logs_dir.mkdir(exist_ok=True)

class JsonFormatter(logging.Formatter):
    """
    Formatter để định dạng log dưới dạng JSON
    """
    def __init__(self, fmt_dict: Dict[str, str], time_format: str = "%Y-%m-%d %H:%M:%S"):
        self.fmt_dict = fmt_dict
        self.time_format = time_format
        self.default_msec_format = "%s.%03d"

    def format(self, record: logging.LogRecord) -> str:
        record_dict = {}
        
        # Thêm các trường từ fmt_dict
        for key, fmt in self.fmt_dict.items():
            try:
                record_dict[key] = fmt % record.__dict__
            except (KeyError, TypeError):
                record_dict[key] = fmt
        
        # Thêm các trường bổ sung
        record_dict["service"] = "integration-api"
        record_dict["environment"] = settings.environment
        record_dict["version"] = settings.version
        
        # Thêm exception info nếu có
        if record.exc_info:
            record_dict["exception"] = self.formatException(record.exc_info)
        
        # Thêm các trường từ extra
        for key, value in record.__dict__.items():
            if key not in ["args", "asctime", "created", "exc_info", "exc_text", "filename",
                          "funcName", "id", "levelname", "levelno", "lineno", "module",
                          "msecs", "message", "msg", "name", "pathname", "process",
                          "processName", "relativeCreated", "stack_info", "thread", "threadName"]:
                record_dict[key] = value
        
        # Chuyển đổi sang JSON
        return json.dumps(record_dict, ensure_ascii=False)

class CustomLogger:
    """
    Logger tùy chỉnh cho integration-api
    """
    def __init__(self, name: str, log_level: str = None):
        self.name = name
        self.logger = logging.getLogger(name)
        
        # Thiết lập log level
        log_level = log_level or settings.logging.level
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Xóa tất cả handlers hiện có
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        # Thêm handler cho stdout
        stdout_handler = logging.StreamHandler(sys.stdout)
        stdout_handler.setFormatter(logging.Formatter(LOG_FORMAT))
        self.logger.addHandler(stdout_handler)
        
        # Thêm handler cho file log
        if settings.logging.file_path:
            file_path = Path(settings.logging.file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(settings.logging.file_path)
            file_handler.setFormatter(logging.Formatter(LOG_FORMAT))
            self.logger.addHandler(file_handler)
        
        # Thêm handler cho JSON log
        json_log_path = logs_dir / f"{name}.json.log"
        json_handler = logging.FileHandler(json_log_path)
        json_handler.setFormatter(JsonFormatter(JSON_LOG_FORMAT))
        self.logger.addHandler(json_handler)
    
    def debug(self, msg: str, *args, **kwargs):
        self.logger.debug(msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        self.logger.info(msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        self.logger.warning(msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        self.logger.error(msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        self.logger.critical(msg, *args, **kwargs)
    
    def exception(self, msg: str, *args, **kwargs):
        self.logger.exception(msg, *args, **kwargs)
    
    def log_request(self, request, extra: Dict[str, Any] = None):
        """
        Ghi log request
        """
        extra = extra or {}
        
        # Lấy thông tin request
        request_info = {
            "method": request.method,
            "url": str(request.url),
            "client": request.client.host if request.client else None,
            "headers": dict(request.headers),
            **extra
        }
        
        self.info(f"Request: {request.method} {request.url}", extra={"request": request_info})
    
    def log_response(self, request, response, elapsed_time: float, extra: Dict[str, Any] = None):
        """
        Ghi log response
        """
        extra = extra or {}
        
        # Lấy thông tin response
        response_info = {
            "status_code": response.status_code,
            "elapsed_time_ms": round(elapsed_time * 1000, 2),
            "headers": dict(response.headers),
            **extra
        }
        
        self.info(
            f"Response: {request.method} {request.url} - Status: {response.status_code} - Time: {elapsed_time:.3f}s",
            extra={"response": response_info}
        )
    
    def log_error(self, error: Exception, request=None, extra: Dict[str, Any] = None):
        """
        Ghi log lỗi
        """
        extra = extra or {}
        
        # Lấy thông tin lỗi
        error_info = {
            "type": type(error).__name__,
            "message": str(error),
            **extra
        }
        
        if request:
            error_info["request"] = {
                "method": request.method,
                "url": str(request.url),
                "client": request.client.host if request.client else None
            }
        
        self.error(f"Error: {type(error).__name__} - {str(error)}", exc_info=True, extra={"error": error_info})

# Tạo logger mặc định
default_logger = CustomLogger("integration-api")

# Hàm để lấy logger
def get_logger(name: str = None, log_level: str = None) -> CustomLogger:
    """
    Lấy logger với tên cụ thể
    
    Args:
        name: Tên logger
        log_level: Cấp độ log
        
    Returns:
        CustomLogger: Logger tùy chỉnh
    """
    if name is None:
        return default_logger
    
    return CustomLogger(name, log_level)
