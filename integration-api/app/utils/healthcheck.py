import logging
import time
from typing import Dict, Any, List, <PERSON>ple
import httpx
import asyncpg
import redis.asyncio as redis

from app.config.settings import settings
from app.db.session import engine

logger = logging.getLogger("healthcheck")

async def check_database() -> Tuple[bool, str]:
    """
    Kiểm tra kết nối đến PostgreSQL
    
    Returns:
        Tuple[bool, str]: (trạng thái, thông báo)
    """
    try:
        start_time = time.time()
        
        # Thực hiện truy vấn đơn giản
        async with engine.connect() as conn:
            result = await conn.execute("SELECT 1")
            row = await result.fetchone()
            
            if row and row[0] == 1:
                elapsed = time.time() - start_time
                return True, f"Kết nối PostgreSQL OK ({elapsed:.3f}s)"
            else:
                return False, "Kết nối PostgreSQL thất bại: <PERSON><PERSON><PERSON> vấn không trả về kết quả mong đợi"
    except asyncpg.exceptions.PostgresError as e:
        return False, f"Lỗi PostgreSQL: {str(e)}"
    except Exception as e:
        return False, f"Lỗi không xác định khi kết nối PostgreSQL: {str(e)}"

async def check_redis() -> Tuple[bool, str]:
    """
    Kiểm tra kết nối đến Redis
    
    Returns:
        Tuple[bool, str]: (trạng thái, thông báo)
    """
    try:
        start_time = time.time()
        
        # Tạo kết nối Redis
        redis_client = redis.Redis(
            host=settings.redis.host,
            port=settings.redis.port,
            db=settings.redis.db,
            password=settings.redis.password,
            decode_responses=True,
            socket_timeout=settings.redis.socket_timeout,
            socket_connect_timeout=settings.redis.socket_connect_timeout,
            retry_on_timeout=settings.redis.retry_on_timeout
        )
        
        # Thực hiện lệnh đơn giản
        result = await redis_client.ping()
        
        # Đóng kết nối
        await redis_client.close()
        
        if result:
            elapsed = time.time() - start_time
            return True, f"Kết nối Redis OK ({elapsed:.3f}s)"
        else:
            return False, "Kết nối Redis thất bại: Lệnh PING không trả về kết quả mong đợi"
    except redis.RedisError as e:
        return False, f"Lỗi Redis: {str(e)}"
    except Exception as e:
        return False, f"Lỗi không xác định khi kết nối Redis: {str(e)}"

async def check_internal_endpoints() -> List[Dict[str, Any]]:
    """
    Kiểm tra các endpoint nội bộ quan trọng
    
    Returns:
        List[Dict[str, Any]]: Danh sách kết quả kiểm tra
    """
    results = []
    
    # Danh sách các endpoint cần kiểm tra
    endpoints = [
        {"name": "API Root", "path": "/", "method": "GET"},
        {"name": "Webhook API", "path": "/api/webhook/logs", "method": "GET"},
    ]
    
    # Xác định port
    port = settings.app_port if hasattr(settings, 'app_port') else 9000
    
    async with httpx.AsyncClient(timeout=5.0) as client:
        for endpoint in endpoints:
            try:
                start_time = time.time()
                
                if endpoint["method"] == "GET":
                    response = await client.get(f"http://localhost:{port}{endpoint['path']}")
                elif endpoint["method"] == "POST":
                    response = await client.post(
                        f"http://localhost:{port}{endpoint['path']}",
                        json=endpoint.get("data", {})
                    )
                else:
                    continue
                
                elapsed = time.time() - start_time
                
                results.append({
                    "name": endpoint["name"],
                    "status": response.status_code < 400,
                    "status_code": response.status_code,
                    "response_time": f"{elapsed:.3f}s",
                    "message": f"HTTP {response.status_code}"
                })
            except Exception as e:
                results.append({
                    "name": endpoint["name"],
                    "status": False,
                    "status_code": None,
                    "response_time": None,
                    "message": f"Lỗi: {str(e)}"
                })
    
    return results

async def get_system_info() -> Dict[str, Any]:
    """
    Lấy thông tin hệ thống
    
    Returns:
        Dict[str, Any]: Thông tin hệ thống
    """
    import platform
    import psutil
    
    try:
        # Thông tin CPU
        cpu_percent = psutil.cpu_percent(interval=0.1)
        cpu_count = psutil.cpu_count()
        
        # Thông tin bộ nhớ
        memory = psutil.virtual_memory()
        memory_total = memory.total / (1024 * 1024 * 1024)  # GB
        memory_used = memory.used / (1024 * 1024 * 1024)    # GB
        memory_percent = memory.percent
        
        # Thông tin đĩa
        disk = psutil.disk_usage('/')
        disk_total = disk.total / (1024 * 1024 * 1024)      # GB
        disk_used = disk.used / (1024 * 1024 * 1024)        # GB
        disk_percent = disk.percent
        
        return {
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "cpu": {
                "percent": cpu_percent,
                "count": cpu_count
            },
            "memory": {
                "total": f"{memory_total:.2f} GB",
                "used": f"{memory_used:.2f} GB",
                "percent": memory_percent
            },
            "disk": {
                "total": f"{disk_total:.2f} GB",
                "used": f"{disk_used:.2f} GB",
                "percent": disk_percent
            }
        }
    except Exception as e:
        logger.error(f"Lỗi khi lấy thông tin hệ thống: {str(e)}")
        return {
            "error": f"Không thể lấy thông tin hệ thống: {str(e)}"
        }

async def get_health_status() -> Dict[str, Any]:
    """
    Lấy trạng thái sức khỏe tổng thể của hệ thống
    
    Returns:
        Dict[str, Any]: Trạng thái sức khỏe
    """
    # Kiểm tra các thành phần
    db_status, db_message = await check_database()
    redis_status, redis_message = await check_redis()
    
    # Kiểm tra các endpoint
    endpoint_results = await check_internal_endpoints()
    
    # Lấy thông tin hệ thống
    system_info = await get_system_info()
    
    # Tính toán trạng thái tổng thể
    overall_status = all([
        db_status,
        redis_status,
        all(result["status"] for result in endpoint_results)
    ])
    
    # Tạo kết quả
    return {
        "service": "integration-api",
        "status": "healthy" if overall_status else "unhealthy",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "version": getattr(settings, "version", "1.0.0"),
        "environment": settings.environment,
        "components": {
            "database": {
                "status": "up" if db_status else "down",
                "message": db_message
            },
            "redis": {
                "status": "up" if redis_status else "down",
                "message": redis_message
            }
        },
        "endpoints": endpoint_results,
        "system_info": system_info
    }
