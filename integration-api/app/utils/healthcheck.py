import logging
import time
from typing import Dict, Any, List, <PERSON>ple
import httpx
import asyncpg
import redis.asyncio as redis

from app.config.settings import settings
from app.db.session import engine

logger = logging.getLogger("healthcheck")

async def check_database() -> Tuple[bool, str]:
    """
    Kiểm tra kết nối đến PostgreSQL
    
    Returns:
        Tuple[bool, str]: (trạng thái, thông báo)
    """
    try:
        start_time = time.time()
        
        # Thực hiện truy vấn đơn giản
        async with engine.connect() as conn:
            result = await conn.execute("SELECT 1")
            row = await result.fetchone()
            
            if row and row[0] == 1:
                elapsed = time.time() - start_time
                return True, f"Kết nối PostgreSQL OK ({elapsed:.3f}s)"
            else:
                return False, "Kết nối PostgreSQL thất bại: <PERSON><PERSON><PERSON> vấn không trả về kết quả mong đợi"
    except asyncpg.exceptions.PostgresError as e:
        return False, f"Lỗi PostgreSQL: {str(e)}"
    except Exception as e:
        return False, f"Lỗi không xác định khi kết nối PostgreSQL: {str(e)}"

async def check_redis() -> Tuple[bool, str]:
    """
    Kiểm tra kết nối đến Redis
    
    Returns:
        Tuple[bool, str]: (trạng thái, thông báo)
    """
    try:
        start_time = time.time()
        
        # Tạo kết nối Redis
        redis_client = redis.Redis(
            host=settings.redis.host,
            port=settings.redis.port,
            db=settings.redis.db,
            password=settings.redis.password,
            decode_responses=True,
            socket_timeout=settings.redis.socket_timeout,
            socket_connect_timeout=settings.redis.socket_connect_timeout,
            retry_on_timeout=settings.redis.retry_on_timeout
        )
        
        # Thực hiện lệnh đơn giản
        result = await redis_client.ping()
        
        # Đóng kết nối
        await redis_client.close()
        
        if result:
            elapsed = time.time() - start_time
            return True, f"Kết nối Redis OK ({elapsed:.3f}s)"
        else:
            return False, "Kết nối Redis thất bại: Lệnh PING không trả về kết quả mong đợi"
    except redis.RedisError as e:
        return False, f"Lỗi Redis: {str(e)}"
    except Exception as e:
        return False, f"Lỗi không xác định khi kết nối Redis: {str(e)}"

async def check_internal_endpoints() -> List[Dict[str, Any]]:
    """
    Kiểm tra các endpoint nội bộ quan trọng
    
    Returns:
        List[Dict[str, Any]]: Danh sách kết quả kiểm tra
    """
    results = []
    
    # Danh sách các endpoint cần kiểm tra
    endpoints = [
        {"name": "API Root", "path": "/", "method": "GET"},
        {"name": "Webhook API", "path": "/api/webhook/logs", "method": "GET"},
    ]
    
    # Xác định port
    port = settings.app_port if hasattr(settings, 'app_port') else 9000
    
    async with httpx.AsyncClient(timeout=5.0) as client:
        for endpoint in endpoints:
            try:
                start_time = time.time()
                
                if endpoint["method"] == "GET":
                    response = await client.get(f"http://localhost:{port}{endpoint['path']}")
                elif endpoint["method"] == "POST":
                    response = await client.post(
                        f"http://localhost:{port}{endpoint['path']}",
                        json=endpoint.get("data", {})
                    )
                else:
                    continue
                
                elapsed = time.time() - start_time
                
                results.append({
                    "name": endpoint["name"],
                    "status": response.status_code < 400,
                    "status_code": response.status_code,
                    "response_time": f"{elapsed:.3f}s",
                    "message": f"HTTP {response.status_code}"
                })
            except Exception as e:
                results.append({
                    "name": endpoint["name"],
                    "status": False,
                    "status_code": None,
                    "response_time": None,
                    "message": f"Lỗi: {str(e)}"
                })
    
    return results

async def get_system_info() -> Dict[str, Any]:
    """
    Lấy thông tin hệ thống

    Returns:
        Dict[str, Any]: Thông tin hệ thống
    """
    import platform
    import os
    import shutil

    try:
        # Thông tin cơ bản từ platform
        system_info = {
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "python_version": platform.python_version(),
            "architecture": platform.architecture()[0]
        }

        # Thông tin CPU chi tiết
        try:
            cpu_info = {
                "processor": platform.processor(),
                "machine": platform.machine(),
            }

            # Lấy số lượng CPU cores
            cpu_info["cores"] = os.cpu_count() or "unknown"

            # Thông tin CPU từ /proc/cpuinfo (Linux)
            if os.path.exists("/proc/cpuinfo"):
                with open("/proc/cpuinfo", "r") as f:
                    cpuinfo = f.read()
                    lines = cpuinfo.split("\n")

                    for line in lines:
                        if line.startswith("model name"):
                            cpu_info["model"] = line.split(":")[1].strip()
                            break
                        elif line.startswith("cpu model"):  # Fallback cho một số hệ thống
                            cpu_info["model"] = line.split(":")[1].strip()
                            break

                    # Đếm số physical cores và logical cores
                    physical_cores = set()
                    logical_cores = 0

                    for line in lines:
                        if line.startswith("physical id"):
                            physical_cores.add(line.split(":")[1].strip())
                        elif line.startswith("processor"):
                            logical_cores += 1

                    if physical_cores:
                        cpu_info["physical_cores"] = len(physical_cores)
                    if logical_cores > 0:
                        cpu_info["logical_cores"] = logical_cores

            # Thông tin CPU usage từ /proc/stat (Linux)
            if os.path.exists("/proc/stat"):
                with open("/proc/stat", "r") as f:
                    stat_line = f.readline()
                    if stat_line.startswith("cpu "):
                        # Parse CPU times: user, nice, system, idle, iowait, irq, softirq
                        cpu_times = [int(x) for x in stat_line.split()[1:8]]
                        total_time = sum(cpu_times)
                        idle_time = cpu_times[3]  # idle time

                        if total_time > 0:
                            cpu_usage = ((total_time - idle_time) / total_time) * 100
                            cpu_info["usage_percent"] = f"{cpu_usage:.1f}%"

            # Thông tin load average (Linux)
            if os.path.exists("/proc/loadavg"):
                with open("/proc/loadavg", "r") as f:
                    loadavg = f.read().strip().split()
                    cpu_info["load_average"] = {
                        "1min": float(loadavg[0]),
                        "5min": float(loadavg[1]),
                        "15min": float(loadavg[2])
                    }

            system_info["cpu"] = cpu_info

        except Exception as e:
            logger.debug(f"Không thể lấy thông tin CPU chi tiết: {str(e)}")
            # Fallback với thông tin cơ bản
            system_info["cpu"] = {
                "processor": platform.processor(),
                "machine": platform.machine(),
                "cores": os.cpu_count() or "unknown"
            }

        # Thử lấy thông tin bộ nhớ từ /proc/meminfo (Linux)
        try:
            if os.path.exists("/proc/meminfo"):
                with open("/proc/meminfo", "r") as f:
                    meminfo = f.read()
                    lines = meminfo.split("\n")
                    mem_total = None
                    mem_available = None

                    for line in lines:
                        if line.startswith("MemTotal:"):
                            mem_total = int(line.split()[1]) * 1024  # Convert KB to bytes
                        elif line.startswith("MemAvailable:"):
                            mem_available = int(line.split()[1]) * 1024  # Convert KB to bytes

                    if mem_total and mem_available:
                        mem_used = mem_total - mem_available
                        system_info["memory"] = {
                            "total": f"{mem_total / (1024**3):.2f} GB",
                            "used": f"{mem_used / (1024**3):.2f} GB",
                            "available": f"{mem_available / (1024**3):.2f} GB",
                            "percent": f"{(mem_used / mem_total) * 100:.1f}%"
                        }
        except Exception:
            pass

        # Thông tin đĩa
        try:
            disk_usage = shutil.disk_usage("/")
            disk_total = disk_usage.total / (1024**3)  # GB
            disk_used = (disk_usage.total - disk_usage.free) / (1024**3)  # GB
            disk_free = disk_usage.free / (1024**3)  # GB

            system_info["disk"] = {
                "total": f"{disk_total:.2f} GB",
                "used": f"{disk_used:.2f} GB",
                "free": f"{disk_free:.2f} GB",
                "percent": f"{(disk_used / disk_total) * 100:.1f}%"
            }
        except Exception:
            pass

        return system_info

    except Exception as e:
        logger.error(f"Lỗi khi lấy thông tin hệ thống: {str(e)}")
        return {
            "system": platform.system(),
            "error": f"Không thể lấy thông tin chi tiết: {str(e)}"
        }

async def get_health_status() -> Dict[str, Any]:
    """
    Lấy trạng thái sức khỏe tổng thể của hệ thống
    
    Returns:
        Dict[str, Any]: Trạng thái sức khỏe
    """
    # Kiểm tra các thành phần
    db_status, db_message = await check_database()
    redis_status, redis_message = await check_redis()
    
    # Kiểm tra các endpoint
    endpoint_results = await check_internal_endpoints()
    
    # Lấy thông tin hệ thống
    system_info = await get_system_info()
    
    # Tính toán trạng thái tổng thể
    overall_status = all([
        db_status,
        redis_status,
        all(result["status"] for result in endpoint_results)
    ])
    
    # Tạo kết quả
    return {
        "service": "integration-api",
        "status": "healthy" if overall_status else "unhealthy",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "version": getattr(settings, "version", "1.0.0"),
        "environment": settings.environment,
        "components": {
            "database": {
                "status": "up" if db_status else "down",
                "message": db_message
            },
            "redis": {
                "status": "up" if redis_status else "down",
                "message": redis_message
            }
        },
        "endpoints": endpoint_results,
        "system_info": system_info
    }
