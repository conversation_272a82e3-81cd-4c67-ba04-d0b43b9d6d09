import time
import logging
import uuid
import json
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from starlette.responses import Response

from app.utils.logger import get_logger
from app.config.settings import settings

logger = get_logger("request")

class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware để ghi log request và response
    """
    
    async def dispatch(self, request: Request, call_next):
        # Tạo request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Thời gian bắt đầu
        start_time = time.time()
        
        # Ghi log thông tin request
        path = request.url.path
        method = request.method
        client_host = request.client.host if request.client else "unknown"
        
        # <PERSON><PERSON><PERSON> bị thông tin request để log
        request_info = {
            "request_id": request_id,
            "method": method,
            "path": path,
            "client": client_host,
            "query_params": dict(request.query_params),
            "headers": dict(request.headers)
        }
        
        # Log body nếu đư<PERSON><PERSON> c<PERSON>u hình
        if settings.logging.log_request_body and method in ["POST", "PUT", "PATCH"]:
            try:
                # <PERSON><PERSON><PERSON> vị trí hiện tại của body
                body_position = await request.body()
                # Đọc body
                body = await request.body()
                # Khôi phục vị trí body để các middleware khác có thể đọc
                request._body = body
                
                try:
                    # Thử parse body dưới dạng JSON
                    request_info["body"] = json.loads(body)
                except json.JSONDecodeError:
                    # Nếu không phải JSON, lưu dưới dạng string
                    request_info["body"] = body.decode("utf-8", errors="replace")
            except Exception as e:
                request_info["body_error"] = str(e)
        
        logger.info(
            f"Request {request_id} started: {method} {path} from {client_host}", 
            extra={"request": request_info}
        )
        
        try:
            # Xử lý request
            response = await call_next(request)
            
            # Tính thời gian xử lý
            process_time = time.time() - start_time
            
            # Ghi log thông tin response
            status_code = response.status_code
            
            # Chuẩn bị thông tin response để log
            response_info = {
                "request_id": request_id,
                "status_code": status_code,
                "process_time": f"{process_time:.3f}s",
                "headers": dict(response.headers)
            }
            
            # Log body response nếu được cấu hình
            if settings.logging.log_response_body and hasattr(response, "body"):
                try:
                    body = response.body
                    
                    try:
                        # Thử parse body dưới dạng JSON
                        response_info["body"] = json.loads(body)
                    except json.JSONDecodeError:
                        # Nếu không phải JSON, lưu dưới dạng string
                        response_info["body"] = body.decode("utf-8", errors="replace")
                except Exception as e:
                    response_info["body_error"] = str(e)
            
            logger.info(
                f"Request {request_id} completed: {method} {path} - {status_code} in {process_time:.3f}s",
                extra={"response": response_info}
            )
            
            # Thêm request ID vào header response
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{process_time:.3f}s"
            
            return response
        except Exception as e:
            # Tính thời gian xử lý
            process_time = time.time() - start_time
            
            # Ghi log lỗi
            error_info = {
                "request_id": request_id,
                "method": method,
                "path": path,
                "process_time": f"{process_time:.3f}s",
                "error_type": type(e).__name__,
                "error_message": str(e)
            }
            
            logger.error(
                f"Request {request_id} failed: {method} {path} - Exception: {str(e)} in {process_time:.3f}s",
                exc_info=True,
                extra={"error": error_info}
            )
            
            # Re-raise exception
            raise
