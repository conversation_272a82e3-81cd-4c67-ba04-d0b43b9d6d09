import time
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from contextlib import asynccontextmanager

from app.config.settings import settings
from app.db.session import init_models, engine
from app.api import webhook
from app.utils.healthcheck import get_health_status
from app.utils.logger import get_logger
from app.middleware.logging_middleware import LoggingMiddleware

# Lấy logger
logger = get_logger("main")

# Lifespan event
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Khởi tạo kết nối khi ứng dụng khởi động
    logger.info("Đang khởi tạo kết nối...")

    # Trong môi trường development, tự động tạo bảng
    if settings.environment == "development":
        try:
            await init_models()
            logger.info("Đã tạo các bảng trong database")
        except Exception as e:
            logger.error(f"Lỗi khi tạo bảng: {str(e)}")

    logger.info("Kết nối đã được khởi tạo thành công!")
    yield

    # Đóng kết nối khi ứng dụng tắt
    logger.info("Đang đóng kết nối...")
    await engine.dispose()
    logger.info("Kết nối đã được đóng!")

# Tạo ứng dụng FastAPI
app = FastAPI(
    title=settings.app_name,
    description="API tích hợp cho hệ thống XỔ SỐ TV",
    version="1.0.0",
    lifespan=lifespan
)

# Thêm middleware logging
app.add_middleware(LoggingMiddleware)

# Cấu hình CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Đăng ký các route
app.include_router(webhook.router, prefix="/api/webhook", tags=["webhook"])

# Route mặc định
@app.get("/", include_in_schema=False)
async def root():
    return RedirectResponse(url="/docs")

# Route health check
@app.get("/health", tags=["health"])
async def health_check():
    """Endpoint kiểm tra trạng thái hoạt động chi tiết"""
    health_data = await get_health_status()
    return health_data

@app.get("/health/simple", tags=["health"])
async def simple_health_check():
    """Endpoint kiểm tra trạng thái hoạt động đơn giản"""
    return {
        "status": "ok",
        "service": "integration-api",
        "version": settings.version,
        "environment": settings.environment
    }
