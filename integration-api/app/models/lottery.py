from sqlalchemy import <PERSON><PERSON>n, Integer, String, Date, JSON, ForeignKey, UniqueConstraint, DateTime, Boolean, Text, func
from sqlalchemy.orm import relationship
from datetime import date, datetime

from app.db.base import Base

class LotteryType(Base):
    """Model cho bảng lottery_types"""
    __tablename__ = "lottery_types"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(20), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=False)
    region = Column(String(50), nullable=True)
    description = Column(String(255), nullable=True)
    
    # Relationship
    draws = relationship("LotteryDraw", back_populates="lottery_type")

    def __repr__(self):
        return f"<LotteryType(id={self.id}, code={self.code}, name={self.name})>"
    
    @classmethod
    async def get_by_code(cls, db, code: str):
        """<PERSON><PERSON>y loại xổ số theo mã"""
        result = await db.execute(
            cls.__table__.select()
            .where(cls.code == code)
        )
        return result.scalars().first()
    
    @classmethod
    async def get_all(cls, db):
        """Lấy tất cả các loại xổ số"""
        result = await db.execute(cls.__table__.select())
        return result.scalars().all()

class LotteryDraw(Base):
    """Model cho bảng lottery_draws"""
    __tablename__ = "lottery_draws"
    __table_args__ = (
        UniqueConstraint('lottery_type_id', 'draw_date', name='uix_lottery_draw'),
    )

    id = Column(Integer, primary_key=True, index=True)
    lottery_type_id = Column(Integer, ForeignKey("lottery_types.id"), nullable=False)
    draw_date = Column(Date, nullable=False, index=True)
    results = Column(JSON, nullable=True)  # Lưu kết quả dạng JSON
    is_complete = Column(Boolean, default=False, nullable=False)  # Đánh dấu kết quả đã đầy đủ chưa
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationship
    lottery_type = relationship("LotteryType", back_populates="draws")
    webhook_logs = relationship("WebhookLog", back_populates="lottery_draw")

    def __repr__(self):
        return f"<LotteryDraw(id={self.id}, type_id={self.lottery_type_id}, date={self.draw_date})>"
    
    @classmethod
    async def get_by_date(cls, db, draw_date: date, lottery_type_id: int = None):
        """Lấy kết quả xổ số theo ngày và loại (tùy chọn)"""
        query = cls.__table__.select().where(cls.draw_date == draw_date)
        
        if lottery_type_id:
            query = query.where(cls.lottery_type_id == lottery_type_id)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    @classmethod
    async def get_by_date_and_type(cls, db, draw_date: date, lottery_type_id: int):
        """Lấy kết quả xổ số theo ngày và loại"""
        query = cls.__table__.select().where(
            cls.draw_date == draw_date,
            cls.lottery_type_id == lottery_type_id
        )
        
        result = await db.execute(query)
        return result.scalars().first()
    
    @classmethod
    async def get_latest(cls, db, lottery_type_id: int = None, limit: int = 1):
        """Lấy kết quả xổ số mới nhất"""
        query = cls.__table__.select().order_by(cls.draw_date.desc()).limit(limit)
        
        if lottery_type_id:
            query = query.where(cls.lottery_type_id == lottery_type_id)
        
        result = await db.execute(query)
        return result.scalars().all()

class WebhookLog(Base):
    """Model cho bảng webhook_logs"""
    __tablename__ = "webhook_logs"

    id = Column(Integer, primary_key=True, index=True)
    lottery_draw_id = Column(Integer, ForeignKey("lottery_draws.id"), nullable=True)
    lottery_type_code = Column(String(20), nullable=True)
    draw_date = Column(Date, nullable=True)
    request_data = Column(JSON, nullable=False)  # Dữ liệu nhận được từ webhook
    response_data = Column(JSON, nullable=True)  # Dữ liệu trả về
    status_code = Column(Integer, nullable=False)  # Mã trạng thái HTTP
    error_message = Column(Text, nullable=True)  # Thông báo lỗi (nếu có)
    ip_address = Column(String(50), nullable=True)  # Địa chỉ IP của người gửi
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationship
    lottery_draw = relationship("LotteryDraw", back_populates="webhook_logs")

    def __repr__(self):
        return f"<WebhookLog(id={self.id}, lottery_draw_id={self.lottery_draw_id}, status_code={self.status_code})>"
    
    @classmethod
    async def create(cls, db, **kwargs):
        """Tạo một bản ghi webhook log mới"""
        log = cls(**kwargs)
        db.add(log)
        await db.commit()
        await db.refresh(log)
        return log
    
    @classmethod
    async def get_by_draw(cls, db, lottery_draw_id: int):
        """Lấy tất cả các log cho một kỳ quay"""
        query = cls.__table__.select().where(cls.lottery_draw_id == lottery_draw_id)
        result = await db.execute(query)
        return result.scalars().all()
