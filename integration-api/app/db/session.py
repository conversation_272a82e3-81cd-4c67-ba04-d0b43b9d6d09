import logging
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import NullPool

from app.config.settings import settings
from app.db.base import Base

logger = logging.getLogger("sqlalchemy")

# Tạo URL kết nối async cho SQLAlchemy
SQLALCHEMY_DATABASE_URL = settings.db.async_connection_string

# Tạo engine
engine = create_async_engine(
    SQLALCHEMY_DATABASE_URL,
    echo=settings.debug,
    future=True,
    pool_size=settings.db.pool_size,
    max_overflow=settings.db.max_overflow,
    pool_timeout=settings.db.pool_timeout,
    pool_recycle=settings.db.pool_recycle,
    pool_pre_ping=True,
)

# Tạo session factory
SessionLocal = async_sessionmaker(
    bind=engine,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False,
    class_=AsyncSession,
)

async def init_models():
    """Khởi tạo tất cả các model (chỉ dùng cho development)"""
    async with engine.begin() as conn:
        logger.info("Tạo tất cả các bảng trong database")
        await conn.run_sync(Base.metadata.create_all)

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency để lấy database session"""
    async with SessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

async def get_db_session():
    """Lấy một session mới"""
    return SessionLocal()
