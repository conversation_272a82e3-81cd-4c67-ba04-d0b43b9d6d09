import logging
import json
import hmac
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from datetime import date, datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import settings
from app.models.lottery import LotteryType, LotteryDraw, WebhookLog
from app.schemas.webhook import WebhookRequest, WebhookResponse

logger = logging.getLogger("webhook-service")

class WebhookService:
    """
    Dịch vụ xử lý webhook nhận kết quả xổ số
    """
    
    def __init__(self, db: AsyncSession):
        """Khởi tạo với SQLAlchemy session"""
        self.db = db
    
    def verify_signature(self, payload: str, signature: str) -> bool:
        """
        Xác thực chữ ký webhook
        
        Args:
            payload: Dữ liệu webhook dạng chuỗi
            signature: Chữ ký từ header
            
        Returns:
            bool: True nếu chữ ký hợp lệ, <PERSON>alse nếu không
        """
        if not settings.webhook.verify_signature:
            return True
        
        if not signature:
            logger.warning("Không có chữ ký trong request")
            return False
        
        # Tính toán HMAC với SHA256
        computed_signature = hmac.new(
            settings.webhook.secret_key.encode(),
            payload.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # So sánh chữ ký
        return hmac.compare_digest(computed_signature, signature)
    
    def is_ip_allowed(self, ip_address: str) -> bool:
        """
        Kiểm tra xem IP có được phép không
        
        Args:
            ip_address: Địa chỉ IP
            
        Returns:
            bool: True nếu IP được phép, False nếu không
        """
        if not settings.webhook.allowed_ips:
            return True
        
        return ip_address in settings.webhook.allowed_ips
    
    async def process_webhook(
        self, 
        webhook_data: WebhookRequest, 
        ip_address: Optional[str] = None
    ) -> Tuple[WebhookResponse, int]:
        """
        Xử lý dữ liệu webhook
        
        Args:
            webhook_data: Dữ liệu webhook
            ip_address: Địa chỉ IP của người gửi
            
        Returns:
            Tuple[WebhookResponse, int]: Response và status code
        """
        try:
            # Kiểm tra IP
            if not self.is_ip_allowed(ip_address):
                logger.warning(f"IP không được phép: {ip_address}")
                return WebhookResponse(
                    success=False,
                    message="IP không được phép",
                    errors=["IP không nằm trong danh sách cho phép"]
                ), 403
            
            # Lấy loại xổ số
            lottery_type = await LotteryType.get_by_code(self.db, webhook_data.lottery_type)
            if not lottery_type:
                logger.warning(f"Không tìm thấy loại xổ số: {webhook_data.lottery_type}")
                
                # Tạo log
                await WebhookLog.create(
                    self.db,
                    lottery_type_code=webhook_data.lottery_type,
                    draw_date=webhook_data.draw_date,
                    request_data=webhook_data.dict(),
                    response_data={"success": False, "message": "Loại xổ số không tồn tại"},
                    status_code=404,
                    error_message="Loại xổ số không tồn tại",
                    ip_address=ip_address
                )
                
                return WebhookResponse(
                    success=False,
                    message="Loại xổ số không tồn tại",
                    errors=[f"Không tìm thấy loại xổ số với mã {webhook_data.lottery_type}"]
                ), 404
            
            # Kiểm tra xem đã có kết quả cho ngày này chưa
            lottery_draw = await LotteryDraw.get_by_date_and_type(
                self.db, 
                webhook_data.draw_date, 
                lottery_type.id
            )
            
            # Chuyển đổi prizes thành định dạng JSON
            prizes_dict = {}
            for prize in webhook_data.prizes:
                prizes_dict[prize.prize_name] = prize.numbers
            
            if lottery_draw:
                # Đã có kết quả, cập nhật
                logger.info(f"Cập nhật kết quả xổ số {lottery_type.code} ngày {webhook_data.draw_date}")
                
                # Gộp kết quả cũ với kết quả mới
                current_results = lottery_draw.results or {}
                merged_results = {**current_results, **prizes_dict}
                
                # Kiểm tra xem kết quả đã đầy đủ chưa
                is_complete = self._check_complete_results(merged_results, lottery_type.code)
                
                # Cập nhật kết quả
                lottery_draw.results = merged_results
                lottery_draw.is_complete = is_complete
                lottery_draw.updated_at = datetime.utcnow()
                
                self.db.add(lottery_draw)
                await self.db.commit()
                await self.db.refresh(lottery_draw)
                
                # Tạo log
                await WebhookLog.create(
                    self.db,
                    lottery_draw_id=lottery_draw.id,
                    lottery_type_code=lottery_type.code,
                    draw_date=webhook_data.draw_date,
                    request_data=webhook_data.dict(),
                    response_data={"success": True, "message": "Cập nhật kết quả thành công"},
                    status_code=200,
                    ip_address=ip_address
                )
                
                return WebhookResponse(
                    success=True,
                    message="Cập nhật kết quả thành công",
                    data={
                        "lottery_draw_id": lottery_draw.id,
                        "lottery_type": lottery_type.code,
                        "draw_date": webhook_data.draw_date.isoformat(),
                        "is_complete": is_complete
                    }
                ), 200
            else:
                # Chưa có kết quả, tạo mới
                logger.info(f"Tạo kết quả xổ số mới {lottery_type.code} ngày {webhook_data.draw_date}")
                
                # Kiểm tra xem kết quả đã đầy đủ chưa
                is_complete = self._check_complete_results(prizes_dict, lottery_type.code)
                
                # Tạo kết quả mới
                new_draw = LotteryDraw(
                    lottery_type_id=lottery_type.id,
                    draw_date=webhook_data.draw_date,
                    results=prizes_dict,
                    is_complete=is_complete,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                self.db.add(new_draw)
                await self.db.commit()
                await self.db.refresh(new_draw)
                
                # Tạo log
                await WebhookLog.create(
                    self.db,
                    lottery_draw_id=new_draw.id,
                    lottery_type_code=lottery_type.code,
                    draw_date=webhook_data.draw_date,
                    request_data=webhook_data.dict(),
                    response_data={"success": True, "message": "Tạo kết quả mới thành công"},
                    status_code=201,
                    ip_address=ip_address
                )
                
                return WebhookResponse(
                    success=True,
                    message="Tạo kết quả mới thành công",
                    data={
                        "lottery_draw_id": new_draw.id,
                        "lottery_type": lottery_type.code,
                        "draw_date": webhook_data.draw_date.isoformat(),
                        "is_complete": is_complete
                    }
                ), 201
        except Exception as e:
            logger.error(f"Lỗi khi xử lý webhook: {str(e)}", exc_info=True)
            
            # Tạo log
            try:
                await WebhookLog.create(
                    self.db,
                    lottery_type_code=webhook_data.lottery_type if hasattr(webhook_data, 'lottery_type') else None,
                    draw_date=webhook_data.draw_date if hasattr(webhook_data, 'draw_date') else None,
                    request_data=webhook_data.dict() if hasattr(webhook_data, 'dict') else {},
                    response_data={"success": False, "message": "Lỗi khi xử lý webhook"},
                    status_code=500,
                    error_message=str(e),
                    ip_address=ip_address
                )
            except Exception as log_error:
                logger.error(f"Lỗi khi tạo log: {str(log_error)}", exc_info=True)
            
            return WebhookResponse(
                success=False,
                message="Lỗi khi xử lý webhook",
                errors=[str(e)]
            ), 500
    
    def _check_complete_results(self, results: Dict[str, Any], lottery_type: str) -> bool:
        """
        Kiểm tra xem kết quả đã đầy đủ chưa
        
        Args:
            results: Kết quả xổ số
            lottery_type: Mã loại xổ số
            
        Returns:
            bool: True nếu kết quả đã đầy đủ, False nếu chưa
        """
        # Danh sách các giải cần có cho từng loại xổ số
        required_prizes = {
            'mb': ['special', 'first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh'],
            'mt': ['special', 'first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh', 'eighth'],
            'mn': ['special', 'first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh', 'eighth']
        }
        
        # Lấy danh sách giải cần có
        required = required_prizes.get(lottery_type, [])
        
        # Kiểm tra xem tất cả các giải cần có đã có trong kết quả chưa
        return all(prize in results for prize in required)
    
    async def get_webhook_logs(
        self, 
        lottery_type_code: Optional[str] = None,
        draw_date: Optional[date] = None,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[WebhookLog], int]:
        """
        Lấy danh sách log webhook
        
        Args:
            lottery_type_code: Mã loại xổ số (tùy chọn)
            draw_date: Ngày quay thưởng (tùy chọn)
            limit: Số lượng kết quả tối đa
            offset: Vị trí bắt đầu
            
        Returns:
            Tuple[List[WebhookLog], int]: Danh sách log và tổng số log
        """
        # Tạo query
        query = WebhookLog.__table__.select()
        count_query = WebhookLog.__table__.count()
        
        # Thêm điều kiện
        if lottery_type_code:
            query = query.where(WebhookLog.lottery_type_code == lottery_type_code)
            count_query = count_query.where(WebhookLog.lottery_type_code == lottery_type_code)
        
        if draw_date:
            query = query.where(WebhookLog.draw_date == draw_date)
            count_query = count_query.where(WebhookLog.draw_date == draw_date)
        
        # Thêm sắp xếp và phân trang
        query = query.order_by(WebhookLog.created_at.desc()).offset(offset).limit(limit)
        
        # Thực hiện query
        result = await self.db.execute(query)
        logs = result.scalars().all()
        
        # Đếm tổng số log
        count_result = await self.db.execute(count_query)
        total_count = count_result.scalar()
        
        return logs, total_count
