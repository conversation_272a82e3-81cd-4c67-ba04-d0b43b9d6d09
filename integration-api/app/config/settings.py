import os
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class DatabaseSettings(BaseModel):
    host: str = os.getenv("POSTGRES_HOST", "postgres")
    port: int = int(os.getenv("POSTGRES_PORT", "5432"))
    user: str = os.getenv("POSTGRES_USER", "xosotv_user")
    password: str = os.getenv("POSTGRES_PASSWORD", "")
    database: str = os.getenv("POSTGRES_DB", "xosotv")

    # SQLAlchemy connection pool settings
    pool_size: int = int(os.getenv("POSTGRES_POOL_SIZE", "5"))
    max_overflow: int = int(os.getenv("POSTGRES_MAX_OVERFLOW", "10"))
    pool_timeout: int = int(os.getenv("POSTGRES_POOL_TIMEOUT", "30"))
    pool_recycle: int = int(os.getenv("POSTGRES_POOL_RECYCLE", "1800"))  # 30 phút

    @property
    def connection_string(self) -> str:
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"

    @property
    def async_connection_string(self) -> str:
        return f"postgresql+asyncpg://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"

class RedisSettings(BaseModel):
    host: str = os.getenv("REDIS_HOST", "redis")
    port: int = int(os.getenv("REDIS_PORT", "6379"))
    db: int = int(os.getenv("REDIS_DB", "0"))
    password: Optional[str] = os.getenv("REDIS_PASSWORD", None)
    socket_timeout: float = float(os.getenv("REDIS_SOCKET_TIMEOUT", "5.0"))
    socket_connect_timeout: float = float(os.getenv("REDIS_CONNECT_TIMEOUT", "5.0"))
    retry_on_timeout: bool = os.getenv("REDIS_RETRY_ON_TIMEOUT", "True").lower() == "true"
    max_connections: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "10"))

    @property
    def connection_string(self) -> str:
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        return f"redis://{self.host}:{self.port}/{self.db}"

class WebhookSettings(BaseModel):
    secret_key: str = os.getenv("WEBHOOK_SECRET_KEY", "your-webhook-secret-key-change-in-production")
    allowed_ips: List[str] = Field(
        default_factory=lambda: os.getenv("WEBHOOK_ALLOWED_IPS", "").split(",") if os.getenv("WEBHOOK_ALLOWED_IPS") else []
    )
    verify_signature: bool = os.getenv("WEBHOOK_VERIFY_SIGNATURE", "True").lower() == "true"
    signature_header: str = os.getenv("WEBHOOK_SIGNATURE_HEADER", "X-Webhook-Signature")

class SecuritySettings(BaseModel):
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    algorithm: str = os.getenv("JWT_ALGORITHM", "HS256")
    access_token_expire_minutes: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    refresh_token_expire_days: int = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

class LoggingSettings(BaseModel):
    level: str = os.getenv("LOG_LEVEL", "INFO")
    format: str = "%(asctime)s | %(levelname)s | %(name)s | %(message)s"
    file_path: Optional[str] = os.getenv("LOG_FILE", "logs/integration-api.log")
    json_file_path: Optional[str] = os.getenv("JSON_LOG_FILE", "logs/integration-api.json.log")
    rotation: str = os.getenv("LOG_ROTATION", "1 day")
    retention: str = os.getenv("LOG_RETENTION", "7 days")
    webhook_log_file: Optional[str] = os.getenv("WEBHOOK_LOG_FILE", "logs/webhook.log")
    log_request_body: bool = os.getenv("LOG_REQUEST_BODY", "False").lower() == "true"
    log_response_body: bool = os.getenv("LOG_RESPONSE_BODY", "False").lower() == "true"

class Settings(BaseModel):
    app_name: str = "XỔ SỐ TV Integration API"
    debug: bool = os.getenv("DEBUG", "False").lower() == "true"
    timezone: str = os.getenv("TZ", "Asia/Ho_Chi_Minh")
    environment: str = os.getenv("ENVIRONMENT", "development")
    version: str = os.getenv("VERSION", "1.0.0")
    app_port: int = int(os.getenv("INTEGRATION_API_PORT", "9000"))

    # CORS settings
    cors_origins: List[str] = Field(
        default_factory=lambda: os.getenv("CORS_ORIGINS", "*").split(",")
    )

    # Cài đặt cache
    cache_ttl: int = int(os.getenv("CACHE_TTL", "3600"))  # Mặc định 1 giờ
    cache_prefix: str = os.getenv("CACHE_PREFIX", "xosotv:")

    # Kết nối các dịch vụ
    db: DatabaseSettings = DatabaseSettings()
    redis: RedisSettings = RedisSettings()
    webhook: WebhookSettings = WebhookSettings()
    security: SecuritySettings = SecuritySettings()
    logging: LoggingSettings = LoggingSettings()

# Tạo instance settings
settings = Settings()
