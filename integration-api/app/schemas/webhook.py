from pydantic import BaseModel, Field, validator
from typing import Dict, Any, List, Optional, Union
from datetime import date, datetime
import re

class LotteryPrize(BaseModel):
    """Schema cho giải thưởng xổ số"""
    prize_name: str = Field(..., description="<PERSON><PERSON><PERSON> gi<PERSON> (special, first, second, ...)")
    numbers: Union[List[str], str] = Field(..., description="Số trúng thưởng (có thể là một số hoặc danh sách các số)")
    
    @validator('prize_name')
    def validate_prize_name(cls, v):
        allowed_prizes = ['special', 'first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh', 'eighth']
        if v not in allowed_prizes:
            raise ValueError(f"Tên giải không hợp lệ. Các giá trị cho phép: {', '.join(allowed_prizes)}")
        return v
    
    @validator('numbers')
    def validate_numbers(cls, v):
        if isinstance(v, str):
            # <PERSON><PERSON><PERSON> tra số đơn
            if not re.match(r'^\d+$', v):
                raise ValueError("Số trúng thưởng phải chỉ chứa các chữ số")
        elif isinstance(v, list):
            # Kiểm tra danh sách số
            for num in v:
                if not isinstance(num, str) or not re.match(r'^\d+$', num):
                    raise ValueError("Mỗi số trong danh sách phải là chuỗi chỉ chứa các chữ số")
        else:
            raise ValueError("Số trúng thưởng phải là chuỗi hoặc danh sách chuỗi")
        return v

class WebhookRequest(BaseModel):
    """Schema cho request webhook"""
    lottery_type: str = Field(..., description="Mã loại xổ số (mb, mt, mn, ...)")
    draw_date: datetime.date = Field(..., description="Ngày quay thưởng (YYYY-MM-DD)")
    prizes: List[LotteryPrize] = Field(..., description="Danh sách các giải thưởng")
    source: Optional[str] = Field(None, description="Nguồn dữ liệu")
    timestamp: Optional[datetime] = Field(None, description="Thời gian gửi dữ liệu")
    
    @validator('lottery_type')
    def validate_lottery_type(cls, v):
        allowed_types = ['mb', 'mt', 'mn']
        if v not in allowed_types:
            raise ValueError(f"Loại xổ số không hợp lệ. Các giá trị cho phép: {', '.join(allowed_types)}")
        return v
    
    @validator('draw_date')
    def validate_draw_date(cls, v):
        today = date.today()
        if v > today:
            raise ValueError("Ngày quay thưởng không thể là ngày trong tương lai")
        return v

class WebhookResponse(BaseModel):
    """Schema cho response webhook"""
    success: bool = Field(..., description="Trạng thái xử lý")
    message: str = Field(..., description="Thông báo")
    data: Optional[Dict[str, Any]] = Field(None, description="Dữ liệu trả về")
    errors: Optional[List[str]] = Field(None, description="Danh sách lỗi (nếu có)")

class WebhookLogResponse(BaseModel):
    """Schema cho response log webhook"""
    id: int = Field(..., description="ID log")
    lottery_type_code: str = Field(..., description="Mã loại xổ số")
    draw_date: datetime.date = Field(..., description="Ngày quay thưởng")
    status_code: int = Field(..., description="Mã trạng thái HTTP")
    created_at: datetime = Field(..., description="Thời gian tạo")
    request_data: Dict[str, Any] = Field(..., description="Dữ liệu request")
    response_data: Optional[Dict[str, Any]] = Field(None, description="Dữ liệu response")
    error_message: Optional[str] = Field(None, description="Thông báo lỗi")

class WebhookLogListResponse(BaseModel):
    """Schema cho response danh sách log webhook"""
    logs: List[WebhookLogResponse] = Field(..., description="Danh sách log")
    count: int = Field(..., description="Tổng số log")
