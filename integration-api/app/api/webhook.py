from fastapi import APIRouter, Depends, HTTPException, Request, Header, Query
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
from datetime import date

from app.db.session import get_db
from app.services.webhook import WebhookService
from app.schemas.webhook import WebhookRequest, WebhookResponse, WebhookLogListResponse, WebhookLogResponse
from app.utils.webhook_logger import webhook_logger
from app.config.settings import settings

router = APIRouter()

@router.post("/lottery", response_model=WebhookResponse)
async def receive_lottery_results(
    request: Request,
    webhook_data: WebhookRequest,
    signature: Optional[str] = Header(None, alias=settings.webhook.signature_header),
    db: AsyncSession = Depends(get_db)
):
    """
    Webhook để nhận kết quả xổ số từ hệ thống core
    
    - Mỗi request có thể chỉ chứa mộ<PERSON> phần củ<PERSON> kết quả (ví dụ: chỉ có giải 7, hoặc gi<PERSON>i 5 và 6)
    - <PERSON>ế<PERSON> quả sẽ được tổng hợp thành kết quả đầy đủ cho mỗi loại xổ số trong ngày
    """
    # Lấy địa chỉ IP của người gửi
    client_ip = request.client.host if request.client else None
    
    # Ghi log request
    webhook_logger.log_request(
        request_data=webhook_data.dict(),
        ip_address=client_ip,
        signature=signature
    )
    
    # Xác thực chữ ký nếu cần
    if settings.webhook.verify_signature:
        # Lấy nội dung request dạng chuỗi
        body = await request.body()
        payload = body.decode()
        
        # Xác thực chữ ký
        webhook_service = WebhookService(db)
        if not webhook_service.verify_signature(payload, signature):
            error_message = "Chữ ký không hợp lệ"
            webhook_logger.log_error(
                error_message=error_message,
                request_data=webhook_data.dict(),
                ip_address=client_ip
            )
            return JSONResponse(
                status_code=401,
                content=WebhookResponse(
                    success=False,
                    message=error_message,
                    errors=["Chữ ký không khớp hoặc không có"]
                ).dict()
            )
    
    # Xử lý webhook
    try:
        webhook_service = WebhookService(db)
        response, status_code = await webhook_service.process_webhook(webhook_data, client_ip)
        
        # Ghi log response
        webhook_logger.log_response(
            response_data=response.dict(),
            status_code=status_code,
            request_data=webhook_data.dict(),
            ip_address=client_ip
        )
        
        return JSONResponse(
            status_code=status_code,
            content=response.dict()
        )
    except Exception as e:
        error_message = f"Lỗi khi xử lý webhook: {str(e)}"
        webhook_logger.log_error(
            error_message=error_message,
            request_data=webhook_data.dict(),
            ip_address=client_ip
        )
        return JSONResponse(
            status_code=500,
            content=WebhookResponse(
                success=False,
                message="Lỗi khi xử lý webhook",
                errors=[str(e)]
            ).dict()
        )

@router.get("/logs", response_model=WebhookLogListResponse)
async def get_webhook_logs(
    lottery_type: Optional[str] = Query(None, description="Mã loại xổ số"),
    draw_date: Optional[date] = Query(None, description="Ngày quay thưởng (YYYY-MM-DD)"),
    limit: int = Query(100, description="Số lượng kết quả tối đa", ge=1, le=1000),
    offset: int = Query(0, description="Vị trí bắt đầu", ge=0),
    db: AsyncSession = Depends(get_db)
):
    """
    Lấy danh sách log webhook
    """
    try:
        webhook_service = WebhookService(db)
        logs, total_count = await webhook_service.get_webhook_logs(
            lottery_type_code=lottery_type,
            draw_date=draw_date,
            limit=limit,
            offset=offset
        )
        
        # Chuyển đổi sang schema response
        log_responses = []
        for log in logs:
            log_responses.append(WebhookLogResponse(
                id=log.id,
                lottery_type_code=log.lottery_type_code,
                draw_date=log.draw_date,
                status_code=log.status_code,
                created_at=log.created_at,
                request_data=log.request_data,
                response_data=log.response_data,
                error_message=log.error_message
            ))
        
        return WebhookLogListResponse(
            logs=log_responses,
            count=total_count
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi khi lấy log webhook: {str(e)}"
        )
