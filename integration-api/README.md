# XỔ SỐ TV Integration API

API tích hợp cho hệ thống XỔ SỐ TV, cung cấp webhook để nhận kết quả xổ số từ hệ thống core.

## Tính năng chính

- Webhook nhận kết quả xổ số từ hệ thống core
- Tổng hợp kết quả từ nhiều lần nhận thành kết quả đầy đủ
- Xác thực webhook bằng chữ ký và IP
- Ghi log chi tiết các request webhook
- API để xem lịch sử webhook

## Cấu trúc dự án

```
integration-api/
├── Dockerfile
├── requirements.txt
├── start-container.sh
├── app/
│   ├── __init__.py
│   ├── main.py          # Điểm vào FastAPI
│   ├── api/             # Định nghĩa các route
│   │   ├── __init__.py
│   │   └── webhook.py
│   ├── models/          # Định nghĩa các model
│   │   ├── __init__.py
│   │   └── lottery.py
│   ├── schemas/         # Đ<PERSON>nh nghĩa Pydantic schema
│   │   ├── __init__.py
│   │   └── webhook.py
│   ├── services/        # Logic, business layer
│   │   ├── __init__.py
│   │   └── webhook.py
│   ├── db/              # Kết nối database
│   │   ├── __init__.py
│   │   ├── base.py
│   │   └── session.py
│   ├── config/          # Cấu hình
│   │   ├── __init__.py
│   │   └── settings.py
│   └── utils/           # Tiện ích
│       ├── __init__.py
│       └── webhook_logger.py
```

## Webhook API

### Nhận kết quả xổ số

```
POST /api/webhook/lottery
```

**Headers:**
- `X-Webhook-Signature`: Chữ ký HMAC-SHA256 của payload (nếu bật xác thực)

**Request Body:**
```json
{
  "lottery_type": "mb",
  "draw_date": "2023-11-15",
  "prizes": [
    {
      "prize_name": "special",
      "numbers": "12345"
    },
    {
      "prize_name": "first",
      "numbers": "67890"
    },
    {
      "prize_name": "second",
      "numbers": ["11111", "22222"]
    }
  ],
  "source": "core-system",
  "timestamp": "2023-11-15T15:30:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Cập nhật kết quả thành công",
  "data": {
    "lottery_draw_id": 1,
    "lottery_type": "mb",
    "draw_date": "2023-11-15",
    "is_complete": false
  }
}
```

### Lấy danh sách log webhook

```
GET /api/webhook/logs
```

**Query Parameters:**
- `lottery_type`: Mã loại xổ số (tùy chọn)
- `draw_date`: Ngày quay thưởng (YYYY-MM-DD) (tùy chọn)
- `limit`: Số lượng kết quả tối đa (mặc định: 100)
- `offset`: Vị trí bắt đầu (mặc định: 0)

**Response:**
```json
{
  "logs": [
    {
      "id": 1,
      "lottery_type_code": "mb",
      "draw_date": "2023-11-15",
      "status_code": 200,
      "created_at": "2023-11-15T15:30:00Z",
      "request_data": {
        "lottery_type": "mb",
        "draw_date": "2023-11-15",
        "prizes": [
          {
            "prize_name": "special",
            "numbers": "12345"
          }
        ]
      },
      "response_data": {
        "success": true,
        "message": "Cập nhật kết quả thành công"
      },
      "error_message": null
    }
  ],
  "count": 1
}
```

## Xác thực Webhook

Webhook hỗ trợ hai cơ chế xác thực:

1. **Xác thực chữ ký**: Hệ thống core phải tính toán HMAC-SHA256 của payload sử dụng secret key và gửi kèm trong header `X-Webhook-Signature`.

2. **Xác thực IP**: Chỉ chấp nhận request từ các địa chỉ IP được cấu hình trong `WEBHOOK_ALLOWED_IPS`.

## Cài đặt và chạy

### Sử dụng Docker

1. Đảm bảo đã cấu hình file `.env` trong thư mục gốc của dự án.

2. Xây dựng và chạy container:
   ```bash
   docker build -t integration-api .
   docker run -p 9000:9000 --env-file ../.env integration-api
   ```

### Sử dụng Docker Compose

Sử dụng docker-compose.yml trong thư mục gốc của dự án:

```bash
cd .. # Đảm bảo bạn đang ở thư mục gốc của dự án
docker-compose up -d integration-api
```

### Chạy trực tiếp

1. Cài đặt các gói phụ thuộc:
   ```bash
   pip install -r requirements.txt
   ```

2. Chạy ứng dụng:
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 9000 --reload
   ```

## Biến môi trường

Các biến môi trường được cấu hình trong file `.env` ở thư mục gốc của dự án. Các biến môi trường chính cho integration-api bao gồm:

| Biến | Mô tả | Giá trị mặc định |
|------|-------|-----------------|
| `INTEGRATION_API_PORT` | Port cho integration-api | `9000` |
| `WEBHOOK_SECRET_KEY` | Khóa bí mật cho webhook | `your-webhook-secret-key-change-in-production` |
| `WEBHOOK_ALLOWED_IPS` | Danh sách IP được phép (phân cách bằng dấu phẩy) | `` |
| `WEBHOOK_VERIFY_SIGNATURE` | Bật/tắt xác thực chữ ký | `True` |
| `WEBHOOK_SIGNATURE_HEADER` | Tên header chứa chữ ký | `X-Webhook-Signature` |

Xem file `.env.example` trong thư mục gốc để biết thêm các biến môi trường khác.

## Tạo chữ ký webhook

Để tạo chữ ký webhook, hệ thống core cần tính toán HMAC-SHA256 của payload sử dụng secret key. Dưới đây là ví dụ bằng Python:

```python
import hmac
import hashlib

def generate_signature(payload, secret_key):
    return hmac.new(
        secret_key.encode(),
        payload.encode(),
        hashlib.sha256
    ).hexdigest()

# Sử dụng
payload = '{"lottery_type":"mb","draw_date":"2023-11-15","prizes":[{"prize_name":"special","numbers":"12345"}]}'
secret_key = 'your-webhook-secret-key'
signature = generate_signature(payload, secret_key)
print(signature)
```

## Tài liệu API

Sau khi chạy ứng dụng, bạn có thể truy cập tài liệu API tại:

- Swagger UI: http://localhost:9000/docs
- ReDoc: http://localhost:9000/redoc
