#!/usr/bin/env python3
"""
Final test để kiểm tra RecursionError đã được sửa
"""

import sys
import os

# Thêm đường dẫn
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'chatbot-service'))

print("=== FINAL RECURSION ERROR TEST ===")

# Test import từng bước
tests = [
    ("Basic imports", lambda: __import__('typing')),
    ("Pydantic", lambda: __import__('pydantic')),
    ("SQLAlchemy", lambda: __import__('sqlalchemy')),
    ("App config", lambda: __import__('app.config', fromlist=['settings'])),
    ("App base", lambda: __import__('app.db.base', fromlist=['Base'])),
    ("User model", lambda: __import__('app.models.user_query', fromlist=['UserQueryModel'])),
    ("Lottery models", lambda: __import__('app.models.lottery', fromlist=['LotteryTypeModel'])),
    ("Chat schemas", lambda: __import__('app.schemas.chat', fromlist=['ChatRequestSchema'])),
    ("Lottery schemas", lambda: __import__('app.schemas.lottery', fromlist=['LotteryTypeSchema'])),
    ("Statistics schemas", lambda: __import__('app.schemas.statistics', fromlist=['StatisticsResponseSchema'])),
    ("Intent analyzer", lambda: __import__('app.services.intent_analyzer', fromlist=['IntentAnalyzer'])),
    ("Lottery service", lambda: __import__('app.services.lottery', fromlist=['LotteryService'])),
    ("Chatbot service", lambda: __import__('app.services.chatbot', fromlist=['ChatbotService'])),
    ("Statistics service", lambda: __import__('app.services.statistics', fromlist=['StatisticsService'])),
]

for test_name, test_func in tests:
    try:
        print(f"Testing {test_name}...", end=" ")
        test_func()
        print("✅")
    except Exception as e:
        print(f"❌ {e}")
        import traceback
        traceback.print_exc()
        exit(1)

print("\n🎉 ALL TESTS PASSED!")
print("✅ RecursionError has been completely resolved!")
print("✅ All schemas and services can be imported successfully!")
print("✅ Forward references are working correctly!")

# Test schema creation
try:
    print("\nTesting schema creation...")
    from app.schemas.lottery import LotteryTypeSchema
    
    lottery = LotteryTypeSchema(
        id=1,
        code="mb", 
        name="Miền Bắc",
        region="Bắc",
        description="Test"
    )
    
    print(f"✅ Schema creation successful: {lottery.model_dump()}")
    
except Exception as e:
    print(f"❌ Schema creation failed: {e}")
    exit(1)

print("\n🚀 System is ready to use!")
