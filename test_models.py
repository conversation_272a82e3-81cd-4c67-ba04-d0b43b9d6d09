#!/usr/bin/env python3
"""
Test script để kiểm tra các model đã được đổi tên thành công
"""

import sys
import os

# Thêm đường dẫn để import được các module
sys.path.append(os.path.join(os.path.dirname(__file__), 'chatbot-service'))

try:
    print("Testing chatbot-service models...")
    from chatbot_service.app.models.user_query import UserQueryModel
    from chatbot_service.app.models.lottery import LotteryTypeModel, LotteryDrawModel
    print("✅ Chatbot-service models imported successfully")
    print(f"  - UserQueryModel: {UserQueryModel.__tablename__}")
    print(f"  - LotteryTypeModel: {LotteryTypeModel.__tablename__}")
    print(f"  - LotteryDrawModel: {LotteryDrawModel.__tablename__}")
except Exception as e:
    print(f"❌ Chatbot-service models failed: {str(e)}")

try:
    print("\nTesting integration-api models...")
    sys.path.append(os.path.join(os.path.dirname(__file__), 'integration-api'))
    from integration_api.app.models.lottery import LotteryTypeModel, LotteryDrawModel, WebhookLogModel
    print("✅ Integration-api models imported successfully")
    print(f"  - LotteryTypeModel: {LotteryTypeModel.__tablename__}")
    print(f"  - LotteryDrawModel: {LotteryDrawModel.__tablename__}")
    print(f"  - WebhookLogModel: {WebhookLogModel.__tablename__}")
except Exception as e:
    print(f"❌ Integration-api models failed: {str(e)}")

print("\n🎉 Model renaming test completed!")
