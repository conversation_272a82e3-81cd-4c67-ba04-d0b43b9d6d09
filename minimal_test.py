#!/usr/bin/env python3
"""
Minimal test để tái tạo schema
"""

print("=== MINIMAL SCHEMA TEST ===")

# Test 1: Basic imports
try:
    print("1. Testing basic imports...")
    from pydantic import BaseModel, Field
    from typing import Dict, Any, List, Optional
    from datetime import date
    print("✅ Basic imports OK")
except Exception as e:
    print(f"❌ Basic imports failed: {e}")
    exit(1)

# Test 2: Create simple schema
try:
    print("2. Creating simple schema...")
    
    class LotteryTypeBaseSchema(BaseModel):
        """Schema cơ bản cho loại xổ số"""
        code: str = Field(..., description="Mã loại xổ số")
        name: str = Field(..., description="Tên loại xổ số")
        region: Optional[str] = Field(None, description="Khu vực")
        description: Optional[str] = Field(None, description="Mô tả")

    class LotteryTypeSchema(LotteryTypeBaseSchema):
        """Schema cho loại xổ số"""
        id: int = Field(..., description="ID loại xổ số")

        class Config:
            from_attributes = True
    
    print("✅ Simple schema creation OK")
except Exception as e:
    print(f"❌ Simple schema creation failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 3: Test schema instantiation
try:
    print("3. Testing schema instantiation...")
    
    lottery_type = LotteryTypeSchema(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Xổ số miền Bắc"
    )
    
    print(f"✅ Schema instantiation OK: {lottery_type.name}")
except Exception as e:
    print(f"❌ Schema instantiation failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 All minimal tests passed!")
print("✅ Pydantic schemas work fine!")
