#!/bin/bash
set -e

echo "=== [llm-core] Start: Warming up environment... ==="

# Xuất thông tin model và môi trường
echo "Model path: ${LLM_MODEL_PATH}"
echo "Threads: ${LLM_THREADS:-4}"
echo "Context Length: ${LLM_CTX_SIZE:-4096}"
echo "Lazy Loading: ${LLM_LAZY_LOAD:-true}"

# Kiểm tra model có tồn tại không - đây là điều kiện tiên quyết
if [ ! -f "${LLM_MODEL_PATH}" ]; then
  echo "❌ Model file not found at: $LLM_MODEL_PATH"
  exit 1
fi

# In phiên bản Python & pip
python3 --version
pip3 --version

# Dọn dẹp bộ nhớ trước khi khởi động
echo "Tối ưu hóa bộ nhớ khả dụng..."
sync
# Không thay đổi cấu hình hệ thống từ container
echo "Sử dụng cơ chế tối ưu hóa bộ nhớ nội bộ..."

# Kiểm tra dung lượng bộ nhớ có sẵn trước khi khởi động
FREE_MEM=$(free -m | awk '/^Mem:/{print $4}')
TOTAL_MEM=$(free -m | awk '/^Mem:/{print $2}')
SWAP_MEM=$(free -m | awk '/^Swap:/{print $2}')

echo "Thông tin bộ nhớ:"
echo "- Bộ nhớ tổng: ${TOTAL_MEM}MB"
echo "- Bộ nhớ trống: ${FREE_MEM}MB"
echo "- Swap: ${SWAP_MEM}MB"

# Đảm bảo có ít nhất 4GB bộ nhớ trống để tải model
if [ ${FREE_MEM} -lt 4096 ]; then
  echo "⚠️ Cảnh báo: Bộ nhớ trống (${FREE_MEM}MB) có thể không đủ để tải model 7B!"
  
  # Nếu bộ nhớ quá thấp, sẽ điều chỉnh cấu hình để tối ưu hóa
  if [ "${LLM_LAZY_LOAD}" != "true" ]; then
    echo "⚠️ Kích hoạt chế độ lazy loading do bộ nhớ thấp"
    export LLM_LAZY_LOAD=true
  fi
  
  # Giảm context size nếu bộ nhớ thấp
  if [ ${FREE_MEM} -lt 2048 ] && [ ${LLM_CTX_SIZE:-4096} -gt 2048 ]; then
    echo "⚠️ Tự động giảm context size xuống 2048 do bộ nhớ thấp"
    export LLM_CTX_SIZE=2048
  fi
  
  # Tắt mlock để tránh giữ bộ nhớ
  export LLM_USE_MLOCK=false
fi

# Đặt biến môi trường Python để kiểm soát bộ nhớ tốt hơn
export MALLOC_TRIM_THRESHOLD_=100000
export PYTHONMALLOC=malloc
export OMP_NUM_THREADS=${LLM_THREADS:-4}
export PYTORCH_CUDA_ALLOC_CONF=garbage_collection_threshold:0.6,max_split_size_mb:128

# Giảm mức sử dụng bộ nhớ của Python
python3 -c "import gc; gc.set_threshold(100, 5, 5)"

# Khởi động API server không tải model ngay
echo "=== [llm-core] Start FastAPI server ==="
# Sử dụng một worker để tránh tải model nhiều lần và giới hạn số lượng request đồng thời
exec uvicorn main:app --host 0.0.0.0 --port 8080 --log-level info --workers 1 --limit-concurrency 100 --timeout-keep-alive 120
