#!/usr/bin/env python3
"""
Script kiểm tra TimescaleDB hoạt động đúng
"""

import asyncio
import json
import httpx
from datetime import datetime

async def test_timescaledb_info():
    """Kiểm tra thông tin TimescaleDB"""
    print("🔍 Kiểm tra thông tin TimescaleDB...")

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8000/timescaledb/info")
            if response.status_code == 200:
                data = response.json()
                print("✅ TimescaleDB đang hoạt động!")
                print(f"📊 Phiên bản: {data.get('timescaledb_version', 'N/A')}")
                print(f"🗂️ Số hypertables: {len(data.get('hypertables', []))}")

                for ht in data.get('hypertables', []):
                    print(f"   - {ht['hypertable_name']}: {ht['num_chunks']} chunks")

                return True
            else:
                print(f"❌ Lỗi: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Không thể kết nối: {e}")
            return False

async def test_chat_functionality():
    """Kiểm tra chức năng chat"""
    print("\n💬 Kiểm tra chức năng chat...")

    test_messages = [
        "Xin chào!",
        "Kết quả xổ số miền Bắc hôm nay",
        "Thống kê số nóng tháng này",
        "Dự đoán số may mắn"
    ]

    async with httpx.AsyncClient() as client:
        for i, message in enumerate(test_messages, 1):
            try:
                print(f"\n📝 Test {i}: {message}")

                response = await client.post(
                    "http://localhost:8000/api/v1/chat",
                    json={
                        "user_id": "test_user",
                        "message": message
                    },
                    timeout=30.0
                )

                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ Phản hồi: {data['response'][:100]}...")
                    print(f"🎯 Ý định: {data.get('intent', 'N/A')}")
                    print(f"⏱️ Thời gian xử lý: {data.get('processing_time', 'N/A')}s")
                else:
                    print(f"❌ Lỗi: {response.status_code}")
                    print(f"📄 Chi tiết: {response.text}")

            except Exception as e:
                print(f"❌ Lỗi khi test chat: {e}")

async def test_health_checks():
    """Kiểm tra health check của các service"""
    print("\n🏥 Kiểm tra health check...")

    services = [
        ("Chatbot Service", "http://localhost:8000/health"),
        ("Integration API", "http://localhost:9000/health"),
        ("LLM Core", "http://localhost:8080/health"),
        ("pgAdmin (DB Management)", "http://localhost:8081")
    ]

    async with httpx.AsyncClient() as client:
        for name, url in services:
            try:
                response = await client.get(url, timeout=10.0)
                if response.status_code == 200:
                    print(f"✅ {name}: Hoạt động bình thường")
                else:
                    print(f"⚠️ {name}: Có vấn đề ({response.status_code})")
            except Exception as e:
                print(f"❌ {name}: Không thể kết nối ({e})")

async def test_cache_stats():
    """Kiểm tra thống kê cache"""
    print("\n📊 Kiểm tra thống kê cache...")

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8000/cache/stats")
            if response.status_code == 200:
                data = response.json()
                cache_stats = data.get('cache_stats', {})
                overall = cache_stats.get('overall', {})

                print(f"✅ Cache đang hoạt động!")
                print(f"📈 Hit rate: {overall.get('hit_rate', 0):.2%}")
                print(f"🔢 Tổng requests: {overall.get('total_requests', 0)}")
                print(f"💾 L1 hits: {overall.get('l1_hits', 0)}")
                print(f"🔄 L2 hits: {overall.get('l2_hits', 0)}")
            else:
                print(f"❌ Lỗi cache stats: {response.status_code}")
        except Exception as e:
            print(f"❌ Không thể lấy cache stats: {e}")

async def test_storage_info():
    """Kiểm tra thông tin storage và lưu trữ vĩnh viễn"""
    print("\n💾 Kiểm tra thông tin storage...")

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8000/storage/info")
            if response.status_code == 200:
                data = response.json()

                print(f"✅ Storage đang hoạt động!")
                print(f"📋 Chính sách: {data.get('storage_policy', 'N/A')}")
                print(f"💽 Tổng kích thước: {data.get('total_size_gb', 0)} GB")

                storage_monitoring = data.get('storage_monitoring', [])
                for table in storage_monitoring:
                    print(f"   - {table['table_name']}: {table['total_size']} ({table['row_count']} records)")

                compression_stats = data.get('compression_stats', [])
                if compression_stats:
                    print("🗜️ Thống kê nén:")
                    for stat in compression_stats:
                        print(f"   - {stat['hypertable_name']}: {stat['compression_ratio']:.1f}% ratio")

            else:
                print(f"❌ Lỗi storage info: {response.status_code}")
        except Exception as e:
            print(f"❌ Không thể lấy storage info: {e}")

async def test_storage_report():
    """Kiểm tra báo cáo storage"""
    print("\n📋 Kiểm tra báo cáo storage...")

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8000/storage/report")
            if response.status_code == 200:
                data = response.json()
                report = data.get('report', '')

                print("✅ Báo cáo storage:")
                print("=" * 50)
                print(report)
                print("=" * 50)
            else:
                print(f"❌ Lỗi storage report: {response.status_code}")
        except Exception as e:
            print(f"❌ Không thể lấy storage report: {e}")

async def main():
    """Chạy tất cả các test"""
    print("🚀 Bắt đầu kiểm tra hệ thống XỔ SỐ TV Chatbot với TimescaleDB")
    print("=" * 60)

    # Kiểm tra TimescaleDB
    timescaledb_ok = await test_timescaledb_info()

    if not timescaledb_ok:
        print("\n❌ TimescaleDB không hoạt động. Dừng test.")
        return

    # Kiểm tra health checks
    await test_health_checks()

    # Kiểm tra cache
    await test_cache_stats()

    # Kiểm tra storage và lưu trữ vĩnh viễn
    await test_storage_info()
    await test_storage_report()

    # Kiểm tra chat functionality
    await test_chat_functionality()

    print("\n" + "=" * 60)
    print("🎉 Hoàn thành kiểm tra hệ thống!")
    print("\n💡 Để xem chi tiết:")
    print("   - Database Management: http://localhost:8081")
    print("   - TimescaleDB info: http://localhost:8000/timescaledb/info")
    print("   - Storage info: http://localhost:8000/storage/info")
    print("   - Storage report: http://localhost:8000/storage/report")
    print("   - Cache stats: http://localhost:8000/cache/stats")
    print("   - Health check: http://localhost:8000/health")
    print("   - API docs: http://localhost:8000/docs")
    print("\n🗂️ Lưu trữ vĩnh viễn:")
    print("   - Dữ liệu xổ số được lưu trữ vĩnh viễn")
    print("   - Compression tự động để tiết kiệm storage")
    print("   - Monitoring và báo cáo định kỳ")

if __name__ == "__main__":
    asyncio.run(main())
