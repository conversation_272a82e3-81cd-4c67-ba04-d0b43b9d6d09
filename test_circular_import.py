#!/usr/bin/env python3
"""
Test script để kiểm tra circular import đã được giải quyết
"""

import sys
import os

# Thêm đường dẫn để import được các module
sys.path.append(os.path.join(os.path.dirname(__file__), 'chatbot-service'))

print("=== TESTING CIRCULAR IMPORT RESOLUTION ===")

# Test 1: Import schemas
try:
    print("1. Testing schema imports...")
    from app.schemas.lottery import LotteryTypeSchema, LotteryResultResponseSchema
    from app.schemas.chat import ChatRequestSchema, ChatResponseSchema, ChatHistorySchema
    print("✅ Schema imports OK")
except Exception as e:
    print(f"❌ Schema imports failed: {str(e)}")
    print(f"Error details: {repr(e)}")
    exit(1)

# Test 2: Import models
try:
    print("2. Testing model imports...")
    from app.models.lottery import LotteryTypeModel, LotteryDrawModel
    from app.models.user_query import UserQueryModel
    print("✅ Model imports OK")
except Exception as e:
    print(f"❌ Model imports failed: {str(e)}")
    print(f"Error details: {repr(e)}")
    exit(1)

# Test 3: Import services
try:
    print("3. Testing service imports...")
    from app.services.lottery import LotteryService
    from app.services.chatbot import ChatbotService
    print("✅ Service imports OK")
except Exception as e:
    print(f"❌ Service imports failed: {str(e)}")
    print(f"Error details: {repr(e)}")
    exit(1)

# Test 4: Test schema instantiation
try:
    print("4. Testing schema instantiation...")
    lottery_type = LotteryTypeSchema(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Xổ số miền Bắc"
    )
    print(f"✅ Schema instantiation OK: {lottery_type.name}")
except Exception as e:
    print(f"❌ Schema instantiation failed: {str(e)}")
    exit(1)

# Test 5: Test complex imports
try:
    print("5. Testing complex imports...")
    from app.services.statistics import StatisticsService
    from app.schemas.statistics import StatisticsResponseSchema
    print("✅ Complex imports OK")
except Exception as e:
    print(f"❌ Complex imports failed: {str(e)}")
    print(f"Error details: {repr(e)}")
    exit(1)

print("\n🎉 All circular import tests passed!")
print("✅ RecursionError has been resolved!")
