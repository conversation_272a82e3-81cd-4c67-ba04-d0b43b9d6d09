#!/usr/bin/env python3
"""
Test main.py với debug
"""

print("=== TESTING MAIN.PY DEBUG ===")

try:
    print("1. Testing app.main import...")
    from app.main import app
    print("✅ app.main imports successfully!")
    print(f"App title: {app.title}")
    print(f"App version: {app.version}")
    
except RecursionError as e:
    print("❌ RecursionError in app.main")
    print(f"Error: {str(e)[:200]}...")
    import traceback
    traceback.print_exc()
    
except Exception as e:
    print(f"⚠️ Other error in app.main: {e}")
    import traceback
    traceback.print_exc()

print("=== TEST COMPLETE ===")
