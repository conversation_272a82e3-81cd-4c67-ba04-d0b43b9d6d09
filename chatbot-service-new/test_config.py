#!/usr/bin/env python3
"""
Test configuration để verify environment variables
"""

import os

print("=== TESTING CONFIGURATION ===")

# Test environment variables
env_vars = [
    "POSTGRES_DB",
    "POSTGRES_USER",
    "POSTGRES_PASSWORD",
    "POSTGRES_HOST",
    "POSTGRES_PORT",
    "DATABASE_POOL_SIZE",
    "DATABASE_MAX_OVERFLOW",
    "REDIS_HOST",
    "REDIS_PORT",
    "REDIS_POOL_SIZE",
    "LLM_CORE_HOST",
    "LLM_CORE_PORT",
    "VERSION",
    "ENVIRONMENT",
    "DEBUG",
    "LOG_LEVEL"
]

print("Environment Variables:")
for var in env_vars:
    value = os.getenv(var, "NOT SET")
    print(f"  {var}: {value}")

print("\n=== TESTING CONFIG IMPORT ===")

try:
    from app.core.config import settings
    print("✅ Config import successful")

    print(f"Database URL: {settings.database_url}")
    print(f"Database Pool Size: {settings.database_pool_size}")
    print(f"Database Max Overflow: {settings.database_max_overflow}")
    print(f"Redis URL: {settings.redis_url}")
    print(f"Redis Pool Size: {settings.redis_pool_size}")
    print(f"LLM Core URL: {settings.llm_core_url}")
    print(f"Version: {settings.version}")
    print(f"Environment: {settings.environment}")
    print(f"Debug: {settings.debug}")

except Exception as e:
    print(f"❌ Config import failed: {e}")
    import traceback
    traceback.print_exc()

print("\n=== CONFIG TEST COMPLETE ===")
