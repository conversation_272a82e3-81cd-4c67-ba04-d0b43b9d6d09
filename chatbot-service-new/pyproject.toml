[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "chatbot-service"
version = "2.0.0"
description = "XỔ SỐ TV Chatbot Service"
authors = [{name = "Development Team"}]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "sqlalchemy[asyncio]>=2.0.23",
    "asyncpg>=0.29.0",
    "redis[hiredis]>=5.0.1",
    "httpx>=0.25.2",
    "structlog>=23.2.0",
    "python-dotenv>=1.0.0",
]

# Development dependencies (install manually for local development)
# pip install pytest pytest-asyncio black isort mypy

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
