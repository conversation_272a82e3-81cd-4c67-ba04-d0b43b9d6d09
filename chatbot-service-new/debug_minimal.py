#!/usr/bin/env python3
"""
Debug minimal để tìm RecursionError
"""

print("=== MINIMAL DEBUG ===")

# Test 1: Import từng module một
modules_to_test = [
    "app.core.config",
    "app.core.logging", 
    "app.db.base",
    "app.db.session",
    "app.schemas.base",
    "app.schemas.lottery",
    "app.models.lottery",
    "app.repositories.lottery",
    "app.services.lottery",
    "app.api.v1.lottery",
    "app.main"
]

for module in modules_to_test:
    try:
        print(f"Testing {module}...", end=" ")
        __import__(module)
        print("✅")
    except RecursionError as e:
        print(f"❌ RECURSION ERROR in {module}")
        print(f"Error: {str(e)[:100]}...")
        break
    except Exception as e:
        print(f"⚠️ Other error: {str(e)[:50]}...")
        continue

print("=== DEBUG COMPLETE ===")
