#!/bin/bash

# Script khởi động cho Chatbot Service v2.0
set -e

echo "🚀 Đang khởi động Chatbot Service v2.0..."

# In thông tin môi trường
echo "Môi trường: ${ENVIRONMENT:-development}"
echo "Chế độ debug: ${DEBUG:-true}"
echo "Mức log: ${LOG_LEVEL:-INFO}"
echo "Phiên bản: ${VERSION:-2.0.0}"

# In thông tin kết nối
echo "Cơ sở dữ liệu: ${POSTGRES_HOST:-timescale}:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-xosotv}"
echo "Redis: ${REDIS_HOST:-redis}:${REDIS_PORT:-6379}"
echo "LLM Core: ${LLM_CORE_HOST:-llm-core}:${LLM_CORE_PORT:-8080}"

# Chờ các dịch vụ phụ thuộc (tù<PERSON> chọn)
if [ "${WAIT_FOR_DEPS:-true}" = "true" ]; then
    echo "⏳ Đang chờ các dịch vụ phụ thuộc..."

    # Chờ cơ sở dữ liệu
    echo "Kiểm tra kết nối cơ sở dữ liệu..."
    timeout 30 bash -c "until nc -z ${POSTGRES_HOST:-timescale} ${POSTGRES_PORT:-5432}; do sleep 1; done" || {
        echo "⚠️ Cơ sở dữ liệu chưa sẵn sàng, tiếp tục khởi động..."
    }

    # Chờ Redis
    echo "Kiểm tra kết nối Redis..."
    timeout 30 bash -c "until nc -z ${REDIS_HOST:-redis} ${REDIS_PORT:-6379}; do sleep 1; done" || {
        echo "⚠️ Redis chưa sẵn sàng, tiếp tục khởi động..."
    }

    echo "✅ Hoàn thành kiểm tra các dịch vụ phụ thuộc"
fi

# Xác định số lượng workers dựa trên môi trường
if [ "${ENVIRONMENT}" = "production" ]; then
    WORKERS=${WORKERS:-4}
    RELOAD=""
    echo "🏭 Chế độ production: ${WORKERS} workers"
else
    WORKERS=${WORKERS:-1}
    RELOAD="--reload"
    echo "🔧 Chế độ development: ${WORKERS} worker với reload"
fi

# Thiết lập host và port
HOST=${HOST:-0.0.0.0}
PORT=${PORT:-8000}

echo "🌐 Khởi động server tại ${HOST}:${PORT}"

# Chuyển log level sang chữ thường cho uvicorn
LOG_LEVEL_LOWER=$(echo "${LOG_LEVEL:-INFO}" | tr '[:upper:]' '[:lower:]')

# Validate log level
case "${LOG_LEVEL_LOWER}" in
    critical|error|warning|info|debug|trace)
        echo "📝 Mức log uvicorn: ${LOG_LEVEL_LOWER}"
        ;;
    *)
        echo "⚠️ Log level không hợp lệ '${LOG_LEVEL_LOWER}', sử dụng 'info'"
        LOG_LEVEL_LOWER="info"
        ;;
esac

# Khởi động ứng dụng với Pydantic v1 (đã fix RecursionError)
echo "🚀 Khởi động với Pydantic v1 - RecursionError đã được fix!"
exec uvicorn app.main:app \
    --host "${HOST}" \
    --port "${PORT}" \
    --workers "${WORKERS}" \
    ${RELOAD} \
    --log-level "${LOG_LEVEL_LOWER}" \
    --access-log
