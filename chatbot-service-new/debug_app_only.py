#!/usr/bin/env python3
"""
Debug chỉ import app
"""

print("=== DEBUG APP ONLY ===")

try:
    print("1. Testing basic imports...")
    import sys
    import os
    from typing import Dict, Any
    print("✅ Basic imports OK")
    
    print("2. Testing pydantic...")
    from pydantic import BaseModel, Field
    print("✅ Pydantic OK")
    
    print("3. Testing sqlalchemy...")
    from sqlalchemy import Column, Integer
    print("✅ SQLAlchemy OK")
    
    print("4. Testing fastapi...")
    from fastapi import FastAPI
    print("✅ FastAPI OK")
    
    print("5. Testing app.core.config...")
    from app.core.config import settings
    print("✅ Config OK")
    
    print("6. Testing app.main...")
    from app.main import app
    print("✅ App OK")
    
except RecursionError as e:
    print(f"❌ RECURSION ERROR: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"⚠️ Other error: {e}")
    import traceback
    traceback.print_exc()

print("=== DEBUG COMPLETE ===")
