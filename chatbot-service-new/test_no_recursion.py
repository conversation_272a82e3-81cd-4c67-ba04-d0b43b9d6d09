#!/usr/bin/env python3
"""
Script test để verify KHÔNG có RecursionError
"""

import sys
import traceback
from typing import Any, Dict, List


def test_import(module_name: str, description: str) -> Dict[str, Any]:
    """Test import một module"""
    try:
        __import__(module_name)
        return {
            "module": module_name,
            "description": description,
            "status": "✅ SUCCESS",
            "error": None
        }
    except RecursionError as e:
        return {
            "module": module_name,
            "description": description,
            "status": "❌ RECURSION ERROR",
            "error": str(e)
        }
    except Exception as e:
        return {
            "module": module_name,
            "description": description,
            "status": "⚠️ OTHER ERROR",
            "error": str(e)
        }


def main():
    """Main test function"""
    print("=" * 80)
    print("🧪 TESTING CHATBOT SERVICE - NO RECURSION ERROR")
    print("=" * 80)
    
    # Test cases
    test_cases = [
        ("app.core.config", "Core configuration"),
        ("app.core.logging", "Logging setup"),
        ("app.db.base", "Database base"),
        ("app.db.session", "Database session"),
        ("app.schemas.base", "Base schemas"),
        ("app.schemas.lottery", "Lottery schemas"),
        ("app.schemas.chat", "Chat schemas"),
        ("app.models.lottery", "Lottery models"),
        ("app.models.user_query", "User query models"),
        ("app.repositories.base", "Base repository"),
        ("app.repositories.lottery", "Lottery repositories"),
        ("app.services.lottery", "Lottery service"),
        ("app.api.deps", "API dependencies"),
        ("app.api.v1.lottery", "Lottery API"),
        ("app.api.v1.router", "API router"),
        ("app.main", "Main application"),
    ]
    
    results: List[Dict[str, Any]] = []
    success_count = 0
    recursion_error_count = 0
    other_error_count = 0
    
    # Run tests
    for module_name, description in test_cases:
        print(f"\n🔍 Testing: {description}")
        result = test_import(module_name, description)
        results.append(result)
        
        print(f"   {result['status']} {module_name}")
        if result['error']:
            print(f"   Error: {result['error']}")
        
        # Count results
        if result['status'] == "✅ SUCCESS":
            success_count += 1
        elif result['status'] == "❌ RECURSION ERROR":
            recursion_error_count += 1
        else:
            other_error_count += 1
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Successful imports: {success_count}")
    print(f"❌ RecursionError: {recursion_error_count}")
    print(f"⚠️ Other errors: {other_error_count}")
    print(f"📈 Total tests: {len(test_cases)}")
    
    # Test schema instantiation
    print("\n🧪 TESTING SCHEMA INSTANTIATION")
    print("-" * 40)
    
    try:
        from app.schemas.lottery import LotteryType, LotteryResult
        
        # Test LotteryType
        lottery_type = LotteryType(
            id=1,
            code="mb",
            name="Miền Bắc",
            region="Bắc",
            description="Test lottery type"
        )
        print(f"✅ LotteryType created: {lottery_type.name}")
        
        # Test LotteryResult
        lottery_result = LotteryResult(
            id=1,
            lottery_type="Miền Bắc",
            lottery_code="mb",
            region="Bắc",
            draw_date="2024-01-01",
            results={"special": "12345"},
            formatted_results="Giải đặc biệt: 12345"
        )
        print(f"✅ LotteryResult created: {lottery_result.lottery_type}")
        
    except RecursionError:
        print("❌ RecursionError in schema instantiation")
        recursion_error_count += 1
    except Exception as e:
        print(f"⚠️ Error in schema instantiation: {e}")
        other_error_count += 1
    
    # Final result
    print("\n" + "=" * 80)
    if recursion_error_count == 0:
        print("🎉 SUCCESS: NO RECURSION ERRORS DETECTED!")
        print("✅ All imports work correctly")
        print("✅ Schemas can be instantiated")
        print("✅ Architecture is clean and professional")
        exit_code = 0
    else:
        print("💥 FAILURE: RECURSION ERRORS DETECTED!")
        print(f"❌ {recursion_error_count} modules have RecursionError")
        exit_code = 1
    
    print("=" * 80)
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
