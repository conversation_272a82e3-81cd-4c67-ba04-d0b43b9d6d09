"""
Lottery service - Business logic cho xổ số

QUAN TRỌNG: Service chỉ import schemas và repositories, KHÔNG import models
"""

from datetime import date, datetime, timedelta
from typing import List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import LoggerMixin
from app.repositories.lottery import LotteryDrawRepository, LotteryTypeRepository
from app.schemas.lottery import (
    LotteryResult,
    LotteryResultsResponse,
    LotteryType,
    LotteryTypeCreate,
    LotteryTypesResponse,
)


class LotteryService(LoggerMixin):
    """
    Service xử lý business logic cho xổ số
    """

    def __init__(self, db: AsyncSession):
        self.db = db
        self.lottery_type_repo = LotteryTypeRepository(db)
        self.lottery_draw_repo = LotteryDrawRepository(db)

    async def get_lottery_types(self) -> LotteryTypesResponse:
        """<PERSON><PERSON><PERSON> da<PERSON> sách loại xổ số"""
        self.logger.info("Getting lottery types")
        
        # Lấy từ repository
        types_models = await self.lottery_type_repo.get_all_active()
        
        # Convert models sang schemas
        types = [
            LotteryType(
                id=model.id,
                code=model.code,
                name=model.name,
                region=model.region,
                description=model.description,
            )
            for model in types_models
        ]
        
        self.logger.info("Retrieved lottery types", count=len(types))
        return LotteryTypesResponse(types=types)

    async def get_lottery_type_by_code(self, code: str) -> Optional[LotteryType]:
        """Lấy loại xổ số theo mã"""
        self.logger.info("Getting lottery type by code", code=code)
        
        model = await self.lottery_type_repo.get_by_code(code)
        if not model:
            return None
        
        return LotteryType(
            id=model.id,
            code=model.code,
            name=model.name,
            region=model.region,
            description=model.description,
        )

    async def create_lottery_type(self, data: LotteryTypeCreate) -> LotteryType:
        """Tạo loại xổ số mới"""
        self.logger.info("Creating lottery type", code=data.code)
        
        # Kiểm tra trùng mã
        existing = await self.lottery_type_repo.get_by_code(data.code)
        if existing:
            raise ValueError(f"Lottery type with code {data.code} already exists")
        
        # Tạo mới
        model = await self.lottery_type_repo.create(obj_in=data.model_dump())
        
        return LotteryType(
            id=model.id,
            code=model.code,
            name=model.name,
            region=model.region,
            description=model.description,
        )

    async def get_lottery_results_by_date(
        self, 
        query_date: date, 
        type_code: Optional[str] = None
    ) -> LotteryResultsResponse:
        """Lấy kết quả xổ số theo ngày"""
        self.logger.info(
            "Getting lottery results by date", 
            date=query_date.isoformat(),
            type_code=type_code
        )
        
        if type_code:
            # Lấy theo loại cụ thể
            lottery_type = await self.get_lottery_type_by_code(type_code)
            if not lottery_type:
                return LotteryResultsResponse(results=[], count=0)
            
            draw_model = await self.lottery_draw_repo.get_by_date_and_type(
                query_date, lottery_type.id
            )
            draws = [draw_model] if draw_model else []
        else:
            # Lấy tất cả loại
            draws = await self.lottery_draw_repo.get_by_date(query_date)
        
        # Convert sang schemas
        results = []
        for draw in draws:
            if draw and draw.lottery_type:
                result = LotteryResult(
                    id=draw.id,
                    lottery_type=draw.lottery_type.name,
                    lottery_code=draw.lottery_type.code,
                    region=draw.lottery_type.region,
                    draw_date=draw.draw_date,
                    results=draw.results,
                    formatted_results=self._format_results(draw.results),
                )
                results.append(result)
        
        self.logger.info("Retrieved lottery results", count=len(results))
        return LotteryResultsResponse(results=results, count=len(results))

    async def get_latest_lottery_results(
        self, 
        type_code: Optional[str] = None, 
        limit: int = 10
    ) -> LotteryResultsResponse:
        """Lấy kết quả xổ số mới nhất"""
        self.logger.info(
            "Getting latest lottery results", 
            type_code=type_code, 
            limit=limit
        )
        
        lottery_type_id = None
        if type_code:
            lottery_type = await self.get_lottery_type_by_code(type_code)
            if not lottery_type:
                return LotteryResultsResponse(results=[], count=0)
            lottery_type_id = lottery_type.id
        
        draws = await self.lottery_draw_repo.get_latest(lottery_type_id, limit)
        
        # Convert sang schemas
        results = []
        for draw in draws:
            if draw.lottery_type:
                result = LotteryResult(
                    id=draw.id,
                    lottery_type=draw.lottery_type.name,
                    lottery_code=draw.lottery_type.code,
                    region=draw.lottery_type.region,
                    draw_date=draw.draw_date,
                    results=draw.results,
                    formatted_results=self._format_results(draw.results),
                )
                results.append(result)
        
        self.logger.info("Retrieved latest lottery results", count=len(results))
        return LotteryResultsResponse(results=results, count=len(results))

    async def get_today_results(
        self, type_code: Optional[str] = None
    ) -> LotteryResultsResponse:
        """Lấy kết quả hôm nay"""
        today = date.today()
        return await self.get_lottery_results_by_date(today, type_code)

    async def get_yesterday_results(
        self, type_code: Optional[str] = None
    ) -> LotteryResultsResponse:
        """Lấy kết quả hôm qua"""
        yesterday = date.today() - timedelta(days=1)
        return await self.get_lottery_results_by_date(yesterday, type_code)

    def _format_results(self, results: dict) -> str:
        """Format kết quả xổ số thành string dễ đọc"""
        if not results:
            return ""
        
        formatted = []
        for prize, numbers in results.items():
            if isinstance(numbers, list):
                numbers_str = ", ".join(str(num) for num in numbers)
            else:
                numbers_str = str(numbers)
            formatted.append(f"{prize}: {numbers_str}")
        
        return " | ".join(formatted)
