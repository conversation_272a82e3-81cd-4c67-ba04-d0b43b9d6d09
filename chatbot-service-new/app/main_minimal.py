"""
Minimal main.py để test RecursionError - Step 3: Add schemas
"""

from fastapi import FastAPI

# Test thêm config và logging
from app.core.config import settings
from app.core.logging import get_logger, setup_logging

# Test thêm database models
from app.db.base import Base
from app.models.lottery import LotteryType, LotteryDraw
from app.models.user_query import UserQuery

# Test thêm schemas - STEP 3.2: ADD LOTTERY SCHEMAS
# Test base schemas
from app.schemas.base import BaseSchema, PaginatedResponse

# Test lottery schemas - TỪNG SCHEMA MỘT
# Test chỉ LotteryType trước (không phải LotteryTypeSchema)
from app.schemas.lottery import LotteryType

# COMMENT OUT các schemas khác để test
# from app.schemas.lottery import (
#     LotteryResultResponseSchema,
#     LotteryTypesResponseSchema,
#     LotteryResultsResponseSchema
# )

# COMMENT OUT chat schemas để test
# from app.schemas.chat import ChatRequestSchema, ChatResponseSchema

# Setup logging
setup_logging()
logger = get_logger(__name__)

# Tạo app với config
app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    description="Step 3.2.1: Testing LotteryTypeSchema only"
)

@app.get("/")
async def root():
    logger.info("Root endpoint called")
    return {
        "message": "Step 3.2.1: LotteryTypeSchema only!",
        "status": "working",
        "version": settings.version,
        "environment": settings.environment,
        "models": ["LotteryType", "LotteryDraw", "UserQuery"],
        "schemas": ["BaseSchema", "PaginatedResponse", "LotteryType"]
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "version": settings.version,
        "step": "3.2.1 - LotteryTypeSchema only",
        "lottery_type_schema_loaded": True
    }

@app.get("/models")
async def models_info():
    return {
        "lottery_type_table": LotteryType.__tablename__,
        "lottery_draw_table": LotteryDraw.__tablename__,
        "user_query_table": UserQuery.__tablename__,
        "base_metadata_tables": len(Base.metadata.tables)
    }

@app.get("/schemas")
async def schemas_test():
    # Test tạo chỉ LotteryTypeSchema
    try:
        # Test LotteryType
        lottery_type = LotteryType(
            id=1,
            code="mb",
            name="Miền Bắc",
            region="Bắc",
            description="Test lottery type schema"
        )

        return {
            "lottery_type": lottery_type.model_dump(),
            "lottery_type_schema_working": True
        }
    except Exception as e:
        return {
            "error": str(e),
            "lottery_type_schema_working": False
        }

logger.info("Step 3.2.1: App created với LotteryTypeSchema only")
