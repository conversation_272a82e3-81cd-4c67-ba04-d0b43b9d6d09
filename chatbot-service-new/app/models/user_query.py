"""
User query models

QUAN TRỌNG: KHÔNG import schemas để tránh circular imports
"""

from typing import Any, Dict, Optional

from sqlalchemy import Integer, String, Text
from sqlalchemy.orm import Mapped, mapped_column

from app.db.base import Base, TimestampMixin


class UserQuery(Base, TimestampMixin):
    """Model cho query của user"""
    
    __tablename__ = "user_queries"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[str] = mapped_column(
        String(100), nullable=False, index=True, comment="ID người dùng"
    )
    session_id: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True, index=True, comment="ID session"
    )
    query_text: Mapped[str] = mapped_column(
        Text, nullable=False, comment="Nội dung câu hỏi"
    )
    response_text: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, comment="Nội dung phản hồi"
    )
    intent: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True, comment="Ý định được phát hiện"
    )
    confidence: Mapped[Optional[float]] = mapped_column(
        nullable=True, comment="Độ tin cậy"
    )
    processing_time_ms: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, comment="Thời gian xử lý (ms)"
    )
    
    def __repr__(self) -> str:
        return (
            f"<UserQuery(id={self.id}, user_id={self.user_id!r}, "
            f"intent={self.intent!r})>"
        )
