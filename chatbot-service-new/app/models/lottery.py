"""
Lottery models với SQLAlchemy 2.0

QUAN TRỌNG: KHÔNG import schemas để tránh circular imports
"""

from datetime import date
from typing import Any, Dict, List, Optional

from sqlalchemy import Date, ForeignKey, Integer, JSON, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base import Base, TimestampMixin


class LotteryType(Base, TimestampMixin):
    """Model cho loại xổ số"""
    
    __tablename__ = "lottery_types"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    code: Mapped[str] = mapped_column(
        String(10), unique=True, nullable=False, index=True, comment="Mã loại xổ số"
    )
    name: Mapped[str] = mapped_column(
        String(100), nullable=False, comment="Tên loại xổ số"
    )
    region: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True, comment="Khu vực"
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, comment="Mô tả"
    )
    
    # Relationships - sử dụng lazy loading để tránh circular references
    draws: Mapped[List["LotteryDraw"]] = relationship(
        "LotteryDraw", 
        back_populates="lottery_type",
        lazy="select",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<LotteryType(id={self.id}, code={self.code!r}, name={self.name!r})>"


class LotteryDraw(Base, TimestampMixin):
    """Model cho kết quả xổ số"""
    
    __tablename__ = "lottery_draws"
    __table_args__ = (
        UniqueConstraint(
            "lottery_type_id", 
            "draw_date", 
            name="uq_lottery_draw_type_date"
        ),
    )
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    lottery_type_id: Mapped[int] = mapped_column(
        Integer, 
        ForeignKey("lottery_types.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="ID loại xổ số"
    )
    draw_date: Mapped[date] = mapped_column(
        Date, nullable=False, index=True, comment="Ngày quay thưởng"
    )
    results: Mapped[Dict[str, Any]] = mapped_column(
        JSON, nullable=False, comment="Kết quả xổ số dạng JSON"
    )
    
    # Relationships
    lottery_type: Mapped["LotteryType"] = relationship(
        "LotteryType", 
        back_populates="draws",
        lazy="select"
    )
    
    def __repr__(self) -> str:
        return (
            f"<LotteryDraw(id={self.id}, type_id={self.lottery_type_id}, "
            f"date={self.draw_date})>"
        )
