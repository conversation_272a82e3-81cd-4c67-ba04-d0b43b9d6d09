"""
Database session management
"""

from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator

from sqlalchemy.ext.asyncio import (
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

# Tạo async engine
engine = create_async_engine(
    str(settings.database_url),
    pool_size=settings.database_pool_size,
    max_overflow=settings.database_max_overflow,
    pool_pre_ping=True,
    echo=settings.debug,
)

# Tạo session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency để lấy database session
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error("Database session error", error=str(e))
            raise
        finally:
            await session.close()


@asynccontextmanager
async def get_db_context() -> AsyncGenerator[AsyncSession, None]:
    """
    Context manager để lấy database session
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error("Database context error", error=str(e))
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """
    Khởi tạo database
    """
    from app.db.base import Base

    async with engine.begin() as conn:
        # Import trực tiếp models để đảm bảo chúng được đăng ký
        from app.models.lottery import LotteryType, LotteryDraw  # noqa: F401
        from app.models.user_query import UserQuery  # noqa: F401

        # Tạo tables
        await conn.run_sync(Base.metadata.create_all)
        logger.info("Database initialized successfully")


async def close_db() -> None:
    """
    Đóng database connections
    """
    await engine.dispose()
    logger.info("Database connections closed")
