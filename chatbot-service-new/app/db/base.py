"""
SQLAlchemy base classes và utilities
"""

from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy import DateTime, func
from sqlalchemy.ext.asyncio import AsyncAttrs
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(AsyncAttrs, DeclarativeBase):
    """
    Base class cho tất cả SQLAlchemy models
    """

    # Tự động tạo __tablename__ từ class name
    __abstract__ = True

    def __repr__(self) -> str:
        """String representation của model"""
        attrs = []
        for column in self.__table__.columns:
            value = getattr(self, column.name, None)
            attrs.append(f"{column.name}={value!r}")
        return f"{self.__class__.__name__}({', '.join(attrs)})"

    def to_dict(self) -> Dict[str, Any]:
        """Chuyển model thành dictionary"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }


class TimestampMixin:
    """
    Mixin để thêm timestamp fields
    """

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Thời gian tạo",
    )

    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now(),
        nullable=True,
        comment="Thời gian cập nhật cuối",
    )
