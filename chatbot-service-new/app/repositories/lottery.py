"""
Lottery repositories

QUAN TRỌNG: KHÔNG import schemas để tránh circular imports
"""

from datetime import date
from typing import List, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.lottery import LotteryDraw, LotteryType
from app.repositories.base import BaseRepository


class LotteryTypeRepository(BaseRepository[LotteryType]):
    """Repository cho LotteryType"""

    def __init__(self, db: AsyncSession):
        super().__init__(LotteryType, db)

    async def get_by_code(self, code: str) -> Optional[LotteryType]:
        """Lấy loại xổ số theo mã"""
        result = await self.db.execute(
            select(LotteryType).where(LotteryType.code == code)
        )
        return result.scalar_one_or_none()

    async def get_all_active(self) -> List[LotteryType]:
        """<PERSON><PERSON><PERSON> tất cả loại xổ số đang hoạt động"""
        result = await self.db.execute(
            select(LotteryType).order_by(LotteryType.name)
        )
        return list(result.scalars().all())


class LotteryDrawRepository(BaseRepository[LotteryDraw]):
    """Repository cho LotteryDraw"""

    def __init__(self, db: AsyncSession):
        super().__init__(LotteryDraw, db)

    async def get_by_date_and_type(
        self, 
        draw_date: date, 
        lottery_type_id: int
    ) -> Optional[LotteryDraw]:
        """Lấy kết quả theo ngày và loại xổ số"""
        result = await self.db.execute(
            select(LotteryDraw)
            .where(
                LotteryDraw.draw_date == draw_date,
                LotteryDraw.lottery_type_id == lottery_type_id
            )
            .options(selectinload(LotteryDraw.lottery_type))
        )
        return result.scalar_one_or_none()

    async def get_by_date(self, draw_date: date) -> List[LotteryDraw]:
        """Lấy tất cả kết quả theo ngày"""
        result = await self.db.execute(
            select(LotteryDraw)
            .where(LotteryDraw.draw_date == draw_date)
            .options(selectinload(LotteryDraw.lottery_type))
            .order_by(LotteryDraw.lottery_type_id)
        )
        return list(result.scalars().all())

    async def get_latest(
        self, 
        lottery_type_id: Optional[int] = None, 
        limit: int = 10
    ) -> List[LotteryDraw]:
        """Lấy kết quả mới nhất"""
        query = select(LotteryDraw).options(selectinload(LotteryDraw.lottery_type))
        
        if lottery_type_id:
            query = query.where(LotteryDraw.lottery_type_id == lottery_type_id)
        
        query = query.order_by(LotteryDraw.draw_date.desc()).limit(limit)
        
        result = await self.db.execute(query)
        return list(result.scalars().all())

    async def get_by_date_range(
        self,
        start_date: date,
        end_date: date,
        lottery_type_id: Optional[int] = None,
    ) -> List[LotteryDraw]:
        """Lấy kết quả trong khoảng thời gian"""
        query = (
            select(LotteryDraw)
            .where(
                LotteryDraw.draw_date >= start_date,
                LotteryDraw.draw_date <= end_date
            )
            .options(selectinload(LotteryDraw.lottery_type))
        )
        
        if lottery_type_id:
            query = query.where(LotteryDraw.lottery_type_id == lottery_type_id)
        
        query = query.order_by(LotteryDraw.draw_date.desc())
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
