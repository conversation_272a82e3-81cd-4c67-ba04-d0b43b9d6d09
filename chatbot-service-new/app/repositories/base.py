"""
Base repository với generic CRUD operations
"""

from typing import Any, Dict, Generic, List, Optional, Type, TypeVar

from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import LoggerMixin
from app.db.base import Base

ModelType = TypeVar("ModelType", bound=Base)


class BaseRepository(Generic[ModelType], LoggerMixin):
    """
    Base repository với CRUD operations
    """

    def __init__(self, model: Type[ModelType], db: AsyncSession):
        self.model = model
        self.db = db

    async def get(self, id: Any) -> Optional[ModelType]:
        """Lấy record theo ID"""
        result = await self.db.execute(
            select(self.model).where(self.model.id == id)
        )
        return result.scalar_one_or_none()

    async def get_multi(
        self, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        **filters: Any
    ) -> List[ModelType]:
        """<PERSON><PERSON><PERSON> <PERSON> records với pagination và filters"""
        query = select(self.model)
        
        # Áp dụng filters
        for field, value in filters.items():
            if hasattr(self.model, field) and value is not None:
                query = query.where(getattr(self.model, field) == value)
        
        query = query.offset(skip).limit(limit)
        result = await self.db.execute(query)
        return list(result.scalars().all())

    async def create(self, *, obj_in: Dict[str, Any]) -> ModelType:
        """Tạo record mới"""
        db_obj = self.model(**obj_in)
        self.db.add(db_obj)
        await self.db.commit()
        await self.db.refresh(db_obj)
        
        self.logger.info(
            "Created record",
            model=self.model.__name__,
            id=db_obj.id,
        )
        return db_obj

    async def update(
        self, 
        *, 
        db_obj: ModelType, 
        obj_in: Dict[str, Any]
    ) -> ModelType:
        """Cập nhật record"""
        # Lọc bỏ các field None
        update_data = {k: v for k, v in obj_in.items() if v is not None}
        
        if update_data:
            await self.db.execute(
                update(self.model)
                .where(self.model.id == db_obj.id)
                .values(**update_data)
            )
            await self.db.commit()
            await self.db.refresh(db_obj)
            
            self.logger.info(
                "Updated record",
                model=self.model.__name__,
                id=db_obj.id,
                fields=list(update_data.keys()),
            )
        
        return db_obj

    async def delete(self, *, id: Any) -> bool:
        """Xóa record theo ID"""
        result = await self.db.execute(
            delete(self.model).where(self.model.id == id)
        )
        await self.db.commit()
        
        deleted = result.rowcount > 0
        if deleted:
            self.logger.info(
                "Deleted record",
                model=self.model.__name__,
                id=id,
            )
        
        return deleted

    async def count(self, **filters: Any) -> int:
        """Đếm số lượng records"""
        from sqlalchemy import func
        
        query = select(func.count(self.model.id))
        
        # Áp dụng filters
        for field, value in filters.items():
            if hasattr(self.model, field) and value is not None:
                query = query.where(getattr(self.model, field) == value)
        
        result = await self.db.execute(query)
        return result.scalar() or 0

    async def exists(self, **filters: Any) -> bool:
        """Kiểm tra record có tồn tại không"""
        query = select(self.model.id)
        
        # Áp dụng filters
        for field, value in filters.items():
            if hasattr(self.model, field) and value is not None:
                query = query.where(getattr(self.model, field) == value)
        
        query = query.limit(1)
        result = await self.db.execute(query)
        return result.scalar() is not None
