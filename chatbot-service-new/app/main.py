"""
FastAPI application

QUAN TRỌNG: Main app KHÔNG import models để tránh circular imports
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from app.api.v1.router import api_router
from app.core.config import settings
from app.core.logging import get_logger, setup_logging
from app.db.session import close_db, init_db

# Setup logging trước khi import bất cứ thứ gì khác
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan events
    """
    # Startup
    logger.info("Starting up application", version=settings.version)

    try:
        # Initialize database
        await init_db()
        logger.info("Database initialized successfully")

        yield

    finally:
        # Shutdown
        logger.info("Shutting down application")
        await close_db()
        logger.info("Application shutdown complete")


def create_app() -> FastAPI:
    """
    Create FastAPI application
    """
    app = FastAPI(
        title=settings.app_name,
        version=settings.version,
        description="XỔ SỐ TV Chatbot Service - Kiến trúc chuyên nghiệp với zero circular imports",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan,
    )

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Trusted host middleware (security)
    if settings.is_production:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", "*.yourdomain.com"],
        )

    # Include routers
    app.include_router(api_router)

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "version": settings.version,
            "environment": settings.environment,
        }

    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint"""
        return {
            "message": "XỔ SỐ TV Chatbot Service",
            "version": settings.version,
            "docs": "/docs" if settings.debug else "Documentation disabled in production",
        }

    logger.info(
        "FastAPI application created",
        debug=settings.debug,
        environment=settings.environment,
    )

    return app


# Create app instance
app = create_app()
