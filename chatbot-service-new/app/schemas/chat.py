"""
Chat schemas - THIẾT KẾ TRÁNH RECURSION ERROR
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import Field

from app.schemas.base import BaseSchema


# =============================================================================
# BASE SCHEMAS
# =============================================================================

class ChatMessageBase(BaseSchema):
    """Base schema cho chat message"""
    
    content: str = Field(..., min_length=1, max_length=10000, description="Nội dung tin nhắn")
    role: str = Field(..., description="Vai trò: user, assistant, system")


class ChatSessionBase(BaseSchema):
    """Base schema cho chat session"""
    
    user_id: str = Field(..., description="ID người dùng")
    session_name: Optional[str] = Field(None, description="Tên session")


# =============================================================================
# REQUEST SCHEMAS
# =============================================================================

class ChatRequest(BaseSchema):
    """Request cho chat"""
    
    message: str = Field(..., min_length=1, max_length=10000, description="Tin nhắn từ user")
    user_id: str = Field(..., description="ID người dùng")
    session_id: Optional[str] = Field(None, description="ID session")
    context: Optional[Dict[str, Any]] = Field(None, description="Context bổ sung")


class ChatSessionCreate(ChatSessionBase):
    """Schema tạo chat session mới"""
    pass


# =============================================================================
# RESPONSE SCHEMAS
# =============================================================================

class ChatMessage(ChatMessageBase):
    """Schema response cho chat message"""
    
    id: int = Field(..., description="ID tin nhắn")
    timestamp: datetime = Field(..., description="Thời gian gửi")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata bổ sung")


class ChatResponse(BaseSchema):
    """Response cho chat"""
    
    message: str = Field(..., description="Phản hồi từ chatbot")
    timestamp: datetime = Field(..., description="Thời gian phản hồi")
    intent: Optional[str] = Field(None, description="Ý định được phát hiện")
    confidence: Optional[float] = Field(None, ge=0, le=1, description="Độ tin cậy")
    context: Optional[Dict[str, Any]] = Field(None, description="Context")
    session_id: str = Field(..., description="ID session")


class ChatSession(ChatSessionBase):
    """Schema response cho chat session"""
    
    id: str = Field(..., description="ID session")
    created_at: datetime = Field(..., description="Thời gian tạo")
    last_activity: Optional[datetime] = Field(None, description="Hoạt động cuối")
    message_count: int = Field(default=0, description="Số lượng tin nhắn")


class ChatHistory(BaseSchema):
    """Schema cho lịch sử chat"""
    
    messages: List[ChatMessage] = Field(..., description="Danh sách tin nhắn")
    session: ChatSession = Field(..., description="Thông tin session")
    total_messages: int = Field(..., description="Tổng số tin nhắn")


# =============================================================================
# QUERY PARAMETERS
# =============================================================================

class ChatHistoryParams(BaseSchema):
    """Parameters cho query lịch sử chat"""
    
    user_id: str = Field(..., description="ID người dùng")
    session_id: Optional[str] = Field(None, description="ID session")
    limit: int = Field(default=50, ge=1, le=200, description="Số lượng tin nhắn")
    before: Optional[datetime] = Field(None, description="Lấy tin nhắn trước thời điểm này")
