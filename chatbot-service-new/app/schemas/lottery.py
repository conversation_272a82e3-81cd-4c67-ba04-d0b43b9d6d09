"""
Lottery schemas - Pydantic v2 compliant

Nguyên tắc Pydantic v2:
1. Sử dụng ConfigDict thay vì Config class
2. Tránh circular imports bằng cách định nghĩa schemas theo thứ tự dependency
3. Sử dụng model_rebuild() cho forward references nếu cần
4. Generic types được handle cẩn thận
"""

from __future__ import annotations  # Enable forward references

from datetime import date
from typing import Any, Dict, List, Optional

from pydantic import ConfigDict, Field

from app.schemas.base import BaseSchema


# =============================================================================
# BASE SCHEMAS - Định nghĩa trước, không dependencies
# =============================================================================

class LotteryTypeBase(BaseSchema):
    """Base schema cho loại xổ số"""

    code: str = Field(..., min_length=2, max_length=10, description="Mã loại xổ số")
    name: str = Field(..., min_length=1, max_length=100, description="Tên loại xổ số")
    region: Optional[str] = Field(None, max_length=50, description="Khu vực")
    description: Optional[str] = Field(None, max_length=500, description="Mô tả")


class LotteryDrawBase(BaseSchema):
    """Base schema cho kết quả xổ số"""

    lottery_type_id: int = Field(..., gt=0, description="ID loại xổ số")
    draw_date: date = Field(..., description="Ngày quay thưởng")
    results: Dict[str, Any] = Field(..., description="Kết quả xổ số dạng JSON")


# =============================================================================
# CREATE/UPDATE SCHEMAS
# =============================================================================

class LotteryTypeCreate(LotteryTypeBase):
    """Schema tạo loại xổ số mới"""
    pass


class LotteryTypeUpdate(BaseSchema):
    """Schema cập nhật loại xổ số"""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    region: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = Field(None, max_length=500)


class LotteryDrawCreate(LotteryDrawBase):
    """Schema tạo kết quả xổ số mới"""
    pass


class LotteryDrawUpdate(BaseSchema):
    """Schema cập nhật kết quả xổ số"""

    results: Optional[Dict[str, Any]] = Field(None, description="Kết quả xổ số")


# =============================================================================
# RESPONSE SCHEMAS - Định nghĩa sau, có thể reference base schemas
# =============================================================================

class LotteryType(LotteryTypeBase):
    """Schema response cho loại xổ số"""

    id: int = Field(..., description="ID loại xổ số")

    model_config = ConfigDict(from_attributes=True)


class LotteryDraw(LotteryDrawBase):
    """Schema response cho kết quả xổ số"""

    id: int = Field(..., description="ID kết quả xổ số")

    model_config = ConfigDict(from_attributes=True)


class LotteryResult(BaseSchema):
    """Schema response cho kết quả xổ số với thông tin đầy đủ"""

    id: int = Field(..., description="ID kết quả xổ số")
    lottery_type: str = Field(..., description="Tên loại xổ số")
    lottery_code: str = Field(..., description="Mã loại xổ số")
    region: Optional[str] = Field(None, description="Khu vực")
    draw_date: date = Field(..., description="Ngày quay thưởng")
    results: Dict[str, Any] = Field(..., description="Kết quả xổ số")
    formatted_results: str = Field(..., description="Kết quả đã định dạng")


# =============================================================================
# QUERY PARAMETERS
# =============================================================================

class LotteryQueryParams(BaseSchema):
    """Parameters cho query kết quả xổ số"""

    date: Optional[date] = Field(None, description="Ngày quay thưởng")
    type_code: Optional[str] = Field(None, description="Mã loại xổ số")
    limit: int = Field(default=10, ge=1, le=100, description="Số lượng kết quả")


# =============================================================================
# RESPONSE WRAPPERS - Định nghĩa cuối cùng
# =============================================================================

class LotteryTypesResponse(BaseSchema):
    """Response cho danh sách loại xổ số"""

    types: List[LotteryType] = Field(..., description="Danh sách loại xổ số")


class LotteryResultsResponse(BaseSchema):
    """Response cho danh sách kết quả xổ số"""

    results: List[LotteryResult] = Field(..., description="Danh sách kết quả")
    count: int = Field(..., description="Số lượng kết quả")


# =============================================================================
# PAGINATION RESPONSES - Sử dụng concrete classes thay vì Generic aliases
# =============================================================================

class LotteryTypesPaginated(BaseSchema):
    """Paginated response cho lottery types"""
    items: List[LotteryType] = Field(..., description="Danh sách loại xổ số")
    total: int = Field(..., description="Tổng số items")
    page: int = Field(..., description="Trang hiện tại")
    size: int = Field(..., description="Số items per page")
    pages: int = Field(..., description="Tổng số trang")


class LotteryResultsPaginated(BaseSchema):
    """Paginated response cho lottery results"""
    items: List[LotteryResult] = Field(..., description="Danh sách kết quả")
    total: int = Field(..., description="Tổng số items")
    page: int = Field(..., description="Trang hiện tại")
    size: int = Field(..., description="Số items per page")
    pages: int = Field(..., description="Tổng số trang")
