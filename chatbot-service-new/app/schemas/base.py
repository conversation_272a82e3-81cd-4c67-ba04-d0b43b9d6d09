"""
Base schemas và common types
"""

from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, ConfigDict, Field

# Type variables
T = TypeVar("T")


class BaseSchema(BaseModel):
    """
    Base schema với cấu hình chung
    """

    model_config = ConfigDict(
        # Pydantic v2 config
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=False,
        str_strip_whitespace=True,
        extra="forbid",
        json_schema_extra={
            "examples": [],
        },
    )


class TimestampSchema(BaseSchema):
    """
    Schema với timestamp fields
    """

    created_at: datetime = Field(..., description="Thời gian tạo")
    updated_at: Optional[datetime] = Field(None, description="Thời gian cập nhật cuối")


class PaginationParams(BaseSchema):
    """
    Pagination parameters
    """

    page: int = Field(default=1, ge=1, description="Số trang")
    size: int = Field(default=20, ge=1, le=100, description="Số items per page")

    @property
    def offset(self) -> int:
        """Tính offset cho database query"""
        return (self.page - 1) * self.size


class PaginatedResponse(BaseSchema, Generic[T]):
    """
    Generic paginated response
    """

    items: List[T] = Field(..., description="Danh sách items")
    total: int = Field(..., description="Tổng số items")
    page: int = Field(..., description="Trang hiện tại")
    size: int = Field(..., description="Số items per page")
    pages: int = Field(..., description="Tổng số trang")

    @classmethod
    def create(
        cls,
        items: List[T],
        total: int,
        page: int,
        size: int,
    ) -> "PaginatedResponse[T]":
        """Factory method để tạo paginated response"""
        pages = (total + size - 1) // size  # Ceiling division
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages,
        )


class ErrorResponse(BaseSchema):
    """
    Standard error response
    """

    error: str = Field(..., description="Mã lỗi")
    message: str = Field(..., description="Thông báo lỗi")
    details: Optional[Dict[str, Any]] = Field(None, description="Chi tiết lỗi")


class SuccessResponse(BaseSchema):
    """
    Standard success response
    """

    success: bool = Field(True, description="Trạng thái thành công")
    message: str = Field(..., description="Thông báo")
    data: Optional[Dict[str, Any]] = Field(None, description="Dữ liệu trả về")
