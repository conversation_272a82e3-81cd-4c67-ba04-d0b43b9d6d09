"""
Structured logging với structlog
"""

import logging
import sys
from typing import Any, Dict

import structlog
from rich.console import <PERSON>sol<PERSON>
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from app.core.config import settings


def setup_logging() -> None:
    """
    Cấu hình structured logging
    """
    # Cấu hình structlog
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,
            structlog.processors.add_log_level,
            structlog.processors.StackInfoRenderer(),
            structlog.dev.set_exc_info,
            structlog.processors.TimeStamper(fmt="ISO"),
            structlog.processors.JSONRenderer()
            if settings.log_format == "json"
            else structlog.dev.ConsoleRenderer(colors=True),
        ],
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.log_level)
        ),
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )

    # C<PERSON><PERSON> hình standard logging
    if settings.log_format == "json":
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '{"timestamp": "%(asctime)s", "level": "%(levelname)s", '
            '"logger": "%(name)s", "message": "%(message)s"}'
        )
        handler.setFormatter(formatter)
    else:
        # Sử dụng Rich handler cho development
        console = Console(stderr=True)
        handler = RichHandler(
            console=console,
            show_time=True,
            show_level=True,
            show_path=True,
            markup=True,
            rich_tracebacks=True,
        )

    # Cấu hình root logger
    logging.basicConfig(
        level=getattr(logging, settings.log_level),
        handlers=[handler],
        format="%(message)s",
    )

    # Giảm log level cho các thư viện bên ngoài
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    logging.getLogger("asyncpg").setLevel(logging.WARNING)


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Lấy structured logger instance
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """
    Mixin class để thêm logger vào các class khác
    """

    @property
    def logger(self) -> structlog.BoundLogger:
        """Logger instance cho class"""
        return get_logger(self.__class__.__name__)


def log_context(**kwargs: Any) -> Dict[str, Any]:
    """
    Tạo logging context
    """
    return kwargs
