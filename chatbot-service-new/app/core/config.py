"""
Configuration management với Pydantic Settings v2
Đ<PERSON>c trực tiếp từ environment variables (không dùng .env file)
"""

import os
from functools import lru_cache
from typing import List, Literal

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Application settings với validation và type safety
    Đọc trực tiếp từ environment variables được set bởi Docker Compose
    """

    model_config = SettingsConfigDict(
        case_sensitive=False,
        extra="ignore",
    )

    # Application - đọc từ environment variables
    app_name: str = Field(default="XỔ SỐ TV Chatbot API", description="Tên ứng dụng")
    version: str = Field(default="2.0.0", description="Phiên bản ứng dụng")
    environment: str = Field(default="development", description="Môi trường triển khai")
    debug: bool = Field(default=True, description="Debug mode")
    log_level: str = Field(default="INFO", description="Log level")

    # Database - đọ<PERSON> từ POSTGRES_* environment variables
    postgres_db: str = Field(default="xosotv", description="Database name")
    postgres_user: str = Field(default="xosotv_user", description="Database user")
    postgres_password: str = Field(default="yourstrongpassword", description="Database password")
    postgres_host: str = Field(default="timescale", description="Database host")
    postgres_port: int = Field(default=5432, description="Database port")

    # Database pool settings
    database_pool_size: int = Field(default=20, ge=1, le=100, description="Database pool size")
    database_max_overflow: int = Field(default=30, ge=0, le=100, description="Database max overflow")

    # Redis - đọc từ REDIS_* environment variables
    redis_host: str = Field(default="redis", description="Redis host")
    redis_port: int = Field(default=6379, description="Redis port")
    redis_pool_size: int = Field(default=10, ge=1, le=50, description="Redis pool size")

    # LLM Core - đọc từ LLM_CORE_* environment variables
    llm_core_host: str = Field(default="llm-core", description="LLM Core host")
    llm_core_port: int = Field(default=8080, description="LLM Core port")
    llm_core_timeout: int = Field(default=30, ge=1, le=300)

    # Security
    secret_key: str = Field(
        default="your-super-secret-chatbot-key-change-in-production",
        min_length=32,
        description="Secret key"
    )
    access_token_expire_minutes: int = Field(default=30, ge=1, le=1440)

    # CORS
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080", "http://localhost:9000"],
        description="CORS allowed origins"
    )

    # Logging (đã có ở trên)
    log_format: Literal["json", "text"] = Field(default="json")

    # Cache TTL (seconds)
    cache_ttl_default: int = Field(default=300, ge=1)
    cache_ttl_lottery_types: int = Field(default=86400, ge=1)
    cache_ttl_lottery_results: int = Field(default=3600, ge=1)

    # Rate Limiting
    rate_limit_requests: int = Field(default=100, ge=1)
    rate_limit_window: int = Field(default=60, ge=1)

    @field_validator("cors_origins", mode="before")
    @classmethod
    def parse_cors_origins(cls, v: str | List[str]) -> List[str]:
        """Parse CORS origins từ string hoặc list"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v

    @property
    def database_url(self) -> str:
        """Tạo database URL từ các thành phần"""
        return f"postgresql+asyncpg://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"

    @property
    def redis_url(self) -> str:
        """Tạo Redis URL từ các thành phần"""
        return f"redis://{self.redis_host}:{self.redis_port}/0"

    @property
    def llm_core_url(self) -> str:
        """Tạo LLM Core URL từ các thành phần"""
        return f"http://{self.llm_core_host}:{self.llm_core_port}"

    @property
    def is_development(self) -> bool:
        """Kiểm tra có phải môi trường development không"""
        return self.environment == "development"

    @property
    def is_production(self) -> bool:
        """Kiểm tra có phải môi trường production không"""
        return self.environment == "production"


@lru_cache()
def get_settings() -> Settings:
    """
    Cached settings instance
    """
    return Settings()


# Global settings instance
settings = get_settings()
