"""
FastAPI dependencies
"""

from typing import AsyncGenerator

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db_session
from app.services.lottery import LotteryService


async def get_lottery_service(
    db: AsyncSession = Depends(get_db_session),
) -> LotteryService:
    """Dependency để lấy LotteryService"""
    return LotteryService(db)
