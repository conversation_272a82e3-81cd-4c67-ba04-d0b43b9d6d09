"""
Lottery API endpoints

QUAN TRỌNG: Chỉ import schemas và services, KHÔNG import models
"""

from datetime import date
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query

from app.api.deps import get_lottery_service
from app.schemas.lottery import (
    LotteryResultsResponse,
    LotteryType,
    LotteryTypeCreate,
    LotteryTypesResponse,
)
from app.services.lottery import LotteryService

router = APIRouter(prefix="/lottery", tags=["lottery"])


@router.get("/types", response_model=LotteryTypesResponse)
async def get_lottery_types(
    lottery_service: LotteryService = Depends(get_lottery_service),
) -> LotteryTypesResponse:
    """
    L<PERSON>y danh sách các loại xổ số
    """
    return await lottery_service.get_lottery_types()


@router.post("/types", response_model=LotteryType)
async def create_lottery_type(
    data: LotteryTypeCreate,
    lottery_service: LotteryService = Depends(get_lottery_service),
) -> LotteryType:
    """
    Tạo loại xổ số mới
    """
    try:
        return await lottery_service.create_lottery_type(data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/types/{code}", response_model=LotteryType)
async def get_lottery_type(
    code: str,
    lottery_service: LotteryService = Depends(get_lottery_service),
) -> LotteryType:
    """
    Lấy loại xổ số theo mã
    """
    lottery_type = await lottery_service.get_lottery_type_by_code(code)
    if not lottery_type:
        raise HTTPException(status_code=404, detail="Lottery type not found")
    return lottery_type


@router.get("/results", response_model=LotteryResultsResponse)
async def get_lottery_results(
    date: Optional[date] = Query(None, description="Ngày quay thưởng (YYYY-MM-DD)"),
    type_code: Optional[str] = Query(None, description="Mã loại xổ số"),
    limit: int = Query(10, ge=1, le=100, description="Số lượng kết quả tối đa"),
    lottery_service: LotteryService = Depends(get_lottery_service),
) -> LotteryResultsResponse:
    """
    Lấy kết quả xổ số
    
    - Nếu không cung cấp ngày, sẽ lấy kết quả mới nhất
    - Nếu không cung cấp loại, sẽ lấy tất cả các loại
    """
    if date:
        return await lottery_service.get_lottery_results_by_date(date, type_code)
    else:
        return await lottery_service.get_latest_lottery_results(type_code, limit)


@router.get("/results/latest", response_model=LotteryResultsResponse)
async def get_latest_lottery_results(
    type_code: Optional[str] = Query(None, description="Mã loại xổ số"),
    limit: int = Query(10, ge=1, le=100, description="Số lượng kết quả tối đa"),
    lottery_service: LotteryService = Depends(get_lottery_service),
) -> LotteryResultsResponse:
    """
    Lấy kết quả xổ số mới nhất
    """
    return await lottery_service.get_latest_lottery_results(type_code, limit)


@router.get("/results/today", response_model=LotteryResultsResponse)
async def get_today_lottery_results(
    type_code: Optional[str] = Query(None, description="Mã loại xổ số"),
    lottery_service: LotteryService = Depends(get_lottery_service),
) -> LotteryResultsResponse:
    """
    Lấy kết quả xổ số hôm nay
    """
    return await lottery_service.get_today_results(type_code)


@router.get("/results/yesterday", response_model=LotteryResultsResponse)
async def get_yesterday_lottery_results(
    type_code: Optional[str] = Query(None, description="Mã loại xổ số"),
    lottery_service: LotteryService = Depends(get_lottery_service),
) -> LotteryResultsResponse:
    """
    Lấy kết quả xổ số hôm qua
    """
    return await lottery_service.get_yesterday_results(type_code)
