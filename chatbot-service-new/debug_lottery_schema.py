#!/usr/bin/env python3
"""
Debug lottery schema từng dòng một
"""

print("=== DEBUG LOTTERY SCHEMA ===")

# Test 1: Import c<PERSON> bản
try:
    print("1. Testing basic imports...")
    from pydantic import BaseModel, Field
    from typing import Dict, Any, List, Optional
    from datetime import date
    print("✅ Basic imports OK")
except Exception as e:
    print(f"❌ Basic imports failed: {e}")
    exit(1)

# Test 2: Import base schemas
try:
    print("2. Testing base schemas import...")
    from app.schemas.base import BaseSchema
    print("✅ BaseSchema import OK")
except Exception as e:
    print(f"❌ BaseSchema import failed: {e}")
    exit(1)

# Test 3: Import PaginatedResponse
try:
    print("3. Testing PaginatedResponse import...")
    from app.schemas.base import PaginatedResponse
    print("✅ PaginatedResponse import OK")
except Exception as e:
    print(f"❌ PaginatedResponse import failed: {e}")
    exit(1)

# Test 4: Tạo schema đơn giản
try:
    print("4. Testing simple schema creation...")
    
    class TestLotteryType(BaseSchema):
        id: int = Field(..., description="ID")
        code: str = Field(..., description="Code")
        name: str = Field(..., description="Name")
    
    test_instance = TestLotteryType(id=1, code="mb", name="Test")
    print(f"✅ Simple schema OK: {test_instance.name}")
except Exception as e:
    print(f"❌ Simple schema failed: {e}")
    exit(1)

# Test 5: Import từ lottery.py từng dòng
try:
    print("5. Testing lottery.py imports...")
    
    # Import từng class một
    print("  5.1 LotteryTypeBase...")
    from app.schemas.lottery import LotteryTypeBase
    print("  ✅ LotteryTypeBase OK")
    
    print("  5.2 LotteryTypeCreate...")
    from app.schemas.lottery import LotteryTypeCreate
    print("  ✅ LotteryTypeCreate OK")
    
    print("  5.3 LotteryType...")
    from app.schemas.lottery import LotteryType
    print("  ✅ LotteryType OK")
    
except Exception as e:
    print(f"❌ Lottery schema import failed at: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 ALL TESTS PASSED!")
print("Lottery schemas can be imported successfully!")
