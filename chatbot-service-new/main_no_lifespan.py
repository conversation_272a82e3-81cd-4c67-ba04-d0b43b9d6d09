"""
Main.py without lifespan để test RecursionError
"""

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.core.logging import get_logger, setup_logging

# Setup logging
setup_logging()
logger = get_logger(__name__)

def create_app() -> FastAPI:
    """
    Create FastAPI application without lifespan
    """
    app = FastAPI(
        title=settings.app_name,
        version=settings.version,
        description="XỔ SỐ TV Chatbot Service - Test without lifespan",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        # NO LIFESPAN
    )

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "version": settings.version,
            "environment": settings.environment,
        }

    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint"""
        return {
            "message": "XỔ SỐ TV Chatbot Service - No Lifespan",
            "version": settings.version,
        }

    logger.info(
        "FastAPI application created without lifespan",
        debug=settings.debug,
        environment=settings.environment,
    )

    return app

# Create app instance
app = create_app()
