#!/usr/bin/env python3
"""
Test Pydantic v2 implementation
"""

print("=== TESTING PYDANTIC V2 IMPLEMENTATION ===")

# Test 1: Pydantic version
try:
    import pydantic
    print(f"✅ Pydantic version: {pydantic.VERSION}")
    
    if pydantic.VERSION.startswith('2.'):
        print("✅ Pydantic v2 detected")
    else:
        print("❌ Not Pydantic v2")
        exit(1)
        
except Exception as e:
    print(f"❌ Pydantic import failed: {e}")
    exit(1)

# Test 2: Basic Pydantic v2 features
try:
    print("\n2. Testing basic Pydantic v2 features...")
    from pydantic import BaseModel, ConfigDict, Field
    
    class TestModel(BaseModel):
        model_config = ConfigDict(
            from_attributes=True,
            str_strip_whitespace=True,
            extra="forbid"
        )
        
        name: str = Field(..., description="Test name")
        value: int = Field(default=0, ge=0)
    
    test = TestModel(name="  test  ", value=123)
    print(f"✅ Basic model: {test.name} (stripped), {test.value}")
    
except Exception as e:
    print(f"❌ Basic Pydantic v2 failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 3: Config import
try:
    print("\n3. Testing config import...")
    from app.core.config import settings
    print(f"✅ Config imported: {settings.app_name}")
    
except Exception as e:
    print(f"❌ Config import failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 4: Base schemas
try:
    print("\n4. Testing base schemas...")
    from app.schemas.base import BaseSchema
    
    class TestBaseSchema(BaseSchema):
        name: str
        
    test_base = TestBaseSchema(name="test")
    print(f"✅ Base schema: {test_base.name}")
    
except Exception as e:
    print(f"❌ Base schemas failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 5: Lottery schemas - THE CRITICAL TEST
try:
    print("\n5. Testing lottery schemas...")
    
    # Import từng schema một
    from app.schemas.lottery import LotteryTypeBase
    print("  ✅ LotteryTypeBase imported")
    
    from app.schemas.lottery import LotteryType
    print("  ✅ LotteryType imported")
    
    from app.schemas.lottery import LotteryResult
    print("  ✅ LotteryResult imported")
    
    from app.schemas.lottery import LotteryTypesResponse
    print("  ✅ LotteryTypesResponse imported")
    
    # Test instantiation
    lottery_type = LotteryType(
        id=1,
        code="mb",
        name="Miền Bắc",
        region="Bắc",
        description="Test"
    )
    print(f"  ✅ LotteryType instance: {lottery_type.name}")
    
    lottery_result = LotteryResult(
        id=1,
        lottery_type="Miền Bắc",
        lottery_code="mb",
        region="Bắc",
        draw_date="2024-01-01",
        results={"special": "12345"},
        formatted_results="Giải đặc biệt: 12345"
    )
    print(f"  ✅ LotteryResult instance: {lottery_result.lottery_type}")
    
    # Test response schemas
    response = LotteryTypesResponse(types=[lottery_type])
    print(f"  ✅ LotteryTypesResponse: {len(response.types)} types")
    
except Exception as e:
    print(f"❌ Lottery schemas failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Test 6: All schemas together
try:
    print("\n6. Testing all schemas together...")
    from app.schemas.lottery import (
        LotteryType,
        LotteryResult,
        LotteryTypesResponse,
        LotteryResultsResponse,
        LotteryTypesPaginated,
        LotteryResultsPaginated
    )
    print("✅ All lottery schemas imported successfully")
    
except Exception as e:
    print(f"❌ All schemas import failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 ALL PYDANTIC V2 TESTS PASSED!")
print("✅ Pydantic v2 implementation is working correctly!")
print("✅ No RecursionError detected!")
print("✅ Ready for production!")
