# Database Configuration
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/chatbot_db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration  
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=10

# LLM Core Configuration
LLM_CORE_URL=http://localhost:8080
LLM_CORE_TIMEOUT=30

# Application Configuration
APP_NAME="XỔ SỐ TV Chatbot API"
APP_VERSION=2.0.0
DEBUG=false
ENVIRONMENT=production

# Security
SECRET_KEY=your-super-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Cache Configuration
CACHE_TTL_DEFAULT=300
CACHE_TTL_LOTTERY_TYPES=86400
CACHE_TTL_LOTTERY_RESULTS=3600

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
