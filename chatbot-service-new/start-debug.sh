#!/bin/bash

# Debug start script
set -e

echo "🔍 DEBUG: Testing different main files..."

# Test 1: Simple main
echo "1. Testing main_simple.py..."
python -c "
try:
    import main_simple
    print('✅ main_simple.py imports OK')
except RecursionError as e:
    print('❌ RecursionError in main_simple.py')
    print(str(e)[:100])
except Exception as e:
    print(f'⚠️ Other error in main_simple.py: {e}')
"

# Test 2: No lifespan main
echo "2. Testing main_no_lifespan.py..."
python -c "
try:
    import main_no_lifespan
    print('✅ main_no_lifespan.py imports OK')
except RecursionError as e:
    print('❌ RecursionError in main_no_lifespan.py')
    print(str(e)[:100])
except Exception as e:
    print(f'⚠️ Other error in main_no_lifespan.py: {e}')
"

# Test 3: Original main
echo "3. Testing app.main..."
python -c "
try:
    from app.main import app
    print('✅ app.main imports OK')
except RecursionError as e:
    print('❌ RecursionError in app.main')
    print(str(e)[:100])
except Exception as e:
    print(f'⚠️ Other error in app.main: {e}')
"

echo "🔍 DEBUG COMPLETE"
