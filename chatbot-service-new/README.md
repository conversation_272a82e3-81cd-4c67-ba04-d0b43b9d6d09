# XỔ SỐ TV Chatbot Service v2.0

## 🎯 Tổng quan

Chatbot Service được thiết kế lại hoàn toàn với kiến trúc chuyên nghiệ<PERSON>, sử dụng các best practices và công nghệ mới nhất để đảm bảo:

- ✅ **ZERO RecursionError** - Kiến trúc được thiết kế để tránh hoàn toàn circular imports
- ✅ **Clean Architecture** - Tách biệt rõ ràng giữa các layers
- ✅ **Type Safety** - Sử dụng Pydantic v2 và SQLAlchemy 2.0 với strict typing
- ✅ **Performance** - Async/await throughout, connection pooling, caching
- ✅ **Maintainability** - Code dễ đọc, test, và mở rộng

## 🏗️ Kiến trúc

### Nguyên tắc thiết kế để tránh RecursionError:

1. **Dependency Direction**:
   ```
   API → Services → Repositories → Models
   API → Schemas (for validation)
   Services → Schemas (for input/output)
   ```

2. **No Circular Imports**:
   - Models KHÔNG import schemas
   - Schemas KHÔNG import models
   - Services import schemas và repositories
   - API import services và schemas

3. **Layer Separation**:
   ```
   ┌─────────────────┐
   │   API Layer     │ ← FastAPI routes, dependencies
   ├─────────────────┤
   │ Services Layer  │ ← Business logic
   ├─────────────────┤
   │Repository Layer │ ← Data access
   ├─────────────────┤
   │  Models Layer   │ ← SQLAlchemy models
   ├─────────────────┤
   │ Schemas Layer   │ ← Pydantic validation
   └─────────────────┘
   ```

### Cấu trúc thư mục:

```
chatbot-service-new/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application
│   ├── core/                   # Core utilities
│   │   ├── config.py          # Settings với Pydantic
│   │   └── logging.py         # Structured logging
│   ├── db/                    # Database layer
│   │   ├── base.py           # SQLAlchemy base
│   │   └── session.py        # Session management
│   ├── models/               # SQLAlchemy models
│   │   ├── lottery.py
│   │   └── user_query.py
│   ├── schemas/              # Pydantic schemas
│   │   ├── base.py          # Base schemas
│   │   ├── lottery.py       # Lottery schemas
│   │   └── chat.py          # Chat schemas
│   ├── repositories/        # Data access layer
│   │   ├── base.py         # Generic repository
│   │   └── lottery.py      # Lottery repository
│   ├── services/           # Business logic
│   │   └── lottery.py     # Lottery service
│   └── api/               # API routes
│       ├── deps.py       # Dependencies
│       └── v1/          # API v1
│           ├── lottery.py
│           └── router.py
├── tests/                # Test suite
├── requirements.txt      # Dependencies
├── pyproject.toml       # Project config
├── Dockerfile          # Container config
└── README.md          # Documentation
```

## 🚀 Cài đặt và chạy

### 1. Cài đặt dependencies:

```bash
cd chatbot-service-new
pip install -r requirements.txt

# Cho development (optional)
pip install pytest pytest-asyncio black isort mypy
```

### 2. Cấu hình environment:

```bash
cp .env.example .env
# Chỉnh sửa .env theo môi trường của bạn
```

### 3. Test không có RecursionError:

```bash
python test_no_recursion.py
```

### 4. Chạy application:

```bash
# Development (local)
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Production (Docker)
docker-compose up -d chatbot-service
```

### 5. Kiểm tra API:

- Health check: http://localhost:8000/health
- API docs: http://localhost:8000/docs
- API endpoints: http://localhost:8000/api/v1/

## 🧪 Testing

### Chạy test imports:

```bash
python test_no_recursion.py
```

### Chạy test suite:

```bash
pytest tests/ -v
```

### Test coverage:

```bash
pytest --cov=app tests/
```

## 📚 API Documentation

### Lottery Endpoints:

- `GET /api/v1/lottery/types` - Lấy danh sách loại xổ số
- `POST /api/v1/lottery/types` - Tạo loại xổ số mới
- `GET /api/v1/lottery/types/{code}` - Lấy loại xổ số theo mã
- `GET /api/v1/lottery/results` - Lấy kết quả xổ số
- `GET /api/v1/lottery/results/latest` - Kết quả mới nhất
- `GET /api/v1/lottery/results/today` - Kết quả hôm nay
- `GET /api/v1/lottery/results/yesterday` - Kết quả hôm qua

### Example Request:

```bash
curl -X GET "http://localhost:8000/api/v1/lottery/types" \
     -H "accept: application/json"
```

### Example Response:

```json
{
  "types": [
    {
      "id": 1,
      "code": "mb",
      "name": "Miền Bắc",
      "region": "Bắc",
      "description": "Xổ số miền Bắc"
    }
  ]
}
```

## 🔧 Configuration

### Environment Variables:

Service đọc trực tiếp từ environment variables được set bởi Docker Compose:

```bash
# Database connection
POSTGRES_DB=xosotv
POSTGRES_USER=xosotv_user
POSTGRES_PASSWORD=yourstrongpassword
POSTGRES_HOST=timescale
POSTGRES_PORT=5432

# Redis connection
REDIS_HOST=redis
REDIS_PORT=6379

# LLM Core connection
LLM_CORE_HOST=llm-core
LLM_CORE_PORT=8080

# Application settings
VERSION=2.0.0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Start script settings
WORKERS=4                    # Số workers (auto-adjust theo ENVIRONMENT)
WAIT_FOR_DEPS=true          # Có chờ dependencies không
HOST=0.0.0.0                # Host bind
PORT=8000                   # Port bind
```

**Lưu ý**: Service KHÔNG đọc từ file .env mà nhận environment variables trực tiếp từ Docker Compose.

## 🐳 Docker

### Build và chạy:

```bash
# Build
docker build -t chatbot-service:2.0 .

# Run với environment variables
docker run -p 8000:8000 \
  -e POSTGRES_DB=xosotv \
  -e POSTGRES_USER=xosotv_user \
  -e POSTGRES_PASSWORD=yourstrongpassword \
  -e POSTGRES_HOST=timescale \
  -e POSTGRES_PORT=5432 \
  -e REDIS_HOST=redis \
  -e REDIS_PORT=6379 \
  -e LLM_CORE_HOST=llm-core \
  -e LLM_CORE_PORT=8080 \
  -e ENVIRONMENT=production \
  -e WORKERS=4 \
  chatbot-service:2.0
```

### Start Script:

Service sử dụng `start-container.sh` làm ENTRYPOINT với các tính năng:

- ✅ **Dependency checking**: Kiểm tra database và Redis trước khi start
- ✅ **Environment-aware**: Tự động điều chỉnh workers và reload dựa trên ENVIRONMENT
- ✅ **Configurable**: Có thể override workers, host, port qua environment variables
- ✅ **Vietnamese logging**: Tất cả messages và logs bằng tiếng Việt
- ✅ **Graceful startup**: Thông báo chi tiết quá trình khởi động

**Ví dụ output khi khởi động:**
```
🚀 Đang khởi động Chatbot Service v2.0...
Môi trường: production
Chế độ debug: false
Mức log: INFO
Phiên bản: 2.0.0
Cơ sở dữ liệu: timescale:5432/xosotv
Redis: redis:6379
LLM Core: llm-core:8080
⏳ Đang chờ các dịch vụ phụ thuộc...
Kiểm tra kết nối cơ sở dữ liệu...
Kiểm tra kết nối Redis...
✅ Hoàn thành kiểm tra các dịch vụ phụ thuộc
🏭 Chế độ production: 4 workers
🌐 Khởi động server tại 0.0.0.0:8000
📝 Mức log uvicorn: info
```

### Docker Compose:

Service được tích hợp sẵn trong `docker-compose.yml` của project:

```bash
# Start service
docker-compose up -d chatbot-service

# View logs
docker-compose logs -f chatbot-service

# Rebuild
docker-compose build chatbot-service
```

## 🔍 Monitoring và Logging

### Structured Logging:

```python
from app.core.logging import get_logger

logger = get_logger(__name__)
logger.info("Processing request", user_id="123", action="get_results")
```

### Health Check:

```bash
curl http://localhost:8000/health
```

## 🚀 Deployment

### Production Checklist:

- [ ] Set `DEBUG=false`
- [ ] Set `ENVIRONMENT=production`
- [ ] Configure proper `SECRET_KEY`
- [ ] Set up database connection pooling
- [ ] Configure Redis for caching
- [ ] Set up monitoring và logging
- [ ] Configure CORS properly
- [ ] Set up SSL/TLS

## 🤝 Contributing

### Code Style:

```bash
# Format code
black app/ tests/
isort app/ tests/

# Type checking
mypy app/

# Run tests
pytest tests/
```

### Git Workflow:

1. Create feature branch
2. Make changes
3. Run tests và linting
4. Submit pull request

## 📝 Changelog

### v2.0.0 (2024-01-01)

- ✅ Thiết kế lại hoàn toàn để tránh RecursionError
- ✅ Upgrade lên Pydantic v2 và SQLAlchemy 2.0
- ✅ Implement clean architecture với repository pattern
- ✅ Thêm structured logging với structlog
- ✅ Cải thiện type safety và validation
- ✅ Thêm comprehensive test suite
- ✅ Docker support với multi-stage builds
- ✅ Production-ready configuration

## 📞 Support

Nếu gặp vấn đề, vui lòng:

1. Kiểm tra logs: `docker logs chatbot-service`
2. Chạy health check: `curl http://localhost:8000/health`
3. Chạy test imports: `python test_no_recursion.py`
4. Kiểm tra configuration trong `.env`

---

**🎉 Chatbot Service v2.0 - Zero RecursionError, Maximum Performance!**
