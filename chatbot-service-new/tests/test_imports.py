"""
Test imports để đảm b<PERSON><PERSON> không có RecursionError
"""

import pytest


def test_import_schemas():
    """Test import schemas - KHÔNG được có RecursionError"""
    try:
        from app.schemas.lottery import LotteryType, LotteryResult
        from app.schemas.chat import ChatRequest, ChatResponse
        from app.schemas.base import BaseSchema, PaginatedResponse
        
        # Test instantiation
        lottery_type = LotteryType(
            id=1,
            code="mb",
            name="Miền Bắc",
            region="Bắc",
            description="Test"
        )
        
        assert lottery_type.id == 1
        assert lottery_type.code == "mb"
        
    except RecursionError:
        pytest.fail("RecursionError occurred when importing schemas")


def test_import_models():
    """Test import models - KHÔNG được có RecursionError"""
    try:
        from app.models.lottery import LotteryType, LotteryDraw
        from app.models.user_query import UserQuery
        
        # Models should be importable without issues
        assert LotteryType.__tablename__ == "lottery_types"
        assert LotteryDraw.__tablename__ == "lottery_draws"
        
    except RecursionError:
        pytest.fail("RecursionError occurred when importing models")


def test_import_services():
    """Test import services - KHÔNG được có RecursionError"""
    try:
        from app.services.lottery import LotteryService
        
        # Service should be importable
        assert LotteryService is not None
        
    except RecursionError:
        pytest.fail("RecursionError occurred when importing services")


def test_import_repositories():
    """Test import repositories - KHÔNG được có RecursionError"""
    try:
        from app.repositories.lottery import LotteryTypeRepository, LotteryDrawRepository
        from app.repositories.base import BaseRepository
        
        # Repositories should be importable
        assert LotteryTypeRepository is not None
        assert LotteryDrawRepository is not None
        
    except RecursionError:
        pytest.fail("RecursionError occurred when importing repositories")


def test_import_api():
    """Test import API routes - KHÔNG được có RecursionError"""
    try:
        from app.api.v1.lottery import router
        from app.api.v1.router import api_router
        
        # API should be importable
        assert router is not None
        assert api_router is not None
        
    except RecursionError:
        pytest.fail("RecursionError occurred when importing API")


def test_import_main_app():
    """Test import main app - KHÔNG được có RecursionError"""
    try:
        from app.main import app
        
        # App should be importable
        assert app is not None
        assert app.title == "XỔ SỐ TV Chatbot API"
        
    except RecursionError:
        pytest.fail("RecursionError occurred when importing main app")


def test_all_imports_together():
    """Test import tất cả cùng lúc - KHÔNG được có RecursionError"""
    try:
        # Import everything at once
        from app.schemas import lottery, chat, base
        from app.models import lottery as lottery_models, user_query
        from app.services import lottery as lottery_service
        from app.repositories import lottery as lottery_repo, base as base_repo
        from app.api.v1 import lottery as lottery_api, router
        from app.main import app
        from app.core import config, logging
        from app.db import base as db_base, session
        
        # All should be importable without RecursionError
        assert all([
            lottery, chat, base,
            lottery_models, user_query,
            lottery_service,
            lottery_repo, base_repo,
            lottery_api, router,
            app,
            config, logging,
            db_base, session
        ])
        
    except RecursionError:
        pytest.fail("RecursionError occurred when importing all modules together")
