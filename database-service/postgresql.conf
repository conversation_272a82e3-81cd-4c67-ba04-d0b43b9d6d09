# PostgreSQL configuration for TimescaleDB
# Cấu hình PostgreSQL cho TimescaleDB

# Shared preload libraries - Bắt buộc cho TimescaleDB
shared_preload_libraries = 'timescaledb'

# TimescaleDB specific settings
timescaledb.telemetry_level = off

# Background worker settings
max_worker_processes = 8
max_parallel_workers = 4
max_parallel_workers_per_gather = 2

# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# WAL settings
wal_level = replica
max_wal_size = 1GB
min_wal_size = 80MB
checkpoint_completion_target = 0.9

# Connection settings
max_connections = 100
listen_addresses = '*'

# Logging settings
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 10MB
log_min_messages = warning
log_min_error_statement = error
log_min_duration_statement = 1000

# Vietnamese timezone
timezone = 'Asia/Ho_Chi_Minh'
log_timezone = 'Asia/Ho_Chi_Minh'

# Locale settings
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'

# Default text search config
default_text_search_config = 'pg_catalog.english'
