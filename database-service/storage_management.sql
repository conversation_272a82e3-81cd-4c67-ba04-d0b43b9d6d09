-- =====================================================
-- Script quản lý storage cho lưu trữ vĩnh viễn
-- =====================================================

-- Tạo function để kiểm tra kích thước database
CREATE OR REPLACE FUNCTION get_database_size_info()
RETURNS TABLE (
    table_name TEXT,
    size_bytes BIGINT,
    size_mb NUMERIC,
    size_gb NUMERIC,
    row_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        schemaname||'.'||tablename as table_name,
        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes,
        ROUND(pg_total_relation_size(schemaname||'.'||tablename) / 1024.0 / 1024.0, 2) as size_mb,
        ROUND(pg_total_relation_size(schemaname||'.'||tablename) / 1024.0 / 1024.0 / 1024.0, 3) as size_gb,
        (CASE
            WHEN tablename = 'lottery_draws' THEN (SELECT COUNT(*) FROM lottery_draws)
            WHEN tablename = 'lottery_statistics' THEN (SELECT COUNT(*) FROM lottery_statistics)
            WHEN tablename = 'user_queries' THEN (SELECT COUNT(*) FROM user_queries)
            WHEN tablename = 'lottery_types' THEN (SELECT COUNT(*) FROM lottery_types)
            WHEN tablename = 'lottery_regions' THEN (SELECT COUNT(*) FROM lottery_regions)
            WHEN tablename = 'lottery_issuers' THEN (SELECT COUNT(*) FROM lottery_issuers)
            WHEN tablename = 'lottery_draw_channels' THEN (SELECT COUNT(*) FROM lottery_draw_channels)
            WHEN tablename = 'lottery_prizes' THEN (SELECT COUNT(*) FROM lottery_prizes)
            ELSE 0
        END)::BIGINT as row_count
    FROM pg_tables
    WHERE schemaname = 'public'
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
END;
$$ LANGUAGE plpgsql;

-- Tạo function để kiểm tra compression stats
CREATE OR REPLACE FUNCTION get_compression_stats()
RETURNS TABLE (
    hypertable_name TEXT,
    total_chunks INTEGER,
    compressed_chunks INTEGER,
    compression_ratio NUMERIC,
    uncompressed_size_gb NUMERIC,
    compressed_size_gb NUMERIC,
    space_saved_gb NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ht.hypertable_name::TEXT,
        ht.num_chunks::INTEGER as total_chunks,
        COALESCE(comp.compressed_chunks, 0)::INTEGER,
        CASE
            WHEN COALESCE(comp.before_compression_total_bytes, 0) > 0
            THEN ROUND(
                (COALESCE(comp.before_compression_total_bytes, 0) - COALESCE(comp.after_compression_total_bytes, 0))::NUMERIC /
                COALESCE(comp.before_compression_total_bytes, 1) * 100, 2
            )
            ELSE 0
        END as compression_ratio,
        ROUND(COALESCE(comp.before_compression_total_bytes, 0) / 1024.0 / 1024.0 / 1024.0, 3) as uncompressed_size_gb,
        ROUND(COALESCE(comp.after_compression_total_bytes, 0) / 1024.0 / 1024.0 / 1024.0, 3) as compressed_size_gb,
        ROUND((COALESCE(comp.before_compression_total_bytes, 0) - COALESCE(comp.after_compression_total_bytes, 0)) / 1024.0 / 1024.0 / 1024.0, 3) as space_saved_gb
    FROM timescaledb_information.hypertables ht
    LEFT JOIN (
        SELECT
            hypertable_name,
            COUNT(*) as compressed_chunks,
            SUM(COALESCE(before_compression_total_bytes, 0)) as before_compression_total_bytes,
            SUM(COALESCE(after_compression_total_bytes, 0)) as after_compression_total_bytes
        FROM timescaledb_information.chunks
        WHERE compression_status = 'Compressed'
        GROUP BY hypertable_name
    ) comp ON ht.hypertable_name = comp.hypertable_name;
END;
$$ LANGUAGE plpgsql;

-- Tạo function để kiểm tra chunk distribution theo thời gian
CREATE OR REPLACE FUNCTION get_chunk_distribution(table_name TEXT)
RETURNS TABLE (
    chunk_name TEXT,
    range_start TIMESTAMPTZ,
    range_end TIMESTAMPTZ,
    size_mb NUMERIC,
    row_count BIGINT,
    compression_status TEXT
) AS $$
BEGIN
    RETURN QUERY
    EXECUTE format('
        SELECT
            chunk_name::TEXT,
            range_start,
            range_end,
            ROUND(total_bytes / 1024.0 / 1024.0, 2) as size_mb,
            COALESCE(row_count, 0) as row_count,
            COALESCE(compression_status, ''Uncompressed'')::TEXT
        FROM timescaledb_information.chunks
        WHERE hypertable_name = %L
        ORDER BY range_start DESC
    ', table_name);
END;
$$ LANGUAGE plpgsql;

-- Tạo function để tối ưu hóa storage
CREATE OR REPLACE FUNCTION optimize_storage()
RETURNS TEXT AS $$
DECLARE
    result_text TEXT := '';
    rec RECORD;
BEGIN
    result_text := 'Bắt đầu tối ưu hóa storage...' || E'\n';

    -- Vacuum và analyze tất cả các bảng
    result_text := result_text || 'Đang chạy VACUUM ANALYZE...' || E'\n';
    VACUUM ANALYZE;

    -- Reindex các bảng chính
    FOR rec IN
        SELECT tablename
        FROM pg_tables
        WHERE schemaname = 'public'
        AND tablename IN ('lottery_draws', 'lottery_statistics', 'user_queries')
    LOOP
        result_text := result_text || 'Reindexing bảng: ' || rec.tablename || E'\n';
        EXECUTE 'REINDEX TABLE ' || rec.tablename;
    END LOOP;

    -- Kiểm tra và compress các chunk cũ chưa được compress
    result_text := result_text || 'Kiểm tra compression...' || E'\n';

    -- Thống kê cuối cùng
    result_text := result_text || 'Hoàn thành tối ưu hóa storage!' || E'\n';

    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- Tạo function để backup metadata
CREATE OR REPLACE FUNCTION backup_timescaledb_metadata()
RETURNS TEXT AS $$
DECLARE
    backup_info TEXT := '';
BEGIN
    backup_info := 'TimescaleDB Metadata Backup - ' || NOW()::TEXT || E'\n';
    backup_info := backup_info || '=================================' || E'\n\n';

    -- Hypertable information
    backup_info := backup_info || 'HYPERTABLES:' || E'\n';
    backup_info := backup_info || (
        SELECT string_agg(
            'Table: ' || hypertable_name ||
            ', Chunks: ' || num_chunks ||
            ', Compression: ' || CASE WHEN compression_enabled THEN 'Enabled' ELSE 'Disabled' END,
            E'\n'
        )
        FROM timescaledb_information.hypertables
    ) || E'\n\n';

    -- Compression policies
    backup_info := backup_info || 'COMPRESSION POLICIES:' || E'\n';
    backup_info := backup_info || (
        SELECT COALESCE(string_agg(
            'Table: ' || hypertable_name ||
            ', Compress after: ' || compress_after,
            E'\n'
        ), 'No compression policies')
        FROM timescaledb_information.compression_settings
    ) || E'\n\n';

    -- Continuous aggregates
    backup_info := backup_info || 'CONTINUOUS AGGREGATES:' || E'\n';
    backup_info := backup_info || (
        SELECT COALESCE(string_agg(
            'View: ' || view_name ||
            ', Hypertable: ' || hypertable_name,
            E'\n'
        ), 'No continuous aggregates')
        FROM timescaledb_information.continuous_aggregates
    ) || E'\n\n';

    RETURN backup_info;
END;
$$ LANGUAGE plpgsql;

-- Tạo view để monitor storage usage
CREATE OR REPLACE VIEW storage_monitoring AS
SELECT
    'lottery_draws' as table_name,
    pg_size_pretty(pg_total_relation_size('lottery_draws')) as total_size,
    (SELECT COUNT(*) FROM lottery_draws) as row_count,
    (SELECT COUNT(DISTINCT draw_date) FROM lottery_draws) as unique_dates,
    (SELECT MIN(draw_date) FROM lottery_draws) as earliest_date,
    (SELECT MAX(draw_date) FROM lottery_draws) as latest_date
UNION ALL
SELECT
    'lottery_statistics' as table_name,
    pg_size_pretty(pg_total_relation_size('lottery_statistics')) as total_size,
    (SELECT COUNT(*) FROM lottery_statistics) as row_count,
    (SELECT COUNT(DISTINCT draw_date) FROM lottery_statistics) as unique_dates,
    (SELECT MIN(draw_date) FROM lottery_statistics) as earliest_date,
    (SELECT MAX(draw_date) FROM lottery_statistics) as latest_date
UNION ALL
SELECT
    'user_queries' as table_name,
    pg_size_pretty(pg_total_relation_size('user_queries')) as total_size,
    (SELECT COUNT(*) FROM user_queries) as row_count,
    (SELECT COUNT(DISTINCT DATE(created_at)) FROM user_queries) as unique_dates,
    (SELECT MIN(created_at)::DATE FROM user_queries) as earliest_date,
    (SELECT MAX(created_at)::DATE FROM user_queries) as latest_date;

-- Tạo function để tạo báo cáo storage hàng tháng
CREATE OR REPLACE FUNCTION generate_monthly_storage_report()
RETURNS TEXT AS $$
DECLARE
    report TEXT := '';
    total_size_bytes BIGINT;
    total_size_gb NUMERIC;
BEGIN
    -- Header
    report := 'BÁO CÁO STORAGE HÀNG THÁNG - ' || TO_CHAR(NOW(), 'MM/YYYY') || E'\n';
    report := report || '================================================' || E'\n\n';

    -- Tổng kích thước database
    SELECT SUM(pg_total_relation_size(schemaname||'.'||tablename))
    INTO total_size_bytes
    FROM pg_tables WHERE schemaname = 'public';

    total_size_gb := ROUND(total_size_bytes / 1024.0 / 1024.0 / 1024.0, 2);

    report := report || 'TỔNG QUAN:' || E'\n';
    report := report || '- Tổng kích thước database: ' || total_size_gb || ' GB' || E'\n';
    report := report || '- Ngày tạo báo cáo: ' || NOW()::DATE || E'\n\n';

    -- Chi tiết từng bảng
    report := report || 'CHI TIẾT TỪNG BẢNG:' || E'\n';
    report := report || (
        SELECT string_agg(
            '- ' || table_name || ': ' || total_size ||
            ' (' || row_count || ' records, ' ||
            'từ ' || earliest_date || ' đến ' || latest_date || ')',
            E'\n'
        )
        FROM storage_monitoring
    ) || E'\n\n';

    -- Compression stats
    report := report || 'THỐNG KÊ NÉN DỮ LIỆU:' || E'\n';
    report := report || (
        SELECT COALESCE(string_agg(
            '- ' || hypertable_name || ': ' ||
            compressed_chunks || '/' || total_chunks || ' chunks compressed (' ||
            compression_ratio || '% ratio, tiết kiệm ' || space_saved_gb || ' GB)',
            E'\n'
        ), 'Chưa có dữ liệu compression')
        FROM get_compression_stats()
    ) || E'\n\n';

    -- Khuyến nghị
    report := report || 'KHUYẾN NGHỊ:' || E'\n';
    report := report || '- Chạy optimize_storage() định kỳ để tối ưu hiệu suất' || E'\n';
    report := report || '- Monitor compression ratio để đảm bảo tiết kiệm storage' || E'\n';
    report := report || '- Backup metadata định kỳ bằng backup_timescaledb_metadata()' || E'\n';

    RETURN report;
END;
$$ LANGUAGE plpgsql;

-- Ghi log hoàn thành
DO $$
BEGIN
    RAISE NOTICE 'Đã tạo các function quản lý storage cho lưu trữ vĩnh viễn:';
    RAISE NOTICE '- get_database_size_info(): Kiểm tra kích thước bảng';
    RAISE NOTICE '- get_compression_stats(): Thống kê compression';
    RAISE NOTICE '- get_chunk_distribution(table_name): Phân bố chunks';
    RAISE NOTICE '- optimize_storage(): Tối ưu hóa storage';
    RAISE NOTICE '- backup_timescaledb_metadata(): Backup metadata';
    RAISE NOTICE '- generate_monthly_storage_report(): Báo cáo hàng tháng';
    RAISE NOTICE '- storage_monitoring view: Monitor storage usage';
END $$;
