-- =====================================================
-- Thiết lập TimescaleDB cho Hệ thống XỔ SỐ TV
-- =====================================================

-- <PERSON><PERSON>m tra và cấu hình TimescaleDB
DO $$
BEGIN
    -- Kiểm tra TimescaleDB extension
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'timescaledb') THEN
        RAISE NOTICE 'TimescaleDB extension đã sẵn sàng';
    ELSE
        RAISE NOTICE 'Tạo TimescaleDB extension...';
        CREATE EXTENSION timescaledb;
    END IF;
END $$;

-- Cấu hình TimescaleDB
ALTER SYSTEM SET timescaledb.telemetry_level = 'off';
ALTER SYSTEM SET timescaledb.max_background_workers = 8;
SELECT pg_reload_conf();

-- <PERSON><PERSON>ch hoạt các extension hữu ích khác
DO $$
BEGIN
    -- uuid-ossp
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'uuid-ossp') THEN
        CREATE EXTENSION "uuid-ossp";
        RAISE NOTICE 'Extension uuid-ossp đã được tạo';
    END IF;

    -- pgcrypto
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pgcrypto') THEN
        CREATE EXTENSION "pgcrypto";
        RAISE NOTICE 'Extension pgcrypto đã được tạo';
    END IF;

    -- btree_gin
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'btree_gin') THEN
        CREATE EXTENSION "btree_gin";
        RAISE NOTICE 'Extension btree_gin đã được tạo';
    END IF;

    -- btree_gist
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'btree_gist') THEN
        CREATE EXTENSION "btree_gist";
        RAISE NOTICE 'Extension btree_gist đã được tạo';
    END IF;
END $$;

-- =====================================================
-- Chuyển đổi các bảng thành hypertables
-- =====================================================

-- 1. Chuyển đổi lottery_draws thành hypertable (phân vùng theo draw_date)
-- Đây là bảng time-series chính của chúng ta
DO $$
BEGIN
    -- Kiểm tra xem đã là hypertable chưa
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'lottery_draws'
    ) THEN
        PERFORM create_hypertable(
            'lottery_draws',
            'draw_date',
            chunk_time_interval => INTERVAL '1 month'
        );
        RAISE NOTICE 'Đã tạo hypertable lottery_draws';
    ELSE
        RAISE NOTICE 'Hypertable lottery_draws đã tồn tại';
    END IF;
END $$;

-- 2. Chuyển đổi lottery_statistics thành hypertable (phân vùng theo draw_date)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'lottery_statistics'
    ) THEN
        PERFORM create_hypertable(
            'lottery_statistics',
            'draw_date',
            chunk_time_interval => INTERVAL '1 month'
        );
        RAISE NOTICE 'Đã tạo hypertable lottery_statistics';
    ELSE
        RAISE NOTICE 'Hypertable lottery_statistics đã tồn tại';
    END IF;
END $$;

-- 3. Chuyển đổi user_queries thành hypertable (phân vùng theo created_at)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'user_queries'
    ) THEN
        PERFORM create_hypertable(
            'user_queries',
            'created_at',
            chunk_time_interval => INTERVAL '1 week'
        );
        RAISE NOTICE 'Đã tạo hypertable user_queries';
    ELSE
        RAISE NOTICE 'Hypertable user_queries đã tồn tại';
    END IF;
END $$;

-- =====================================================
-- Thiết lập chính sách nén dữ liệu
-- =====================================================

-- Enable compression và tạo compression policies
DO $$
BEGIN
    -- Enable compression cho lottery_draws
    BEGIN
        ALTER TABLE lottery_draws SET (
            timescaledb.compress,
            timescaledb.compress_segmentby = 'lottery_type_id',
            timescaledb.compress_orderby = 'draw_date DESC, id DESC'
        );
        RAISE NOTICE 'Đã enable compression cho lottery_draws';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Compression đã được enable cho lottery_draws hoặc có lỗi: %', SQLERRM;
    END;

    -- Thêm compression policy cho lottery_draws
    BEGIN
        PERFORM add_compression_policy('lottery_draws', INTERVAL '3 months');
        RAISE NOTICE 'Đã thêm compression policy cho lottery_draws (3 tháng)';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Compression policy đã tồn tại cho lottery_draws hoặc có lỗi: %', SQLERRM;
    END;
END $$;

DO $$
BEGIN
    -- Enable compression cho lottery_statistics
    BEGIN
        ALTER TABLE lottery_statistics SET (
            timescaledb.compress,
            timescaledb.compress_segmentby = 'lottery_type_id,analysis_type',
            timescaledb.compress_orderby = 'draw_date DESC, id DESC'
        );
        RAISE NOTICE 'Đã enable compression cho lottery_statistics';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Compression đã được enable cho lottery_statistics hoặc có lỗi: %', SQLERRM;
    END;

    -- Thêm compression policy cho lottery_statistics
    BEGIN
        PERFORM add_compression_policy('lottery_statistics', INTERVAL '1 month');
        RAISE NOTICE 'Đã thêm compression policy cho lottery_statistics (1 tháng)';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Compression policy đã tồn tại cho lottery_statistics hoặc có lỗi: %', SQLERRM;
    END;
END $$;

DO $$
BEGIN
    -- Enable compression cho user_queries
    BEGIN
        ALTER TABLE user_queries SET (
            timescaledb.compress,
            timescaledb.compress_segmentby = 'user_id,intent',
            timescaledb.compress_orderby = 'created_at DESC, id DESC'
        );
        RAISE NOTICE 'Đã enable compression cho user_queries';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Compression đã được enable cho user_queries hoặc có lỗi: %', SQLERRM;
    END;

    -- Thêm compression policy cho user_queries
    BEGIN
        PERFORM add_compression_policy('user_queries', INTERVAL '2 weeks');
        RAISE NOTICE 'Đã thêm compression policy cho user_queries (2 tuần)';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Compression policy đã tồn tại cho user_queries hoặc có lỗi: %', SQLERRM;
    END;
END $$;

-- =====================================================
-- Thiết lập chính sách lưu trữ dữ liệu
-- =====================================================

-- KHÔNG thiết lập retention policy - lưu trữ vĩnh viễn
-- Dữ liệu xổ số sẽ được lưu trữ vĩnh viễn để:
-- - Phân tích xu hướng dài hạn
-- - Nghiên cứu patterns và quy luật
-- - Machine learning và AI training
-- - Tuân thủ yêu cầu pháp lý

-- Lưu ý: Nếu cần thiết lập retention trong tương lai, có thể sử dụng:
-- SELECT add_retention_policy('table_name', INTERVAL 'time_period');

-- Chỉ áp dụng retention cho user_queries để bảo vệ privacy (tùy chọn)
-- SELECT add_retention_policy(
--     'user_queries',
--     INTERVAL '2 years',
--     if_not_exists => TRUE
-- );

-- =====================================================
-- Tạo continuous aggregates cho các truy vấn thường dùng
-- =====================================================

-- Thống kê xổ số theo ngày
CREATE MATERIALIZED VIEW lottery_daily_stats
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 day', draw_date) AS day,
    lottery_type_id,
    COUNT(*) as draws_count,
    COUNT(DISTINCT results) as unique_results
FROM lottery_draws
GROUP BY day, lottery_type_id
WITH NO DATA;

-- Thống kê xổ số theo tuần
CREATE MATERIALIZED VIEW lottery_weekly_stats
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 week', draw_date) AS week,
    lottery_type_id,
    COUNT(*) as draws_count,
    COUNT(DISTINCT results) as unique_results
FROM lottery_draws
GROUP BY week, lottery_type_id
WITH NO DATA;

-- Thống kê xổ số theo tháng
CREATE MATERIALIZED VIEW lottery_monthly_stats
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 month', draw_date) AS month,
    lottery_type_id,
    COUNT(*) as draws_count,
    COUNT(DISTINCT results) as unique_results
FROM lottery_draws
GROUP BY month, lottery_type_id
WITH NO DATA;

-- Thống kê hoạt động người dùng theo ngày
CREATE MATERIALIZED VIEW user_activity_daily
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 day', created_at) AS day,
    intent,
    COUNT(*) as query_count,
    COUNT(DISTINCT user_id) as unique_users
FROM user_queries
GROUP BY day, intent
WITH NO DATA;

-- =====================================================
-- Thiết lập chính sách refresh cho continuous aggregates
-- =====================================================

-- Refresh thống kê hàng ngày mỗi giờ
DO $$
BEGIN
    BEGIN
        PERFORM add_continuous_aggregate_policy(
            'lottery_daily_stats',
            start_offset => INTERVAL '3 days',
            end_offset => INTERVAL '1 hour',
            schedule_interval => INTERVAL '1 hour'
        );
        RAISE NOTICE 'Đã thêm refresh policy cho lottery_daily_stats';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Refresh policy đã tồn tại cho lottery_daily_stats hoặc có lỗi: %', SQLERRM;
    END;
END $$;

-- Refresh thống kê hàng tuần mỗi 6 giờ
DO $$
BEGIN
    BEGIN
        PERFORM add_continuous_aggregate_policy(
            'lottery_weekly_stats',
            start_offset => INTERVAL '3 weeks',
            end_offset => INTERVAL '6 hours',
            schedule_interval => INTERVAL '6 hours'
        );
        RAISE NOTICE 'Đã thêm refresh policy cho lottery_weekly_stats';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Refresh policy đã tồn tại cho lottery_weekly_stats hoặc có lỗi: %', SQLERRM;
    END;
END $$;

-- Refresh thống kê hàng tháng mỗi ngày
DO $$
BEGIN
    BEGIN
        PERFORM add_continuous_aggregate_policy(
            'lottery_monthly_stats',
            start_offset => INTERVAL '3 months',
            end_offset => INTERVAL '1 day',
            schedule_interval => INTERVAL '1 day'
        );
        RAISE NOTICE 'Đã thêm refresh policy cho lottery_monthly_stats';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Refresh policy đã tồn tại cho lottery_monthly_stats hoặc có lỗi: %', SQLERRM;
    END;
END $$;

-- Refresh thống kê hoạt động người dùng mỗi 2 giờ
DO $$
BEGIN
    BEGIN
        PERFORM add_continuous_aggregate_policy(
            'user_activity_daily',
            start_offset => INTERVAL '3 days',
            end_offset => INTERVAL '2 hours',
            schedule_interval => INTERVAL '2 hours'
        );
        RAISE NOTICE 'Đã thêm refresh policy cho user_activity_daily';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Refresh policy đã tồn tại cho user_activity_daily hoặc có lỗi: %', SQLERRM;
    END;
END $$;

-- =====================================================
-- Tạo các index được tối ưu cho TimescaleDB
-- =====================================================

-- Index cho lottery_draws (tối ưu cho time-series)
CREATE INDEX IF NOT EXISTS idx_lottery_draws_lottery_type_time
ON lottery_draws (lottery_type_id, draw_date DESC);

CREATE INDEX IF NOT EXISTS idx_lottery_draws_results_gin
ON lottery_draws USING GIN (results);

-- Index cho lottery_statistics
CREATE INDEX IF NOT EXISTS idx_lottery_statistics_type_time
ON lottery_statistics (lottery_type_id, draw_date DESC);

CREATE INDEX IF NOT EXISTS idx_lottery_statistics_analysis_type
ON lottery_statistics (analysis_type, draw_date DESC);

-- Index cho user_queries
CREATE INDEX IF NOT EXISTS idx_user_queries_user_time
ON user_queries (user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_queries_intent_time
ON user_queries (intent, created_at DESC);

-- =====================================================
-- Chèn dữ liệu mẫu các loại xổ số để test
-- =====================================================

INSERT INTO lottery_types (name, description, format, issuer, region, ticket_price, draw_schedule, status) VALUES
('Xổ số miền Bắc', 'Xổ số truyền thống miền Bắc', 'traditional', 'Công ty XSKT miền Bắc', 'Miền Bắc', 10000, 'Hàng ngày', 'active'),
('Xổ số miền Trung', 'Xổ số truyền thống miền Trung', 'traditional', 'Công ty XSKT miền Trung', 'Miền Trung', 10000, 'Thứ 2, 4, 6', 'active'),
('Xổ số miền Nam', 'Xổ số truyền thống miền Nam', 'traditional', 'Công ty XSKT miền Nam', 'Miền Nam', 10000, 'Hàng ngày', 'active')
ON CONFLICT DO NOTHING;

-- =====================================================
-- Cấp quyền truy cập
-- =====================================================

-- Cấp quyền cho user ứng dụng
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO xosotv_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO xosotv_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO xosotv_user;

-- Cấp quyền đặc biệt cho TimescaleDB
GRANT ALL ON SCHEMA _timescaledb_catalog TO xosotv_user;
GRANT ALL ON SCHEMA _timescaledb_internal TO xosotv_user;
GRANT ALL ON SCHEMA _timescaledb_cache TO xosotv_user;

-- =====================================================
-- Ghi log hoàn thành thiết lập
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=== Thiết lập TimescaleDB hoàn tất thành công ===';
    RAISE NOTICE 'Các hypertable đã được tạo:';
    RAISE NOTICE '- lottery_draws (phân vùng theo draw_date, chunk 1 tháng)';
    RAISE NOTICE '- lottery_statistics (phân vùng theo draw_date, chunk 1 tháng)';
    RAISE NOTICE '- user_queries (phân vùng theo created_at, chunk 1 tuần)';
    RAISE NOTICE '';
    RAISE NOTICE 'Chính sách nén dữ liệu:';
    RAISE NOTICE '- lottery_draws: compression enabled, nén sau 3 tháng';
    RAISE NOTICE '- lottery_statistics: compression enabled, nén sau 1 tháng';
    RAISE NOTICE '- user_queries: compression enabled, nén sau 2 tuần';
    RAISE NOTICE '';
    RAISE NOTICE 'Chính sách lưu trữ:';
    RAISE NOTICE '- lottery_draws: lưu trữ vĩnh viễn';
    RAISE NOTICE '- lottery_statistics: lưu trữ vĩnh viễn';
    RAISE NOTICE '- user_queries: lưu trữ vĩnh viễn (có thể thay đổi sau)';
    RAISE NOTICE '';
    RAISE NOTICE 'Continuous aggregates đã được tạo để tối ưu hiệu suất';
    RAISE NOTICE '=== Hệ thống sẵn sàng cho production! ===';
END $$;
