-- =====================================================
-- Khởi tạo Database cho Hệ thống XỔ SỐ TV
-- =====================================================

-- Tạo các enum types
CREATE TYPE lottery_format_enum AS ENUM ('traditional', 'digital', 'scratch');
CREATE TYPE status_enum AS ENUM ('active', 'inactive', 'suspended');
CREATE TYPE channel_type_enum AS ENUM ('tv', 'online', 'radio');
CREATE TYPE prize_type_enum AS ENUM ('fixed', 'jackpot', 'accumulated');
CREATE TYPE analysis_type_enum AS ENUM ('frequency', 'cycle', 'last_seen', 'hot_cold');
CREATE TYPE intent_enum AS ENUM ('result_query', 'analysis', 'support', 'general');

-- =====================================================
-- Bảng quản lý vùng miền
-- =====================================================
CREATE TABLE lottery_regions (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Bảng quản lý đơn vị phát hành
-- =====================================================
CREATE TABLE lottery_issuers (
    id BIGSERIAL PRIMARY KEY,
    company_name TEXT NOT NULL,
    region_id BIGINT REFERENCES lottery_regions(id),
    province TEXT,
    address TEXT,
    website TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Bảng quản lý kênh quay số
-- =====================================================
CREATE TABLE lottery_draw_channels (
    id BIGSERIAL PRIMARY KEY,
    channel_name TEXT NOT NULL,
    channel_type channel_type_enum NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Bảng quản lý loại xổ số
-- =====================================================
CREATE TABLE lottery_types (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    format lottery_format_enum NOT NULL DEFAULT 'traditional',
    issuer TEXT,
    region TEXT,
    ticket_price INTEGER,
    draw_schedule TEXT,
    status status_enum NOT NULL DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Bảng kết quả xổ số (sẽ được convert thành hypertable)
-- =====================================================
CREATE TABLE lottery_draws (
    id BIGSERIAL,
    lottery_type_id BIGINT NOT NULL REFERENCES lottery_types(id),
    draw_date DATE NOT NULL,
    results JSONB NOT NULL,
    draw_channel_id BIGINT REFERENCES lottery_draw_channels(id),
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    -- Composite primary key bao gồm partitioning column
    PRIMARY KEY (id, draw_date)
);

-- =====================================================
-- Bảng cơ cấu giải thưởng
-- =====================================================
CREATE TABLE lottery_prizes (
    id BIGSERIAL PRIMARY KEY,
    lottery_type_id BIGINT NOT NULL REFERENCES lottery_types(id),
    prize_name TEXT NOT NULL,
    prize_description TEXT,
    prize_value BIGINT,
    prize_type prize_type_enum NOT NULL DEFAULT 'fixed',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Bảng thống kê xổ số (sẽ được convert thành hypertable)
-- =====================================================
CREATE TABLE lottery_statistics (
    id BIGSERIAL,
    lottery_type_id BIGINT NOT NULL REFERENCES lottery_types(id),
    draw_date DATE NOT NULL,
    analysis_type analysis_type_enum NOT NULL,
    data JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    -- Composite primary key bao gồm partitioning column
    PRIMARY KEY (id, draw_date)
);

-- =====================================================
-- Bảng truy vấn người dùng (sẽ được convert thành hypertable)
-- =====================================================
CREATE TABLE user_queries (
    id BIGSERIAL,
    user_id TEXT NOT NULL,
    query_text TEXT NOT NULL,
    intent intent_enum,
    response_summary TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    -- Composite primary key bao gồm partitioning column
    PRIMARY KEY (id, created_at)
);

-- =====================================================
-- Tạo indexes cơ bản
-- =====================================================

-- Indexes cho lottery_draws
CREATE INDEX idx_lottery_draws_type_date ON lottery_draws (lottery_type_id, draw_date);
CREATE INDEX idx_lottery_draws_date ON lottery_draws (draw_date);

-- Indexes cho lottery_statistics
CREATE INDEX idx_lottery_statistics_type_date ON lottery_statistics (lottery_type_id, draw_date);
CREATE INDEX idx_lottery_statistics_analysis_type ON lottery_statistics (analysis_type);

-- Indexes cho user_queries
CREATE INDEX idx_user_queries_user_id ON user_queries (user_id);
CREATE INDEX idx_user_queries_created_at ON user_queries (created_at);
CREATE INDEX idx_user_queries_intent ON user_queries (intent);

-- Indexes cho các bảng khác
CREATE INDEX idx_lottery_issuers_region_id ON lottery_issuers (region_id);
CREATE INDEX idx_lottery_prizes_lottery_type_id ON lottery_prizes (lottery_type_id);
CREATE INDEX idx_lottery_types_status ON lottery_types (status);
CREATE INDEX idx_lottery_types_format ON lottery_types (format);

-- =====================================================
-- Tạo constraints
-- =====================================================

-- Unique constraint cho lottery_draws (đã bao gồm trong composite primary key)
-- Thêm unique constraint cho business logic
ALTER TABLE lottery_draws ADD CONSTRAINT unique_lottery_draw_business
UNIQUE (lottery_type_id, draw_date);

-- Check constraints
ALTER TABLE lottery_types ADD CONSTRAINT check_ticket_price
CHECK (ticket_price > 0);

ALTER TABLE lottery_prizes ADD CONSTRAINT check_prize_value
CHECK (prize_value >= 0);

-- =====================================================
-- Tạo triggers cho updated_at
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Áp dụng trigger cho tất cả bảng có updated_at
CREATE TRIGGER update_lottery_regions_updated_at BEFORE UPDATE ON lottery_regions
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lottery_issuers_updated_at BEFORE UPDATE ON lottery_issuers
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lottery_draw_channels_updated_at BEFORE UPDATE ON lottery_draw_channels
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lottery_types_updated_at BEFORE UPDATE ON lottery_types
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lottery_draws_updated_at BEFORE UPDATE ON lottery_draws
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lottery_prizes_updated_at BEFORE UPDATE ON lottery_prizes
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lottery_statistics_updated_at BEFORE UPDATE ON lottery_statistics
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_queries_updated_at BEFORE UPDATE ON user_queries
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Ghi log hoàn thành
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'Khởi tạo database hoàn tất:';
    RAISE NOTICE '- Đã tạo 8 bảng chính';
    RAISE NOTICE '- Đã tạo 6 enum types';
    RAISE NOTICE '- Đã tạo indexes và constraints';
    RAISE NOTICE '- Đã tạo triggers cho updated_at';
    RAISE NOTICE 'Sẵn sàng cho TimescaleDB setup...';
END $$;
